/**
 * 多模态豆包API集成接口
 * 为现有3D数字人系统提供多模态功能
 */

// 扩展DigitalHuman类，添加多模态功能
DigitalHuman.prototype.initMultiModalAPI = async function() {
    console.log('初始化多模态豆包API...');
    
    try {
        // 初始化多模态系统
        const success = await multiModalAPI.init();
        
        if (success) {
            this.multiModalEnabled = true;
            this.updateStatus('多模态AI就绪');
            
            // 绑定多模态事件处理器
            this.bindMultiModalHandlers();
            
            return true;
        } else {
            this.updateStatus('多模态初始化失败');
            return false;
        }
        
    } catch (error) {
        console.error('多模态API初始化失败:', error);
        this.updateStatus('多模态功能不可用');
        return false;
    }
};

// 绑定多模态事件处理器
DigitalHuman.prototype.bindMultiModalHandlers = function() {
    console.log('绑定多模态事件处理器');
    
    // 增强现有的发送消息功能
    const originalSendMessage = this.sendMessage;
    this.sendMessage = async function() {
        const input = document.getElementById('message-input');
        const message = input.value.trim();
        
        if (!message) return;
        
        // 显示用户消息
        if (this.wsClient) {
            this.wsClient.addMessageToChat('user', message);
        }
        
        // 使用多模态API处理
        if (this.multiModalEnabled) {
            try {
                await multiModalAPI.processTextInput(message);
                this.setExpression('thinking');
            } catch (error) {
                console.error('多模态处理失败，降级到普通模式:', error);
                // 降级到原来的处理方式
                originalSendMessage.call(this);
                return;
            }
        } else {
            // 使用原来的方式
            originalSendMessage.call(this);
        }
        
        // 清空输入框
        input.value = '';
    };
};

// 处理摄像头图像的多模态分析
DigitalHuman.prototype.analyzeCurrentFrame = async function() {
    if (!this.multiModalEnabled || !this.cameraAudio?.videoElement) {
        console.log('多模态分析不可用');
        return;
    }
    
    try {
        // 从视频流捕获当前帧
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const video = this.cameraAudio.videoElement;
        
        canvas.width = video.videoWidth || 640;
        canvas.height = video.videoHeight || 480;
        ctx.drawImage(video, 0, 0);
        
        // 转换为Base64
        const imageData = canvas.toDataURL('image/jpeg', 0.8);
        
        // 发送到多模态API分析
        await multiModalAPI.processImageInput(imageData);
        
        console.log('图像分析请求已发送');
        
    } catch (error) {
        console.error('图像分析失败:', error);
    }
};

// 语音输入的多模态处理
DigitalHuman.prototype.processSpeechInput = async function(transcript) {
    if (!this.multiModalEnabled) {
        console.log('多模态语音处理不可用');
        return;
    }
    
    try {
        // 使用多模态API处理语音输入
        await multiModalAPI.processTextInput(transcript);
        
        // 设置语音交互表情
        this.setExpression('happy');
        
        console.log('语音输入处理完成:', transcript);
        
    } catch (error) {
        console.error('语音多模态处理失败:', error);
    }
};

// 添加多模态控制按钮到界面
function addMultiModalControls() {
    const controls = document.getElementById('controls');
    if (!controls) return;
    
    const multiModalButtons = document.createElement('div');
    multiModalButtons.innerHTML = `
        <div style="margin-top: 8px; padding-top: 8px; border-top: 1px solid rgba(255,255,255,0.3);">
            <button onclick="digitalHuman.analyzeCurrentFrame()" title="分析当前画面">📸</button>
            <button onclick="testMultiModalChat()" title="多模态聊天测试">🤖</button>
            <button onclick="toggleMultiModalMode()" title="切换多模态模式">🔄</button>
        </div>
    `;
    
    controls.appendChild(multiModalButtons);
}

// 测试多模态聊天功能
async function testMultiModalChat() {
    if (!window.digitalHuman?.multiModalEnabled) {
        alert('多模态功能未启用');
        return;
    }
    
    const testPrompts = [
        '你好，测试多模态功能',
        '请介绍一下你的能力',
        '能否分析图像内容？'
    ];
    
    const randomPrompt = testPrompts[Math.floor(Math.random() * testPrompts.length)];
    
    // 模拟用户输入
    const messageInput = document.getElementById('message-input');
    if (messageInput) {
        messageInput.value = randomPrompt;
        digitalHuman.sendMessage();
    }
}

// 切换多模态模式
function toggleMultiModalMode() {
    if (window.digitalHuman?.multiModalEnabled) {
        digitalHuman.multiModalEnabled = false;
        digitalHuman.updateStatus('切换到普通模式');
        console.log('已切换到普通聊天模式');
    } else {
        digitalHuman.initMultiModalAPI().then(success => {
            if (success) {
                console.log('已切换到多模态模式');
            } else {
                alert('多模态模式启动失败');
            }
        });
    }
}

// 配置助手函数
function configureMultiModalAPI() {
    // 为安全起见，不在浏览器中设置或保存第三方 API Key。
    // 推荐做法：由后端管理员在服务端配置 ARK_API_KEY，前端仅通过受控接口调用代理服务。
    // 如果后端提供了受控的管理接口（例如 POST /admin/set_api_key），可在受信任环境中调用。
    if (confirm('为安全起见，请在后端设置 ARK_API_KEY。是否打开帮助文档以了解如何配置？')) {
        window.open('MULTIMODAL_USAGE.md', '_blank');
    }
}

// 在页面加载完成后添加多模态控制
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        addMultiModalControls();
    }, 2000);
});

console.log('多模态集成接口加载完成');