/**
 * 智能模型选择和加载管理系统
 * Linus式设计：用户体验优先，渐进式加载
 * 
 * 加载优先级：
 * 1. 2D图片 (即时显示)
 * 2. GLB模型列表 (后台异步)
 * 3. 用户选择模型 (按需加载)
 */

class ModelSelectionManager {
    constructor() {
        // 可用的数字人模型配置
        this.availableModels = {
            // 2D模型 - 最高优先级
            '2d_default': {
                type: '2d',
                name: '2D数字人',
                description: '即时响应，表情丰富',
                thumbnail: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/...',
                loadTime: '< 1s',
                features: ['表情动画', '手势动作', '即时响应'],
                priority: 1
            },
            
            // 3D模型 - 按需加载
            'glb_model_1': {
                type: '3d',
                name: 'GLB模型1',
                description: '高质量3D渲染',
                path: 'look1_nayong_glb_test.glb',
                thumbnail: null, // 将自动生成
                loadTime: '5-10s',
                features: ['3D渲染', 'BlendShape', '骨骼动画'],
                priority: 2
            },
            
            'glb_model_2': {
                type: '3d',
                name: 'GLB模型2',  
                description: '备选3D模型',
                path: 'look1_nayong_glb_test (1).glb',
                thumbnail: null,
                loadTime: '5-10s',
                features: ['3D渲染', 'BlendShape', '骨骼动画'],
                priority: 3
            },
            
            'glb_extracted': {
                type: '3d',
                name: '解压模型',
                description: '完整材质3D模型',
                path: 'look1-nayong-glb-test/source/look1.glb',
                thumbnail: null,
                loadTime: '10-15s',
                features: ['高质量材质', '完整动画', '最佳效果'],
                priority: 4
            }
        };
        
        // 当前状态
        this.currentModel = null;
        this.loadingModel = null;
        this.modelCache = new Map();
        this.loadingProgress = new Map();
        
        // UI元素
        this.selectionUI = null;
        this.progressIndicator = null;
        
        this.init();
    }
    
    /**
     * 初始化模型选择系统
     */
    async init() {
        console.log('🚀 初始化模型选择管理系统');
        
        // 1. 立即加载2D模型
        await this.loadDefaultModel();
        
        // 2. 创建选择界面
        this.createSelectionUI();
        
        // 3. 后台检测3D模型可用性
        this.detectAvailableModels();
        
        console.log('✅ 模型选择系统就绪');
    }
    
    /**
     * 加载默认2D模型 - 使用企业级加载器
     */
    async loadDefaultModel() {
        console.log('📸 优先加载2D数字人...');
        
        try {
            // 使用企业级加载器
            if (window.enterpriseModelLoader) {
                await window.enterpriseModelLoader.load2DModel();
                this.currentModel = '2d_default';
                console.log('✅ 2D数字人加载完成 (企业级加载器)');
                this.updateStatus('2D数字人就绪 - 选择3D模型以获得更多功能');
                return;
            }
            
            // 备用方案
            const defaultModel = this.availableModels['2d_default'];
            this.update2DImageSource();
            this.switchTo2DMode();
            this.currentModel = '2d_default';
            console.log('✅ 2D数字人加载完成 (备用方案)');
            this.updateStatus('2D数字人就绪 - 选择3D模型以获得更多功能');
            
        } catch (error) {
            console.error('❌ 2D模型加载失败:', error);
        }
    }
    
    /**
     * 更新2D图片源
     */
    update2DImageSource() {
        // 将你提供的图片设置为默认2D数字人
        const imageElement = document.getElementById('digital-human-2d-image');
        if (imageElement) {
            // 使用用户提供的2D数字人图片
            imageElement.src = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAgACADASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD5/ooooA//2Q==';
            imageElement.alt = '企业客服数字人';
        }
    }
    
    /**
     * 切换到2D模式
     */
    switchTo2DMode() {
        const canvas = document.getElementById('avatar-canvas');
        const container = document.getElementById('digital-human-2d-container');
        const toggleBtn = document.querySelector('.model-toggle-btn');
        
        if (canvas) canvas.style.display = 'none';
        if (container) container.style.display = 'flex';
        if (toggleBtn) toggleBtn.textContent = '3D';
        
        // 确保2D动画系统激活
        if (window.integrate2DImageAnimation) {
            integrate2DImageAnimation();
        }
    }
    
    /**
     * 创建模型选择界面
     */
    createSelectionUI() {
        const widget = document.getElementById('digital-human-widget');
        if (!widget) return;
        
        // 创建选择面板
        const selectionPanel = document.createElement('div');
        selectionPanel.className = 'model-selection-panel';
        selectionPanel.style.cssText = `
            position: absolute;
            top: 10px;
            right: 50px;
            background: rgba(0,0,0,0.8);
            border-radius: 8px;
            padding: 12px;
            min-width: 200px;
            z-index: 1001;
            display: none;
            color: white;
            font-size: 12px;
        `;
        
        // 选择按钮
        const selectButton = document.createElement('button');
        selectButton.innerHTML = '📱';
        selectButton.title = '选择数字人模型';
        selectButton.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            border: none;
            padding: 8px;
            border-radius: 4px;
            cursor: pointer;
            z-index: 1002;
            font-size: 16px;
        `;
        
        selectButton.onclick = () => {
            const isVisible = selectionPanel.style.display !== 'none';
            selectionPanel.style.display = isVisible ? 'none' : 'block';
        };
        
        widget.appendChild(selectButton);
        widget.appendChild(selectionPanel);
        
        this.selectionUI = selectionPanel;
        this.updateSelectionUI();
    }
    
    /**
     * 更新选择界面
     */
    updateSelectionUI() {
        if (!this.selectionUI) return;
        
        let html = '<div style="font-weight: bold; margin-bottom: 8px;">选择数字人模型</div>';
        
        Object.entries(this.availableModels).forEach(([id, model]) => {
            const isActive = this.currentModel === id;
            const isLoading = this.loadingModel === id;
            const progress = this.loadingProgress.get(id) || 0;
            
            let status = '';
            if (isActive) status = '✅ 当前';
            else if (isLoading) status = `⏳ ${progress}%`;
            else if (model.available === false) status = '❌ 不可用';
            else if (model.type === '2d') status = '⚡ 即时';
            else status = '📦 可选';
            
            html += `
                <div class="model-option" style="
                    padding: 8px;
                    margin: 4px 0;
                    border-radius: 4px;
                    background: ${isActive ? 'rgba(0,123,255,0.3)' : 'rgba(255,255,255,0.1)'};
                    cursor: ${isLoading ? 'wait' : 'pointer'};
                    border: 1px solid ${isActive ? '#007bff' : 'transparent'};
                " onclick="modelManager.selectModel('${id}')">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <div style="font-weight: bold;">${model.name}</div>
                            <div style="font-size: 10px; color: #ccc;">${model.description}</div>
                            <div style="font-size: 10px; color: #999;">加载时间: ${model.loadTime}</div>
                        </div>
                        <div style="text-align: right; font-size: 10px;">
                            ${status}
                        </div>
                    </div>
                    ${isLoading ? `
                        <div style="width: 100%; height: 2px; background: #333; margin-top: 4px; border-radius: 1px;">
                            <div style="width: ${progress}%; height: 100%; background: #007bff; border-radius: 1px;"></div>
                        </div>
                    ` : ''}
                    <div style="font-size: 10px; color: #aaa; margin-top: 4px;">
                        ${model.features.join(' · ')}
                    </div>
                </div>
            `;
        });
        
        this.selectionUI.innerHTML = html;
    }
    
    /**
     * 后台检测可用模型
     */
    async detectAvailableModels() {
        console.log('🔍 后台检测3D模型可用性...');
        
        const detectionPromises = Object.entries(this.availableModels)
            .filter(([id, model]) => model.type === '3d')
            .map(([id, model]) => this.checkModelAvailability(id, model));
        
        await Promise.all(detectionPromises);
        
        console.log('🔍 模型可用性检测完成');
        this.updateSelectionUI();
    }
    
    /**
     * 检查单个模型可用性
     */
    async checkModelAvailability(modelId, model) {
        try {
            const response = await fetch(model.path, { method: 'HEAD' });
            
            if (response.ok) {
                const fileSize = response.headers.get('content-length');
                model.available = true;
                model.fileSize = fileSize ? parseInt(fileSize) : null;
                
                console.log(`✅ ${model.name} 可用 (${this.formatFileSize(model.fileSize)})`);
            } else {
                model.available = false;
                console.warn(`❌ ${model.name} 不可用 (${response.status})`);
            }
        } catch (error) {
            model.available = false;
            console.warn(`❌ ${model.name} 检测失败:`, error.message);
        }
    }
    
    /**
     * 选择模型
     */
    async selectModel(modelId) {
        const model = this.availableModels[modelId];
        if (!model) {
            console.error('模型不存在:', modelId);
            return;
        }
        
        if (this.currentModel === modelId) {
            console.log('已经是当前模型:', model.name);
            return;
        }
        
        if (this.loadingModel === modelId) {
            console.log('模型正在加载中:', model.name);
            return;
        }
        
        console.log('🔄 切换到模型:', model.name);
        
        if (model.type === '2d') {
            this.switchTo2DMode();
            this.currentModel = modelId;
            this.updateSelectionUI();
            this.updateStatus(`已切换到: ${model.name}`);
        } else if (model.type === '3d') {
            await this.load3DModel(modelId, model);
        }
    }
    
    /**
     * 加载3D模型 - 使用企业级加载器
     */
    async load3DModel(modelId, model) {
        if (!model.available) {
            this.updateStatus(`❌ ${model.name} 不可用`);
            return;
        }
        
        this.loadingModel = modelId;
        this.loadingProgress.set(modelId, 0);
        this.updateSelectionUI();
        this.updateStatus(`⏳ 正在加载 ${model.name}...`);
        
        try {
            // 优先使用企业级加载器
            if (window.enterpriseModelLoader) {
                console.log('🎮 使用企业级加载器加载3D模型');
                const gltf = await window.enterpriseModelLoader.load3DModel(model.path);
                await this.apply3DModel(modelId, gltf);
                return;
            }
            
            // 备用方案：直接使用GLTFLoader
            if (!window.GLTFLoader) {
                throw new Error('GLTFLoader未加载');
            }
            
            // 如果已缓存，直接使用
            if (this.modelCache.has(modelId)) {
                console.log('📦 使用缓存模型:', model.name);
                await this.apply3DModel(modelId, this.modelCache.get(modelId));
                return;
            }
            
            const loader = new window.GLTFLoader();
            
            const gltf = await new Promise((resolve, reject) => {
                loader.load(
                    model.path,
                    (gltf) => resolve(gltf),
                    (progress) => {
                        const percent = Math.round((progress.loaded / progress.total) * 100);
                        this.loadingProgress.set(modelId, percent);
                        this.updateSelectionUI();
                        this.updateStatus(`⏳ 加载 ${model.name}: ${percent}%`);
                    },
                    (error) => reject(error)
                );
            });
            
            // 缓存模型
            this.modelCache.set(modelId, gltf);
            
            // 应用模型
            await this.apply3DModel(modelId, gltf);
            
        } catch (error) {
            console.error(`❌ ${model.name} 加载失败:`, error);
            this.updateStatus(`❌ ${model.name} 加载失败: ${error.message}`);
        } finally {
            this.loadingModel = null;
            this.loadingProgress.delete(modelId);
            this.updateSelectionUI();
        }
    }
    
    /**
     * 应用3D模型
     */
    async apply3DModel(modelId, gltf) {
        const model = this.availableModels[modelId];
        
        console.log(`✅ 应用3D模型: ${model.name}`);
        
        // 切换到3D模式
        this.switchTo3DMode();
        
        // 更新数字人系统的模型
        if (window.digitalHuman) {
            // 移除旧模型
            if (digitalHuman.avatar) {
                digitalHuman.scene.remove(digitalHuman.avatar);
            }
            
            // 应用新模型
            digitalHuman.avatar = gltf.scene;
            digitalHuman.avatar.scale.set(1, 1, 1);
            digitalHuman.avatar.position.set(0, 0, 0);
            
            // 重新设置材质和功能
            digitalHuman.setupModelMaterials();
            digitalHuman.setupBlendShapes();
            digitalHuman.setupAnimations(gltf);
            
            digitalHuman.scene.add(digitalHuman.avatar);
            
            console.log(`🎭 ${model.name} 应用完成`);
        }
        
        this.currentModel = modelId;
        this.updateStatus(`✅ ${model.name} 已就绪`);
    }
    
    /**
     * 切换到3D模式
     */
    switchTo3DMode() {
        const canvas = document.getElementById('avatar-canvas');
        const container = document.getElementById('digital-human-2d-container');
        const toggleBtn = document.querySelector('.model-toggle-btn');
        
        if (canvas) canvas.style.display = 'block';
        if (container) container.style.display = 'none';
        if (toggleBtn) toggleBtn.textContent = '2D';
    }
    
    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (!bytes) return '未知';
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }
    
    /**
     * 更新状态显示
     */
    updateStatus(message) {
        const statusEl = document.getElementById('status');
        if (statusEl) {
            statusEl.textContent = message;
        }
        console.log('📊', message);
    }
    
    /**
     * 获取当前模型信息
     */
    getCurrentModelInfo() {
        if (!this.currentModel) return null;
        return this.availableModels[this.currentModel];
    }
    
    /**
     * 清理缓存
     */
    clearCache() {
        this.modelCache.clear();
        console.log('🗑️ 模型缓存已清理');
    }
    
    /**
     * 预加载推荐模型
     */
    async preloadRecommendedModels() {
        // 在空闲时预加载第一个可用的3D模型
        const firstAvailable3D = Object.entries(this.availableModels)
            .find(([id, model]) => model.type === '3d' && model.available);
        
        if (firstAvailable3D && !this.modelCache.has(firstAvailable3D[0])) {
            console.log('🎯 预加载推荐模型:', firstAvailable3D[1].name);
            // 后台静默加载
            this.load3DModel(firstAvailable3D[0], firstAvailable3D[1]);
        }
    }
}

// 创建全局模型管理实例
const modelManager = new ModelSelectionManager();

// 集成到现有系统
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保其他系统已就绪
    setTimeout(() => {
        console.log('🎮 模型选择管理系统启动');
    }, 1000);
});