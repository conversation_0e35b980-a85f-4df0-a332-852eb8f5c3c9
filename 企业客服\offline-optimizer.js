/**
 * 离线模式优化器
 * Linus设计哲学：失败时优雅降级，不要崩溃
 */

class OfflineModeOptimizer {
    constructor() {
        this.isOnline = navigator.onLine;
        this.offlineFeatures = {
            '2d_animations': true,
            '3d_basic_rendering': true,
            'local_chat': true,
            'voice_detection': false, // 离线时禁用语音检测
            'api_calls': false,
            'websocket': false
        };
        
        this.init();
    }
    
    init() {
        console.log('🔌 初始化离线模式优化器');
        
        // 监听网络状态变化
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.handleOnlineStateChange();
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.handleOnlineStateChange();
        });
        
        // 初始状态检查
        this.handleOnlineStateChange();
    }
    
    /**
     * 处理网络状态变化
     */
    handleOnlineStateChange() {
        if (this.isOnline) {
            console.log('🌐 网络已连接 - 启用完整功能');
            this.enableOnlineFeatures();
        } else {
            console.log('🔌 离线模式 - 启用基础功能');
            this.enableOfflineMode();
        }
        
        this.updateUI();
    }
    
    /**
     * 启用在线功能
     */
    enableOnlineFeatures() {
        // 启用语音检测
        if (window.cameraAudio) {
            cameraAudio.enableVoiceDetection = true;
        }
        
        // 启用API调用
        this.offlineFeatures.api_calls = true;
        this.offlineFeatures.websocket = true;
        this.offlineFeatures.voice_detection = true;
        
        // 重新尝试WebSocket连接
        if (window.digitalHuman && digitalHuman.initWebSocket) {
            setTimeout(() => {
                digitalHuman.initWebSocket();
            }, 2000);
        }
    }
    
    /**
     * 启用离线模式
     */
    enableOfflineMode() {
        console.log('🔌 启用离线模式');
        
        // 禁用语音检测以减少日志
        if (window.cameraAudio) {
            cameraAudio.enableVoiceDetection = false;
        }
        
        // 禁用API调用
        this.offlineFeatures.api_calls = false;
        this.offlineFeatures.websocket = false;
        this.offlineFeatures.voice_detection = false;
        
        // 停止WebSocket重连尝试
        if (window.digitalHuman && window.digitalHuman.wsClient) {
            window.digitalHuman.wsClient.stopReconnecting = true;
        }
        
        // 显示离线模式提示
        this.showOfflineNotification();
        
        // 更新2D容器的提示文本
        this.update2DContainerForOfflineMode();
    }
    
    /**
     * 更新2D容器的离线模式提示
     */
    update2DContainerForOfflineMode() {
        const container = document.getElementById('digital-human-2d-container');
        if (container) {
            const textInfo = container.querySelector('.digital-human-info');
            if (textInfo) {
                textInfo.innerHTML = `
                    <div style="margin-bottom: 8px;">🤖 AI客服助手</div>
                    <div style="font-size: 14px; opacity: 0.8;">离线模式 - 基础功能可用</div>
                    <div style="font-size: 12px; opacity: 0.6; margin-top: 8px;">
                        ✅ 2D动画 ✅ 本地交互<br>
                        ❌ 在线AI聊天暂时不可用<br>
                        <span style="color: #ffeb3b;">网络恢复后自动启用完整功能</span>
                    </div>
                `;
            }
        }
    }
    
    /**
     * 更新UI状态
     */
    updateUI() {
        const status = document.getElementById('status');
        if (status) {
            if (this.isOnline) {
                status.textContent = '🌐 系统就绪 - 在线模式 - 所有功能可用';
                status.style.color = '#4caf50';
            } else {
                status.textContent = '🔌 离线模式 - 2D数字人可用 - 等待网络连接';
                status.style.color = '#ff9800';
            }
        }
        
        // 更新模型选择面板状态
        if (window.modernUI) {
            modernUI.updateModelOptions();
        }
        
        // 更新2D容器中的在线状态提示
        const container = document.getElementById('digital-human-2d-container');
        if (container) {
            const textInfo = container.querySelector('.digital-human-info');
            if (textInfo && this.isOnline) {
                textInfo.innerHTML = `
                    <div style="margin-bottom: 8px;">🤖 AI客服助手</div>
                    <div style="font-size: 14px; opacity: 0.8;">2D数字人已就绪 - 在线模式</div>
                    <div style="font-size: 12px; opacity: 0.6; margin-top: 8px;">
                        ✅ 2D动画 ✅ AI聊天 ✅ 语音交互<br>
                        <span style="color: #4caf50;">所有功能已启用</span>
                    </div>
                `;
            }
        }
    }
    
    /**
     * 显示离线通知
     */
    showOfflineNotification() {
        if (window.modernUI) {
            modernUI.showNotification('🔌 离线模式：API功能已禁用，本地功能正常', 'info');
        }
    }
    
    /**
     * 检查功能是否可用
     */
    isFeatureEnabled(feature) {
        if (!this.isOnline) {
            return this.offlineFeatures[feature] || false;
        }
        return true;
    }
    
    /**
     * 安全的API调用包装器
     */
    async safeApiCall(apiFunction, fallbackMessage = '离线模式下无法使用此功能') {
        if (!this.isFeatureEnabled('api_calls')) {
            console.warn('API调用已禁用:', fallbackMessage);
            return { error: fallbackMessage, offline: true };
        }
        
        try {
            return await apiFunction();
        } catch (error) {
            console.error('API调用失败:', error);
            return { error: error.message, offline: false };
        }
    }
    
    /**
     * 优化控制台日志
     */
    optimizeLogging() {
        // 在离线模式下减少日志输出
        if (!this.isOnline) {
            const originalLog = console.log;
            const originalWarn = console.warn;
            
            console.log = function(...args) {
                // 过滤掉重复的网络错误日志
                const message = args.join(' ');
                if (message.includes('WebSocket') || 
                    message.includes('CORS') || 
                    message.includes('语音开始') || 
                    message.includes('语音结束')) {
                    return; // 不输出这些日志
                }
                originalLog.apply(console, args);
            };
            
            console.warn = function(...args) {
                const message = args.join(' ');
                if (message.includes('connection') || message.includes('fetch')) {
                    return; // 不输出网络相关警告
                }
                originalWarn.apply(console, args);
            };
        }
    }
    
    /**
     * 创建离线模式聊天功能
     */
    createOfflineChat() {
        const offlineResponses = [
            '您好！我是企业数字人客服，目前处于离线模式。',
            '抱歉，当前无法连接到服务器，但我仍能为您演示基础功能。',
            '离线模式下，您可以体验表情动画和手势控制。',
            '请检查网络连接，以获得完整的AI对话功能。',
            '感谢您的理解，网络恢复后我将提供更完整的服务。'
        ];
        
        return {
            respond: (message) => {
                const randomResponse = offlineResponses[Math.floor(Math.random() * offlineResponses.length)];
                return Promise.resolve(randomResponse);
            }
        };
    }
}

// 创建全局离线优化器
window.offlineOptimizer = new OfflineModeOptimizer();

// 扩展数字人系统
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        if (window.digitalHuman) {
            digitalHuman.offlineOptimizer = window.offlineOptimizer;
            
            // 重写sendMessage以支持离线模式
            const originalSendMessage = digitalHuman.sendMessage;
            digitalHuman.sendMessage = function() {
                const message = window.modernUI ? window.modernUI.getInputValue() : '';
                if (!message) return;
                
                if (window.modernUI) {
                    window.modernUI.addMessage(message, true);
                    window.modernUI.clearInput();
                    window.modernUI.setLoading(true);
                }
                
                if (window.offlineOptimizer.isOnline && originalSendMessage) {
                    // 在线模式使用原始方法
                    originalSendMessage.call(this);
                } else {
                    // 离线模式使用本地响应
                    const offlineChat = window.offlineOptimizer.createOfflineChat();
                    offlineChat.respond(message).then(response => {
                        if (window.modernUI) {
                            window.modernUI.addMessage(response, false);
                            window.modernUI.setLoading(false);
                        }
                    });
                }
            };
            
            console.log('🔌 离线模式优化器已集成到数字人系统');
        }
    }, 2000);
});

console.log('🔌 离线模式优化器加载完成');