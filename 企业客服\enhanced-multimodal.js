/**
 * 增强多模态交互系统
 * 集成语音输入、图片上传、文件分享、屏幕共享等现代功能
 */

class EnhancedMultimodal {
    constructor(chatInterface) {
        this.chatInterface = chatInterface;
        this.mediaRecorder = null;
        this.audioChunks = [];
        this.isRecording = false;
        this.screenStream = null;
        
        // 多模态配置
        this.config = {
            voice: {
                enabled: true,
                language: 'zh-CN',
                continuous: false,
                interimResults: true,
                maxRecordingTime: 60000 // 60秒
            },
            camera: {
                enabled: true,
                facingMode: 'user',
                width: { ideal: 1280 },
                height: { ideal: 720 }
            },
            screen: {
                enabled: true,
                audio: true,
                video: true
            },
            files: {
                enabled: true,
                maxSize: 50 * 1024 * 1024, // 50MB
                allowedTypes: [
                    'image/*',
                    'audio/*',
                    'video/*',
                    'application/pdf',
                    'application/msword',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'text/*'
                ]
            }
        };
        
        this.init();
    }
    
    /**
     * 初始化多模态系统
     */
    async init() {
        await this.setupVoiceRecognition();
        await this.setupAudioRecording();
        await this.setupCameraAccess();
        await this.setupScreenSharing();
        this.setupFileHandling();
        this.setupGestureRecognition();
        this.enhanceChatInterface();
        
        console.log('✅ 增强多模态系统初始化完成');
    }
    
    /**
     * 设置语音识别
     */
    async setupVoiceRecognition() {
        if (!this.config.voice.enabled) return;
        
        // 检查浏览器支持
        if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
            console.warn('浏览器不支持语音识别');
            return;
        }
        
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        this.speechRecognition = new SpeechRecognition();
        
        // 配置语音识别
        this.speechRecognition.continuous = this.config.voice.continuous;
        this.speechRecognition.interimResults = this.config.voice.interimResults;
        this.speechRecognition.lang = this.config.voice.language;
        
        // 事件处理
        this.speechRecognition.onstart = () => {
            this.onVoiceRecognitionStart();
        };
        
        this.speechRecognition.onresult = (event) => {
            this.onVoiceRecognitionResult(event);
        };
        
        this.speechRecognition.onend = () => {
            this.onVoiceRecognitionEnd();
        };
        
        this.speechRecognition.onerror = (event) => {
            this.onVoiceRecognitionError(event);
        };
    }
    
    /**
     * 设置音频录制
     */
    async setupAudioRecording() {
        try {
            this.audioStream = await navigator.mediaDevices.getUserMedia({ 
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                }
            });
            
            this.mediaRecorder = new MediaRecorder(this.audioStream, {
                mimeType: 'audio/webm;codecs=opus'
            });
            
            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.audioChunks.push(event.data);
                }
            };
            
            this.mediaRecorder.onstop = () => {
                this.processAudioRecording();
            };
            
        } catch (error) {
            console.warn('音频录制初始化失败:', error);
        }
    }
    
    /**
     * 设置摄像头访问
     */
    async setupCameraAccess() {
        if (!this.config.camera.enabled) return;
        
        try {
            this.cameraStream = await navigator.mediaDevices.getUserMedia({
                video: {
                    facingMode: this.config.camera.facingMode,
                    width: this.config.camera.width,
                    height: this.config.camera.height
                }
            });
            
            console.log('✅ 摄像头访问已授权');
        } catch (error) {
            console.warn('摄像头访问失败:', error);
        }
    }
    
    /**
     * 设置屏幕共享
     */
    async setupScreenSharing() {
        if (!this.config.screen.enabled) return;
        
        // 检查浏览器支持
        if (!navigator.mediaDevices.getDisplayMedia) {
            console.warn('浏览器不支持屏幕共享');
            return;
        }
        
        console.log('✅ 屏幕共享功能已准备');
    }
    
    /**
     * 设置文件处理
     */
    setupFileHandling() {
        // 增强文件拖拽功能
        this.setupAdvancedDragDrop();
        
        // 设置粘贴板监听
        this.setupClipboardHandling();
        
        // 设置文件预览
        this.setupFilePreview();
    }
    
    /**
     * 设置手势识别
     */
    setupGestureRecognition() {
        // 使用MediaPipe或类似库进行手势识别
        if (window.Hands) {
            this.initHandTracking();
        }
    }
    
    /**
     * 增强聊天界面
     */
    enhanceChatInterface() {
        // 添加多模态控制面板
        this.addMultimodalControls();
        
        // 添加实时预览区域
        this.addPreviewArea();
        
        // 添加状态指示器
        this.addStatusIndicators();
    }
    
    /**
     * 添加多模态控制面板
     */
    addMultimodalControls() {
        const controlsHtml = `
            <div class="multimodal-controls">
                <button class="control-btn" id="voice-record-btn" title="录音">
                    🎙️
                </button>
                <button class="control-btn" id="camera-capture-btn" title="拍照">
                    📷
                </button>
                <button class="control-btn" id="screen-share-btn" title="屏幕共享">
                    🖥️
                </button>
                <button class="control-btn" id="location-btn" title="位置">
                    📍
                </button>
                <button class="control-btn" id="drawing-btn" title="绘图">
                    ✏️
                </button>
            </div>
        `;
        
        const inputContainer = this.chatInterface.container.querySelector('.modern-input-container');
        inputContainer.insertAdjacentHTML('afterbegin', controlsHtml);
        
        // 绑定事件
        this.bindMultimodalEvents();
    }
    
    /**
     * 绑定多模态事件
     */
    bindMultimodalEvents() {
        // 录音按钮
        document.getElementById('voice-record-btn').addEventListener('click', () => {
            this.toggleAudioRecording();
        });
        
        // 拍照按钮
        document.getElementById('camera-capture-btn').addEventListener('click', () => {
            this.capturePhoto();
        });
        
        // 屏幕共享按钮
        document.getElementById('screen-share-btn').addEventListener('click', () => {
            this.toggleScreenShare();
        });
        
        // 位置按钮
        document.getElementById('location-btn').addEventListener('click', () => {
            this.shareLocation();
        });
        
        // 绘图按钮
        document.getElementById('drawing-btn').addEventListener('click', () => {
            this.openDrawingBoard();
        });
    }
    
    /**
     * 切换音频录制
     */
    toggleAudioRecording() {
        if (this.isRecording) {
            this.stopAudioRecording();
        } else {
            this.startAudioRecording();
        }
    }
    
    /**
     * 开始音频录制
     */
    startAudioRecording() {
        if (!this.mediaRecorder) {
            alert('音频录制功能不可用');
            return;
        }
        
        this.audioChunks = [];
        this.mediaRecorder.start();
        this.isRecording = true;
        
        // 更新UI
        const btn = document.getElementById('voice-record-btn');
        btn.innerHTML = '⏹️';
        btn.style.background = 'var(--error-color)';
        btn.style.color = 'white';
        
        // 设置最大录制时间
        this.recordingTimeout = setTimeout(() => {
            this.stopAudioRecording();
        }, this.config.voice.maxRecordingTime);
        
        // 显示录制状态
        this.showRecordingStatus();
    }
    
    /**
     * 停止音频录制
     */
    stopAudioRecording() {
        if (!this.isRecording) return;
        
        this.mediaRecorder.stop();
        this.isRecording = false;
        
        // 清除超时
        if (this.recordingTimeout) {
            clearTimeout(this.recordingTimeout);
        }
        
        // 恢复UI
        const btn = document.getElementById('voice-record-btn');
        btn.innerHTML = '🎙️';
        btn.style.background = '';
        btn.style.color = '';
        
        // 隐藏录制状态
        this.hideRecordingStatus();
    }
    
    /**
     * 处理音频录制
     */
    processAudioRecording() {
        const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' });
        
        // 创建音频URL
        const audioUrl = URL.createObjectURL(audioBlob);
        
        // 添加音频消息
        this.chatInterface.addMessage({
            type: 'user',
            content: this.createAudioMessage(audioUrl, audioBlob.size),
            timestamp: new Date(),
            isAudio: true
        });
        
        // 发送音频进行转录
        this.transcribeAudio(audioBlob);
    }
    
    /**
     * 创建音频消息
     */
    createAudioMessage(audioUrl, size) {
        const duration = '未知';
        const fileSize = this.formatFileSize(size);
        
        return `
            <div class="audio-message">
                <div class="audio-player">
                    <audio controls>
                        <source src="${audioUrl}" type="audio/webm">
                        您的浏览器不支持音频播放。
                    </audio>
                </div>
                <div class="audio-info">
                    <span class="audio-duration">🎵 语音消息</span>
                    <span class="audio-size">${fileSize}</span>
                </div>
            </div>
        `;
    }
    
    /**
     * 转录音频
     */
    async transcribeAudio(audioBlob) {
        try {
            // 显示转录状态
            this.chatInterface.showTypingIndicator();
            
            // 调用语音转文字API
            const transcript = await this.callSpeechToTextAPI(audioBlob);
            
            this.chatInterface.hideTypingIndicator();
            
            if (transcript) {
                // 添加转录结果
                this.chatInterface.addMessage({
                    type: 'ai',
                    content: `🎤 语音转录：${transcript}`,
                    timestamp: new Date()
                });
                
                // 自动发送转录文本进行AI处理
                const aiResponse = await this.chatInterface.callAIAPI(transcript);
                this.chatInterface.addMessage({
                    type: 'ai',
                    content: aiResponse,
                    timestamp: new Date()
                });
            }
            
        } catch (error) {
            console.error('语音转录失败:', error);
            this.chatInterface.hideTypingIndicator();
        }
    }
    
    /**
     * 调用语音转文字API
     */
    async callSpeechToTextAPI(audioBlob) {
        // 这里可以集成各种语音识别服务
        // 如：百度语音、腾讯云、阿里云等
        
        // 模拟API调用
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve('这是模拟的语音转录结果');
            }, 2000);
        });
    }
    
    /**
     * 拍照功能
     */
    async capturePhoto() {
        if (!this.cameraStream) {
            try {
                this.cameraStream = await navigator.mediaDevices.getUserMedia({
                    video: this.config.camera
                });
            } catch (error) {
                alert('无法访问摄像头');
                return;
            }
        }
        
        // 创建临时视频元素
        const video = document.createElement('video');
        video.srcObject = this.cameraStream;
        video.play();
        
        video.onloadedmetadata = () => {
            // 创建canvas进行截图
            const canvas = document.createElement('canvas');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            
            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0);
            
            // 转换为图片
            canvas.toBlob((blob) => {
                const imageUrl = URL.createObjectURL(blob);
                
                // 添加图片消息
                this.chatInterface.addMessage({
                    type: 'user',
                    content: `<img src="${imageUrl}" style="max-width: 100%; border-radius: 8px;" alt="拍摄的照片">`,
                    timestamp: new Date(),
                    isImage: true
                });
                
                // 分析图片
                this.analyzeImage(imageUrl);
                
            }, 'image/jpeg', 0.8);
            
            // 停止摄像头
            this.cameraStream.getTracks().forEach(track => track.stop());
            this.cameraStream = null;
        };
    }
    
    /**
     * 屏幕共享
     */
    async toggleScreenShare() {
        if (this.screenStream) {
            this.stopScreenShare();
        } else {
            await this.startScreenShare();
        }
    }
    
    /**
     * 开始屏幕共享
     */
    async startScreenShare() {
        try {
            this.screenStream = await navigator.mediaDevices.getDisplayMedia({
                video: true,
                audio: this.config.screen.audio
            });
            
            // 创建屏幕截图
            this.captureScreenshot();
            
            // 更新按钮状态
            const btn = document.getElementById('screen-share-btn');
            btn.innerHTML = '⏹️';
            btn.style.background = 'var(--success-color)';
            btn.style.color = 'white';
            
        } catch (error) {
            console.error('屏幕共享失败:', error);
            alert('屏幕共享失败');
        }
    }
    
    /**
     * 停止屏幕共享
     */
    stopScreenShare() {
        if (this.screenStream) {
            this.screenStream.getTracks().forEach(track => track.stop());
            this.screenStream = null;
            
            // 恢复按钮状态
            const btn = document.getElementById('screen-share-btn');
            btn.innerHTML = '🖥️';
            btn.style.background = '';
            btn.style.color = '';
        }
    }
    
    /**
     * 截取屏幕截图
     */
    captureScreenshot() {
        const video = document.createElement('video');
        video.srcObject = this.screenStream;
        video.play();
        
        video.onloadedmetadata = () => {
            const canvas = document.createElement('canvas');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            
            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0);
            
            canvas.toBlob((blob) => {
                const imageUrl = URL.createObjectURL(blob);
                
                this.chatInterface.addMessage({
                    type: 'user',
                    content: `<img src="${imageUrl}" style="max-width: 100%; border-radius: 8px;" alt="屏幕截图">`,
                    timestamp: new Date(),
                    isScreenshot: true
                });
                
            }, 'image/png');
        };
    }
    
    /**
     * 分享位置
     */
    shareLocation() {
        if (!navigator.geolocation) {
            alert('您的浏览器不支持地理定位');
            return;
        }
        
        navigator.geolocation.getCurrentPosition(
            (position) => {
                const { latitude, longitude } = position.coords;
                
                this.chatInterface.addMessage({
                    type: 'user',
                    content: this.createLocationMessage(latitude, longitude),
                    timestamp: new Date(),
                    isLocation: true
                });
            },
            (error) => {
                console.error('获取位置失败:', error);
                alert('获取位置失败');
            }
        );
    }
    
    /**
     * 创建位置消息
     */
    createLocationMessage(lat, lng) {
        return `
            <div class="location-message">
                <div class="location-icon">📍</div>
                <div class="location-info">
                    <div class="location-title">我的位置</div>
                    <div class="location-coords">
                        纬度: ${lat.toFixed(6)}<br>
                        经度: ${lng.toFixed(6)}
                    </div>
                    <a href="https://maps.google.com/?q=${lat},${lng}" target="_blank" class="location-link">
                        在地图中查看
                    </a>
                </div>
            </div>
        `;
    }
    
    /**
     * 打开绘图板
     */
    openDrawingBoard() {
        // 创建绘图模态框
        const drawingModal = this.createDrawingModal();
        document.body.appendChild(drawingModal);
    }
    
    /**
     * 创建绘图模态框
     */
    createDrawingModal() {
        const modal = document.createElement('div');
        modal.className = 'drawing-modal';
        modal.innerHTML = `
            <div class="drawing-container">
                <div class="drawing-header">
                    <h3>绘图板</h3>
                    <button class="close-btn">×</button>
                </div>
                <canvas class="drawing-canvas" width="600" height="400"></canvas>
                <div class="drawing-tools">
                    <button class="tool-btn active" data-tool="pen">✏️</button>
                    <button class="tool-btn" data-tool="eraser">🧹</button>
                    <button class="tool-btn" data-tool="clear">🗑️</button>
                    <input type="color" class="color-picker" value="#000000">
                    <input type="range" class="size-slider" min="1" max="20" value="3">
                    <button class="send-drawing-btn">发送</button>
                </div>
            </div>
        `;
        
        // 初始化绘图功能
        this.initDrawingBoard(modal);
        
        return modal;
    }
    
    /**
     * 工具函数
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    showRecordingStatus() {
        // 显示录制状态指示器
        const status = document.createElement('div');
        status.className = 'recording-status';
        status.innerHTML = '🔴 正在录音...';
        status.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--error-color);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            z-index: 1000;
            animation: pulse 1s infinite;
        `;
        document.body.appendChild(status);
        this.recordingStatusElement = status;
    }
    
    hideRecordingStatus() {
        if (this.recordingStatusElement) {
            this.recordingStatusElement.remove();
            this.recordingStatusElement = null;
        }
    }
}

// 导出类
window.EnhancedMultimodal = EnhancedMultimodal;
