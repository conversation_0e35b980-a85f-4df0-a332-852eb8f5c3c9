/**
 * 企业级数字人渲染管线
 * Linus式设计：质量优先，性能平衡，零妥协
 * 
 * 核心组件：
 * - PBR材质系统（物理正确渲染）
 * - IBL环境光照（基于图像的光照）
 * - 后处理特效管线
 * - 自适应质量系统
 * - 企业级性能监控
 */

// 等待Three.js加载完成再初始化
window.addEventListener('three-loaded', async () => {
    console.log('🔧 Three.js已加载，初始化企业级渲染管线');
    
    // 动态导入后处理模块
    const { EffectComposer } = await import('https://unpkg.com/three@0.160.0/examples/jsm/postprocessing/EffectComposer.js');
    const { RenderPass } = await import('https://unpkg.com/three@0.160.0/examples/jsm/postprocessing/RenderPass.js');
    const { SSAOPass } = await import('https://unpkg.com/three@0.160.0/examples/jsm/postprocessing/SSAOPass.js');
    const { UnrealBloomPass } = await import('https://unpkg.com/three@0.160.0/examples/jsm/postprocessing/UnrealBloomPass.js');
    const { TAARenderPass } = await import('https://unpkg.com/three@0.160.0/examples/jsm/postprocessing/TAARenderPass.js');
    const { OutputPass } = await import('https://unpkg.com/three@0.160.0/examples/jsm/postprocessing/OutputPass.js');

class EnterpriseDigitalHuman {
    constructor() {
        // 核心数据结构 - 质量优先
        this.renderPipeline = {
            pbr: null,           // PBR材质系统
            ibl: null,           // 基于图像的光照
            postProcess: null,   // 后处理特效
            lod: null,           // 细节层次管理
            animation: null,     // 高级动画系统
            performance: null    // 性能监控
        };
        
        // 渲染质量配置
        this.qualityConfig = {
            renderScale: 1.0,        // 渲染分辨率倍数
            shadowMapSize: 2048,     // 阴影贴图分辨率
            antialias: true,         // 抗锯齿
            enableSSAO: true,        // 环境光遮蔽
            enableBloom: true,       // 辉光效果
            enableTAA: true,         // 时间抗锯齿
            maxLights: 8,            // 最大光源数
            environmentIntensity: 1.0 // 环境光强度
        };
        
        // 性能监控
        this.performanceStats = {
            fps: 60,
            frameTime: 16.67,
            drawCalls: 0,
            triangles: 0,
            gpuMemory: 0
        };
        
        // 核心Three.js对象
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.composer = null;
        
        // 存储导入的类
        this.EffectComposer = EffectComposer;
        this.RenderPass = RenderPass;
        this.SSAOPass = SSAOPass;
        this.UnrealBloomPass = UnrealBloomPass;
        this.TAARenderPass = TAARenderPass;
        this.OutputPass = OutputPass;
        
        console.log('🎯 企业级数字人渲染管线初始化');
    }
    
    /**
     * 初始化企业级渲染管线
     */
    async init() {
        try {
            console.log('🚀 启动企业级渲染管线');
            
            // 1. 初始化基础渲染
            this.initRenderer();
            
            // 2. 初始化PBR管线
            this.initPBRPipeline();
            
            // 3. 设置IBL环境光照
            await this.setupImageBasedLighting();
            
            // 4. 配置后处理管线
            this.setupPostProcessing();
            
            // 5. 初始化性能监控
            this.initPerformanceMonitoring();
            
            // 6. 自适应质量系统
            this.initAdaptiveQuality();
            
            console.log('✅ 企业级渲染管线就绪');
            return true;
            
        } catch (error) {
            console.error('❌ 企业级渲染管线初始化失败:', error);
            return false;
        }
    }
    
    /**
     * 初始化高质量渲染器
     */
    initRenderer() {
        const canvas = document.getElementById('avatar-canvas');
        const rect = canvas.getBoundingClientRect();
        
        // 企业级渲染器配置
        this.renderer = new THREE.WebGLRenderer({
            canvas: canvas,
            antialias: this.qualityConfig.antialias,
            alpha: true,
            premultipliedAlpha: false,
            stencil: false,
            powerPreference: "high-performance"
        });
        
        // 基础渲染设置
        this.renderer.setSize(
            rect.width * this.qualityConfig.renderScale, 
            rect.height * this.qualityConfig.renderScale
        );
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        
        // 启用现代WebGL扩展
        this.renderer.capabilities.isWebGL2 = true;
        
        console.log('🎮 高质量渲染器初始化完成');
    }
    
    /**
     * 初始化PBR管线 - 物理正确渲染
     */
    initPBRPipeline() {
        // PBR材质 - 真实物理渲染
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.0;
        this.renderer.outputColorSpace = THREE.SRGBColorSpace;
        
        // 启用物理正确光照（Three.js r155后已废弃，但保持兼容）
        if (this.renderer.physicallyCorrectLights !== undefined) {
            this.renderer.physicallyCorrectLights = true;
        }
        
        // 高质量阴影
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.shadowMap.autoUpdate = true;
        
        // 创建场景
        this.scene = new THREE.Scene();
        
        // 创建专业摄像机
        this.setupProfessionalCamera();
        
        console.log('🎨 PBR渲染管线就绪');
    }
    
    /**
     * 设置专业摄像机
     */
    setupProfessionalCamera() {
        const canvas = document.getElementById('avatar-canvas');
        const rect = canvas.getBoundingClientRect();
        
        // 企业级摄像机参数
        this.camera = new THREE.PerspectiveCamera(
            45,                           // FOV - 更自然的视角
            rect.width / rect.height,     // 宽高比
            0.1,                          // 近裁剪面
            100                           // 远裁剪面
        );
        
        // 专业人像摄影位置
        this.camera.position.set(0, 1.6, 1.8);  // 轻微俯视角度
        this.camera.lookAt(0, 1.5, 0);           // 聚焦在脸部
        
        console.log('📷 专业摄像机配置完成');
    }
    
    /**
     * 设置基于图像的光照 - 真实环境反射
     */
    async setupImageBasedLighting() {
        try {
            console.log('🌅 加载IBL环境...');
            
            // 创建程序化HDR环境（作为fallback）
            const hdrEnvironment = this.createProceduralHDR();
            
            // 生成环境贴图
            const pmremGenerator = new THREE.PMREMGenerator(this.renderer);
            pmremGenerator.compileEquirectangularShader();
            
            this.envMap = pmremGenerator.fromScene(hdrEnvironment);
            
            // 应用到场景
            this.scene.environment = this.envMap.texture;
            this.scene.background = this.envMap.texture;
            
            // 清理
            pmremGenerator.dispose();
            hdrEnvironment.dispose();
            
            // 设置专业级照明
            this.setupProfessionalLighting();
            
            console.log('✨ IBL环境光照就绪');
            
        } catch (error) {
            console.error('⚠️ IBL加载失败，使用备用照明:', error);
            this.setupFallbackLighting();
        }
    }
    
    /**
     * 创建程序化HDR环境
     */
    createProceduralHDR() {
        const geometry = new THREE.SphereGeometry(50, 32, 16);
        
        // 创建渐变材质模拟天空
        const vertexShader = `
            varying vec3 vWorldPosition;
            void main() {
                vec4 worldPosition = modelMatrix * vec4(position, 1.0);
                vWorldPosition = worldPosition.xyz;
                gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
        `;
        
        const fragmentShader = `
            varying vec3 vWorldPosition;
            void main() {
                vec3 direction = normalize(vWorldPosition);
                float elevation = direction.y;
                
                // 天空渐变
                vec3 skyTop = vec3(0.3, 0.6, 1.0);     // 天蓝
                vec3 skyHorizon = vec3(0.8, 0.9, 1.0); // 浅蓝
                vec3 skyBottom = vec3(0.6, 0.8, 0.9);  // 偏白
                
                vec3 skyColor = mix(skyBottom, skyHorizon, smoothstep(-0.2, 0.2, elevation));
                skyColor = mix(skyColor, skyTop, smoothstep(0.2, 0.8, elevation));
                
                // 添加太阳
                vec3 sunDirection = normalize(vec3(0.3, 0.8, 0.5));
                float sunDot = max(dot(direction, sunDirection), 0.0);
                vec3 sunColor = vec3(3.0, 2.5, 2.0) * pow(sunDot, 256.0);
                
                gl_FragColor = vec4(skyColor + sunColor, 1.0);
            }
        `;
        
        const material = new THREE.ShaderMaterial({
            vertexShader,
            fragmentShader,
            side: THREE.BackSide
        });
        
        const environment = new THREE.Scene();
        environment.add(new THREE.Mesh(geometry, material));
        
        return environment;
    }
    
    /**
     * 设置专业级照明 - 三点照明法
     */
    setupProfessionalLighting() {
        // 主光 - Key Light
        this.keyLight = new THREE.DirectionalLight(0xffffff, 2.5);
        this.keyLight.position.set(2, 4, 3);
        this.keyLight.target.position.set(0, 1.5, 0);
        this.keyLight.castShadow = true;
        
        // 高质量阴影配置
        this.keyLight.shadow.mapSize.setScalar(this.qualityConfig.shadowMapSize);
        this.keyLight.shadow.camera.near = 0.1;
        this.keyLight.shadow.camera.far = 20;
        this.keyLight.shadow.camera.left = -3;
        this.keyLight.shadow.camera.right = 3;
        this.keyLight.shadow.camera.top = 3;
        this.keyLight.shadow.camera.bottom = -3;
        this.keyLight.shadow.bias = -0.0001;
        this.keyLight.shadow.normalBias = 0.01;
        
        this.scene.add(this.keyLight);
        this.scene.add(this.keyLight.target);
        
        // 补光 - Fill Light
        this.fillLight = new THREE.DirectionalLight(0xfff8e7, 1.2);
        this.fillLight.position.set(-2, 2, 2);
        this.fillLight.target.position.set(0, 1.5, 0);
        this.scene.add(this.fillLight);
        this.scene.add(this.fillLight.target);
        
        // 轮廓光 - Rim Light
        this.rimLight = new THREE.DirectionalLight(0x8ec5ff, 0.8);
        this.rimLight.position.set(0, 3, -2);
        this.rimLight.target.position.set(0, 1.5, 0);
        this.scene.add(this.rimLight);
        this.scene.add(this.rimLight.target);
        
        // 环境光增强
        this.ambientLight = new THREE.AmbientLight(0x404040, 0.4);
        this.scene.add(this.ambientLight);
        
        console.log('💡 专业级照明系统就绪');
    }
    
    /**
     * 备用照明系统
     */
    setupFallbackLighting() {
        // 简化但高质量的照明
        const hemiLight = new THREE.HemisphereLight(0xffffff, 0x444444, 0.8);
        this.scene.add(hemiLight);
        
        const dirLight = new THREE.DirectionalLight(0xffffff, 1.0);
        dirLight.position.set(1, 3, 2);
        dirLight.castShadow = true;
        dirLight.shadow.mapSize.setScalar(1024);
        this.scene.add(dirLight);
        
        console.log('🔆 备用照明系统激活');
    }
    
    /**
     * 设置后处理管线 - 电影级效果
     */
    setupPostProcessing() {
        // 创建后处理合成器
        this.composer = new this.EffectComposer(this.renderer);
        
        // 基础渲染通道
        const renderPass = new this.RenderPass(this.scene, this.camera);
        this.composer.addPass(renderPass);
        
        // SSAO - 环境光遮蔽
        if (this.qualityConfig.enableSSAO) {
            const ssaoPass = new this.SSAOPass(this.scene, this.camera, 
                this.renderer.getDrawingBufferSize(new THREE.Vector2()).width,
                this.renderer.getDrawingBufferSize(new THREE.Vector2()).height
            );
            ssaoPass.kernelRadius = 0.1;
            ssaoPass.minDistance = 0.005;
            ssaoPass.maxDistance = 0.1;
            ssaoPass.output = this.SSAOPass.OUTPUT.Default;
            this.composer.addPass(ssaoPass);
        }
        
        // TAA - 时间抗锯齿
        if (this.qualityConfig.enableTAA) {
            const taaPass = new this.TAARenderPass(this.scene, this.camera);
            this.composer.addPass(taaPass);
        }
        
        // Bloom - 辉光效果
        if (this.qualityConfig.enableBloom) {
            const bloomPass = new this.UnrealBloomPass(
                new THREE.Vector2(window.innerWidth, window.innerHeight),
                0.1,    // strength
                0.4,    // radius
                0.85    // threshold
            );
            this.composer.addPass(bloomPass);
        }
        
        // 输出通道 - 色调映射
        const outputPass = new this.OutputPass();
        this.composer.addPass(outputPass);
        
        console.log('🎬 后处理管线配置完成');
    }
    
    /**
     * 初始化性能监控
     */
    initPerformanceMonitoring() {
        this.performanceMonitor = {
            frameCount: 0,
            lastTime: performance.now(),
            
            update: () => {
                this.performanceMonitor.frameCount++;
                const now = performance.now();
                
                if (now - this.performanceMonitor.lastTime >= 1000) {
                    this.performanceStats.fps = this.performanceMonitor.frameCount;
                    this.performanceStats.frameTime = 1000 / this.performanceStats.fps;
                    
                    // 获取渲染统计
                    const info = this.renderer.info;
                    this.performanceStats.drawCalls = info.render.calls;
                    this.performanceStats.triangles = info.render.triangles;
                    
                    this.performanceMonitor.frameCount = 0;
                    this.performanceMonitor.lastTime = now;
                    
                    // 输出性能报告
                    if (this.performanceStats.fps < 30) {
                        console.warn('⚠️ 性能警告: FPS =', this.performanceStats.fps);
                    }
                }
            }
        };
        
        console.log('📊 性能监控系统启动');
    }
    
    /**
     * 自适应质量系统
     */
    initAdaptiveQuality() {
        this.adaptiveQuality = {
            targetFPS: 60,
            qualityLevel: 'high',
            
            adjust: () => {
                if (this.performanceStats.fps < 45 && this.adaptiveQuality.qualityLevel === 'high') {
                    this.downgradeQuality();
                } else if (this.performanceStats.fps > 55 && this.adaptiveQuality.qualityLevel === 'medium') {
                    this.upgradeQuality();
                }
            }
        };
        
        console.log('🎛️ 自适应质量系统就绪');
    }
    
    /**
     * 降级渲染质量
     */
    downgradeQuality() {
        console.log('📉 降级渲染质量以保持性能');
        
        this.qualityConfig.renderScale = 0.8;
        this.qualityConfig.shadowMapSize = 1024;
        this.qualityConfig.enableSSAO = false;
        
        this.adaptiveQuality.qualityLevel = 'medium';
        this.updateQualitySettings();
    }
    
    /**
     * 升级渲染质量
     */
    upgradeQuality() {
        console.log('📈 升级渲染质量');
        
        this.qualityConfig.renderScale = 1.0;
        this.qualityConfig.shadowMapSize = 2048;
        this.qualityConfig.enableSSAO = true;
        
        this.adaptiveQuality.qualityLevel = 'high';
        this.updateQualitySettings();
    }
    
    /**
     * 应用质量设置
     */
    updateQualitySettings() {
        const canvas = document.getElementById('avatar-canvas');
        const rect = canvas.getBoundingClientRect();
        
        // 更新渲染分辨率
        this.renderer.setSize(
            rect.width * this.qualityConfig.renderScale,
            rect.height * this.qualityConfig.renderScale
        );
        
        // 更新合成器大小
        if (this.composer) {
            this.composer.setSize(
                rect.width * this.qualityConfig.renderScale,
                rect.height * this.qualityConfig.renderScale
            );
        }
    }
    
    /**
     * 渲染循环 - 企业级性能
     */
    render() {
        // 性能监控
        this.performanceMonitor.update();
        
        // 自适应质量调整
        this.adaptiveQuality.adjust();
        
        // 执行渲染
        if (this.composer) {
            this.composer.render();
        } else {
            this.renderer.render(this.scene, this.camera);
        }
    }
    
    /**
     * 资源清理
     */
    dispose() {
        if (this.composer) {
            this.composer.dispose();
        }
        
        if (this.envMap) {
            this.envMap.dispose();
        }
        
        console.log('🧹 企业级渲染管线资源已清理');
    }
}

// 全局导出
window.EnterpriseDigitalHuman = EnterpriseDigitalHuman;

console.log('🏢 企业级数字人渲染管线模块加载完成');

}); // 结束 three-loaded 事件监听器