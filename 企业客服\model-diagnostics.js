/**
 * 3D模型诊断工具
 * 用于检测模型加载、Blend<PERSON>hape、动画等功能
 */

class ModelDiagnostics {
    constructor() {
        this.diagnosticResults = {};
    }
    
    /**
     * 运行完整诊断
     */
    async runFullDiagnostics() {
        console.log('🔍 开始3D模型完整诊断...');
        
        const results = {
            timestamp: new Date().toISOString(),
            browser: this.getBrowserInfo(),
            webgl: this.checkWebGLSupport(),
            modelLoading: await this.checkModelLoading(),
            blendShapes: null,
            animations: null,
            rendering: null
        };
        
        if (results.modelLoading.success) {
            results.blendShapes = this.checkBlendShapes();
            results.animations = this.checkAnimations();
            results.rendering = this.checkRendering();
        }
        
        this.diagnosticResults = results;
        this.displayResults(results);
        
        return results;
    }
    
    /**
     * 获取浏览器信息
     */
    getBrowserInfo() {
        return {
            userAgent: navigator.userAgent,
            webgl: !!window.WebGLRenderingContext,
            webgl2: !!window.WebGL2RenderingContext
        };
    }
    
    /**
     * 检查WebGL支持
     */
    checkWebGLSupport() {
        const canvas = document.createElement('canvas');
        const contexts = ['webgl2', 'webgl', 'experimental-webgl'];
        
        for (const contextName of contexts) {
            try {
                const ctx = canvas.getContext(contextName);
                if (ctx) {
                    return {
                        supported: true,
                        context: contextName,
                        renderer: ctx.getParameter(ctx.RENDERER),
                        vendor: ctx.getParameter(ctx.VENDOR)
                    };
                }
            } catch (e) {
                continue;
            }
        }
        
        return {
            supported: false,
            error: 'WebGL不支持'
        };
    }
    
    /**
     * 检查模型加载
     */
    async checkModelLoading() {
        console.log('📦 检查模型加载...');
        
        const modelPaths = [
            'look1_nayong_glb_test.glb',
            'look1_nayong_glb_test (1).glb',
            'look1-nayong-glb-test/source/look1.glb'
        ];
        
        const results = {
            success: false,
            loadedPath: null,
            attempts: [],
            error: null
        };
        
        for (const path of modelPaths) {
            try {
                const result = await this.tryLoadSingleModel(path);
                results.attempts.push({
                    path: path,
                    success: result.success,
                    error: result.error,
                    fileSize: result.fileSize
                });
                
                if (result.success) {
                    results.success = true;
                    results.loadedPath = path;
                    break;
                }
            } catch (error) {
                results.attempts.push({
                    path: path,
                    success: false,
                    error: error.message
                });
            }
        }
        
        if (!results.success) {
            results.error = '所有模型路径都加载失败';
        }
        
        return results;
    }
    
    /**
     * 尝试加载单个模型
     */
    async tryLoadSingleModel(path) {
        return new Promise((resolve) => {
            // 首先检查文件是否存在
            fetch(path, { method: 'HEAD' })
                .then(response => {
                    if (!response.ok) {
                        resolve({
                            success: false,
                            error: `文件不存在 (${response.status})`,
                            fileSize: null
                        });
                        return;
                    }
                    
                    const fileSize = response.headers.get('content-length');
                    
                    // 然后尝试用GLTFLoader加载
                    if (!window.GLTFLoader) {
                        resolve({
                            success: false,
                            error: 'GLTFLoader未加载',
                            fileSize: fileSize ? parseInt(fileSize) : null
                        });
                        return;
                    }
                    
                    const loader = new window.GLTFLoader();
                    loader.load(
                        path,
                        (gltf) => {
                            resolve({
                                success: true,
                                error: null,
                                fileSize: fileSize ? parseInt(fileSize) : null,
                                sceneChildren: gltf.scene.children.length,
                                animationsCount: gltf.animations ? gltf.animations.length : 0
                            });
                        },
                        undefined,
                        (error) => {
                            resolve({
                                success: false,
                                error: error.message,
                                fileSize: fileSize ? parseInt(fileSize) : null
                            });
                        }
                    );
                })
                .catch(error => {
                    resolve({
                        success: false,
                        error: `网络错误: ${error.message}`,
                        fileSize: null
                    });
                });
        });
    }
    
    /**
     * 检查BlendShape功能
     */
    checkBlendShapes() {
        console.log('😊 检查BlendShape功能...');
        
        if (!window.digitalHuman || !digitalHuman.avatar) {
            return {
                available: false,
                error: '数字人或模型未加载'
            };
        }
        
        const results = {
            available: false,
            morphTargetsFound: 0,
            meshesWithMorphTargets: [],
            blendShapeNames: [],
            fallbackSystem: !!digitalHuman.fallbackExpressionController
        };
        
        // 检查morphTargets
        if (digitalHuman.morphTargets && Object.keys(digitalHuman.morphTargets).length > 0) {
            results.available = true;
            results.morphTargetsFound = Object.keys(digitalHuman.morphTargets).length;
            
            Object.entries(digitalHuman.morphTargets).forEach(([meshName, mesh]) => {
                const morphInfo = {
                    meshName: meshName,
                    morphTargetCount: mesh.morphTargetInfluences ? mesh.morphTargetInfluences.length : 0,
                    morphTargetNames: mesh.morphTargetDictionary ? Object.keys(mesh.morphTargetDictionary) : []
                };
                
                results.meshesWithMorphTargets.push(morphInfo);
                results.blendShapeNames.push(...morphInfo.morphTargetNames);
            });
            
            // 去重
            results.blendShapeNames = [...new Set(results.blendShapeNames)];
        }
        
        return results;
    }
    
    /**
     * 检查动画功能
     */
    checkAnimations() {
        console.log('🎬 检查动画功能...');
        
        if (!window.digitalHuman || !digitalHuman.avatar) {
            return {
                available: false,
                error: '数字人或模型未加载'
            };
        }
        
        const results = {
            builtInAnimations: {
                available: !!digitalHuman.mixer,
                count: Object.keys(digitalHuman.animations || {}).length,
                names: Object.keys(digitalHuman.animations || {})
            },
            proceduralAnimations: {
                available: !!digitalHuman.proceduralAnimations,
                count: Object.keys(digitalHuman.proceduralAnimations || {}).length,
                names: Object.keys(digitalHuman.proceduralAnimations || {})
            },
            fallbackAnimations: true // 总是可用的简单动画
        };
        
        return results;
    }
    
    /**
     * 检查渲染功能
     */
    checkRendering() {
        console.log('🎨 检查渲染功能...');
        
        if (!window.digitalHuman) {
            return {
                available: false,
                error: '数字人系统未初始化'
            };
        }
        
        const results = {
            scene: !!digitalHuman.scene,
            camera: !!digitalHuman.camera,
            renderer: !!digitalHuman.renderer,
            avatar: !!digitalHuman.avatar,
            renderLoop: true // 假设渲染循环在运行
        };
        
        if (digitalHuman.renderer) {
            const context = digitalHuman.renderer.getContext();
            results.renderingContext = {
                contextType: context.constructor.name,
                drawingBufferWidth: context.drawingBufferWidth,
                drawingBufferHeight: context.drawingBufferHeight
            };
        }
        
        return results;
    }
    
    /**
     * 显示诊断结果
     */
    displayResults(results) {
        console.log('📋 诊断结果报告:');
        console.log('================');
        
        // 浏览器支持
        console.log('🌐 浏览器支持:');
        console.log(`  WebGL: ${results.webgl.supported ? '✅' : '❌'}`);
        if (results.webgl.supported) {
            console.log(`  渲染器: ${results.webgl.renderer}`);
            console.log(`  厂商: ${results.webgl.vendor}`);
        }
        
        // 模型加载
        console.log('\n📦 模型加载:');
        console.log(`  成功: ${results.modelLoading.success ? '✅' : '❌'}`);
        if (results.modelLoading.success) {
            console.log(`  加载路径: ${results.modelLoading.loadedPath}`);
        } else {
            console.log(`  错误: ${results.modelLoading.error}`);
        }
        
        results.modelLoading.attempts.forEach((attempt, index) => {
            console.log(`  尝试 ${index + 1}: ${attempt.path} - ${attempt.success ? '✅' : '❌'} ${attempt.error || ''}`);
        });
        
        // BlendShape
        if (results.blendShapes) {
            console.log('\n😊 BlendShape功能:');
            console.log(`  可用: ${results.blendShapes.available ? '✅' : '❌'}`);
            console.log(`  Morph目标数量: ${results.blendShapes.morphTargetsFound || 0}`);
            console.log(`  备用系统: ${results.blendShapes.fallbackSystem ? '✅' : '❌'}`);
            
            if (results.blendShapes.blendShapeNames && Array.isArray(results.blendShapes.blendShapeNames) && results.blendShapes.blendShapeNames.length > 0) {
                console.log(`  BlendShape名称: ${results.blendShapes.blendShapeNames.join(', ')}`);
            }
        }
        
        // 动画
        if (results.animations) {
            console.log('\n🎬 动画功能:');
            console.log(`  内置动画: ${results.animations.builtInAnimations.available ? '✅' : '❌'} (${results.animations.builtInAnimations.count}个)`);
            console.log(`  程序化动画: ${results.animations.proceduralAnimations.available ? '✅' : '❌'} (${results.animations.proceduralAnimations.count}个)`);
            console.log(`  备用动画: ✅`);
            
            if (results.animations.builtInAnimations.names.length > 0) {
                console.log(`  内置动画名称: ${results.animations.builtInAnimations.names.join(', ')}`);
            }
        }
        
        // 渲染
        if (results.rendering) {
            console.log('\n🎨 渲染功能:');
            console.log(`  场景: ${results.rendering.scene ? '✅' : '❌'}`);
            console.log(`  摄像机: ${results.rendering.camera ? '✅' : '❌'}`);
            console.log(`  渲染器: ${results.rendering.renderer ? '✅' : '❌'}`);
            console.log(`  模型: ${results.rendering.avatar ? '✅' : '❌'}`);
        }
        
        console.log('\n📋 诊断完成');
        
        // 在页面上也显示结果
        this.displayResultsInUI(results);
    }
    
    /**
     * 在UI中显示结果
     */
    displayResultsInUI(results) {
        const statusEl = document.getElementById('status');
        if (!statusEl) return;
        
        let statusText = '';
        
        if (!results.webgl.supported) {
            statusText = '❌ WebGL不支持';
        } else if (!results.modelLoading.success) {
            statusText = '❌ 模型加载失败';
        } else if (results.blendShapes && results.blendShapes.available) {
            statusText = `✅ 模型就绪 (${results.blendShapes.morphTargetsFound} BlendShapes)`;
        } else if (results.blendShapes && results.blendShapes.fallbackSystem) {
            statusText = '⚠️ 模型就绪 (备用表情系统)';
        } else {
            statusText = '✅ 基础功能就绪';
        }
        
        statusEl.textContent = statusText;
        
        // 在聊天窗口显示详细结果
        if (window.digitalHuman && digitalHuman.wsClient) {
            const summary = this.generateSummaryReport(results);
            digitalHuman.wsClient.addMessageToChat('ai', summary);
        }
    }
    
    /**
     * 生成摘要报告
     */
    generateSummaryReport(results) {
        let report = '🔍 系统诊断报告:\n\n';
        
        report += `WebGL支持: ${results.webgl.supported ? '✅' : '❌'}\n`;
        report += `模型加载: ${results.modelLoading.success ? '✅' : '❌'}\n`;
        
        if (results.modelLoading.success) {
            report += `加载路径: ${results.modelLoading.loadedPath}\n`;
        }
        
        if (results.blendShapes) {
            report += `表情功能: ${results.blendShapes.available ? '✅ BlendShape' : (results.blendShapes.fallbackSystem ? '⚠️ 备用系统' : '❌')}\n`;
        }
        
        if (results.animations) {
            const animCount = results.animations.builtInAnimations.count + results.animations.proceduralAnimations.count;
            report += `动画功能: ✅ (${animCount}个动画可用)\n`;
        }
        
        return report;
    }
    
    /**
     * 测试表情功能
     */
    async testExpressions() {
        console.log('🧪 测试表情功能...');
        
        if (!window.digitalHuman) {
            console.log('❌ 数字人系统未初始化');
            return;
        }
        
        const expressions = ['neutral', 'happy', 'thinking', 'concern'];
        
        for (const expression of expressions) {
            console.log(`测试表情: ${expression}`);
            digitalHuman.setExpression(expression);
            await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
        }
        
        console.log('✅ 表情测试完成');
    }
    
    /**
     * 测试动画功能
     */
    async testAnimations() {
        console.log('🧪 测试动画功能...');
        
        if (!window.digitalHuman) {
            console.log('❌ 数字人系统未初始化');
            return;
        }
        
        const animations = ['greeting', 'pointing', 'explaining'];
        
        for (const animation of animations) {
            console.log(`测试动画: ${animation}`);
            digitalHuman.testAnimation(animation);
            await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒
        }
        
        console.log('✅ 动画测试完成');
    }
}

// 创建全局诊断实例
const modelDiagnostics = new ModelDiagnostics();

// 添加诊断按钮到控制面板
function addDiagnosticControls() {
    const controls = document.getElementById('controls');
    if (!controls) return;
    
    const diagnosticButtons = document.createElement('div');
    diagnosticButtons.innerHTML = `
        <div style="margin-top: 8px; padding-top: 8px; border-top: 1px solid rgba(255,255,255,0.3);">
            <button onclick="modelDiagnostics.runFullDiagnostics()" title="运行完整诊断">🔍</button>
            <button onclick="modelDiagnostics.testExpressions()" title="测试表情">😊</button>
            <button onclick="modelDiagnostics.testAnimations()" title="测试动画">🎬</button>
        </div>
    `;
    
    controls.appendChild(diagnosticButtons);
}

// 页面加载完成后添加诊断控制
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        addDiagnosticControls();
        
        // 自动运行诊断（延迟5秒，等待系统初始化完成）
        setTimeout(() => {
            modelDiagnostics.runFullDiagnostics();
        }, 5000);
    }, 3000);
});

console.log('🔍 模型诊断工具加载完成');