# 2D数字人图片配置指南

## 问题解决方案

原问题：系统加载的是32×32像素的极小占位图，无法显示数字人形象。

## 配置步骤

### 步骤1：准备数字人图片
将您的数字人图片（如第一张截图中的红色西装女性）保存为以下任一格式：
- `digital-human.jpg`
- `digital-human.png` 
- `digital-human.webp`

### 步骤2：放置图片文件
将图片文件放在以下位置：
```
F:\张剑虹\数字人8\企业客服\digital-human.jpg
```

### 步骤3：修改配置文件（可选）
编辑 `digital-human-config.js` 文件：

```javascript
window.DIGITAL_HUMAN_CONFIG = {
    image2D: {
        // 主图片路径 - 修改为您的图片路径
        primaryPath: './digital-human.jpg',
        
        // 备用图片路径
        fallbackPaths: [
            './digital-human.png',
            './digital-human.webp',
            'https://your-cdn.com/digital-human.jpg'  // 网络图片
        ],
        
        // 图片显示样式
        style: {
            width: '300px',
            height: '400px',
            objectFit: 'cover'  // 保持比例填充
        }
    }
};
```

## 支持的图片格式
- **JPG/JPEG** - 推荐，文件小
- **PNG** - 支持透明背景
- **WebP** - 现代格式，压缩率高
- **SVG** - 矢量图形（程序生成）

## 图片建议
- **分辨率**: 300×400像素或更高
- **纵横比**: 3:4（人物肖像比例）
- **文件大小**: 建议小于2MB
- **背景**: 纯色或渐变背景效果好

## 网络图片配置
如果使用在线图片：
```javascript
primaryPath: 'https://your-domain.com/digital-human.jpg'
```

## 故障排除
如果图片不显示：
1. 检查文件路径是否正确
2. 确认图片格式支持
3. 查看浏览器控制台错误信息
4. 尝试使用绝对路径或在线图片URL

## 系统优化特性
- ✅ 自动尝试多个备用路径
- ✅ 支持配置文件动态配置
- ✅ 图片加载失败时显示精美占位符
- ✅ 鼠标悬停缩放效果
- ✅ 全屏显示模式