/**
 * 性能优化器
 * Linus式设计：智能适配，动态优化，用户无感知
 * 
 * 核心特性：
 * - GPU性能分层检测
 * - 动态质量调整
 * - LOD细节层次管理
 * - 内存使用监控
 * - 帧率自适应优化
 */

class PerformanceOptimizer {
    constructor() {
        // LOD级别配置
        this.lodLevels = {
            high: { 
                polygons: 50000, 
                texSize: 2048, 
                shadowMapSize: 2048,
                enableSSAO: true,
                enableBloom: true,
                enableTAA: true,
                particleCount: 1000,
                renderScale: 1.0
            },
            medium: { 
                polygons: 20000, 
                texSize: 1024,
                shadowMapSize: 1024,
                enableSSAO: true,
                enableBloom: false,
                enableTAA: false,
                particleCount: 500,
                renderScale: 0.8
            },
            low: { 
                polygons: 5000, 
                texSize: 512,
                shadowMapSize: 512,
                enableSSAO: false,
                enableBloom: false,
                enableTAA: false,
                particleCount: 100,
                renderScale: 0.6
            }
        };
        
        // 性能监控
        this.performance = {
            fps: 60,
            frameTime: 16.67,
            cpuUsage: 0,
            gpuUsage: 0,
            memoryUsage: 0,
            drawCalls: 0,
            triangles: 0,
            textures: 0
        };
        
        // 系统信息
        this.systemInfo = {
            gpuTier: 'unknown',
            devicePixelRatio: window.devicePixelRatio || 1,
            maxTextureSize: 0,
            webglVersion: 1,
            isMobile: false,
            isTablet: false,
            batteryLevel: 1.0,
            thermalState: 'normal'
        };
        
        // 优化状态
        this.optimizationState = {
            currentQuality: 'high',
            adaptiveEnabled: true,
            lastOptimization: 0,
            optimizationCooldown: 5000, // 5秒
            frameHistory: [],
            frameHistorySize: 60 // 保留60帧历史
        };
        
        this.init();
    }
    
    /**
     * 初始化性能优化器
     */
    init() {
        console.log('⚡ 性能优化器初始化');
        
        // 检测系统信息
        this.detectSystemInfo();
        
        // 检测GPU性能层级
        this.detectGPUTier();
        
        // 设置初始质量级别
        this.setInitialQuality();
        
        // 启动性能监控
        this.startPerformanceMonitoring();
        
        // 监听设备状态变化
        this.setupDeviceStateListeners();
        
        console.log('✅ 性能优化器就绪，质量级别:', this.optimizationState.currentQuality);
    }
    
    /**
     * 检测系统信息
     */
    detectSystemInfo() {
        // 检测移动设备
        const userAgent = navigator.userAgent.toLowerCase();
        this.systemInfo.isMobile = /mobile|android|iphone|ipod|blackberry|iemobile|opera mini/.test(userAgent);
        this.systemInfo.isTablet = /tablet|ipad/.test(userAgent);
        
        // 检测WebGL版本
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
            
            if (gl) {
                this.systemInfo.webglVersion = gl instanceof WebGL2RenderingContext ? 2 : 1;
                this.systemInfo.maxTextureSize = gl.getParameter(gl.MAX_TEXTURE_SIZE);
            }
        } catch (e) {
            console.warn('WebGL检测失败:', e);
        }
        
        // 检测电池状态
        if ('getBattery' in navigator) {
            navigator.getBattery().then(battery => {
                this.systemInfo.batteryLevel = battery.level;
                
                battery.addEventListener('levelchange', () => {
                    this.systemInfo.batteryLevel = battery.level;
                    this.checkPowerOptimization();
                });
                
                battery.addEventListener('chargingchange', () => {
                    this.checkPowerOptimization();
                });
            });
        }
        
        console.log('🔍 系统信息检测完成:', this.systemInfo);
    }
    
    /**
     * 检测GPU性能层级
     */
    detectGPUTier() {
        // 基于WebGL渲染器信息判断GPU性能
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            
            if (gl) {
                const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
                if (debugInfo) {
                    const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL).toLowerCase();
                    this.systemInfo.gpuTier = this.classifyGPU(renderer);
                } else {
                    // 回退检测方法
                    this.systemInfo.gpuTier = this.fallbackGPUDetection();
                }
            }
        } catch (e) {
            console.warn('GPU检测失败:', e);
            this.systemInfo.gpuTier = 'low';
        }
        
        console.log('🎮 GPU性能层级:', this.systemInfo.gpuTier);
    }
    
    /**
     * GPU分类
     */
    classifyGPU(renderer) {
        // 高端GPU
        const highEndGPUs = [
            'rtx 4090', 'rtx 4080', 'rtx 4070', 'rtx 3090', 'rtx 3080', 'rtx 3070',
            'rtx 2080', 'rtx 2070', 'gtx 1080', 'gtx 1070',
            'radeon rx 7900', 'radeon rx 6900', 'radeon rx 6800', 'radeon rx 6700',
            'adreno 740', 'adreno 730', 'apple m2', 'apple m1'
        ];
        
        // 中端GPU
        const midEndGPUs = [
            'rtx 4060', 'rtx 3060', 'rtx 2060', 'gtx 1660', 'gtx 1060', 'gtx 1050',
            'radeon rx 6600', 'radeon rx 5700', 'radeon rx 5600', 'radeon rx 580',
            'adreno 660', 'adreno 650', 'mali-g78', 'mali-g77'
        ];
        
        for (let gpu of highEndGPUs) {
            if (renderer.includes(gpu)) return 'high';
        }
        
        for (let gpu of midEndGPUs) {
            if (renderer.includes(gpu)) return 'medium';
        }
        
        return 'low';
    }
    
    /**
     * 回退GPU检测
     */
    fallbackGPUDetection() {
        // 基于设备类型的简单分类
        if (this.systemInfo.isMobile) {
            return 'low';
        } else if (this.systemInfo.isTablet) {
            return 'medium';
        } else {
            // 桌面设备默认中等性能
            return 'medium';
        }
    }
    
    /**
     * 设置初始质量级别
     */
    setInitialQuality() {
        let initialQuality = 'medium';
        
        // 基于GPU性能设置初始质量
        switch (this.systemInfo.gpuTier) {
            case 'high':
                initialQuality = 'high';
                break;
            case 'medium':
                initialQuality = 'medium';
                break;
            case 'low':
                initialQuality = 'low';
                break;
        }
        
        // 移动设备降级
        if (this.systemInfo.isMobile) {
            initialQuality = initialQuality === 'high' ? 'medium' : 'low';
        }
        
        // 低电量设备降级
        if (this.systemInfo.batteryLevel < 0.2) {
            initialQuality = 'low';
        }
        
        this.optimizationState.currentQuality = initialQuality;
        this.applyQualitySettings(initialQuality);
    }
    
    /**
     * 启动性能监控
     */
    startPerformanceMonitoring() {
        this.performanceMonitor = {
            frameCount: 0,
            lastTime: performance.now(),
            
            update: () => {
                this.performanceMonitor.frameCount++;
                const now = performance.now();
                
                if (now - this.performanceMonitor.lastTime >= 1000) {
                    // 计算FPS
                    this.performance.fps = this.performanceMonitor.frameCount;
                    this.performance.frameTime = 1000 / this.performance.fps;
                    
                    // 更新帧历史
                    this.updateFrameHistory(this.performance.fps);
                    
                    // 获取渲染统计
                    this.updateRenderingStats();
                    
                    // 检查是否需要优化
                    if (this.optimizationState.adaptiveEnabled) {
                        this.checkPerformanceOptimization();
                    }
                    
                    this.performanceMonitor.frameCount = 0;
                    this.performanceMonitor.lastTime = now;
                }
            }
        };
        
        console.log('📊 性能监控已启动');
    }
    
    /**
     * 更新帧历史
     */
    updateFrameHistory(fps) {
        this.optimizationState.frameHistory.push(fps);
        
        if (this.optimizationState.frameHistory.length > this.optimizationState.frameHistorySize) {
            this.optimizationState.frameHistory.shift();
        }
    }
    
    /**
     * 更新渲染统计
     */
    updateRenderingStats() {
        // 从Three.js渲染器获取统计信息
        if (window.enterpriseDigitalHuman && window.enterpriseDigitalHuman.renderer) {
            const renderer = window.enterpriseDigitalHuman.renderer;
            const info = renderer.info;
            
            this.performance.drawCalls = info.render.calls;
            this.performance.triangles = info.render.triangles;
            this.performance.textures = info.memory.textures;
        }
        
        // 内存使用估算
        if (performance.memory) {
            this.performance.memoryUsage = performance.memory.usedJSHeapSize / performance.memory.totalJSHeapSize;
        }
    }
    
    /**
     * 检查性能优化
     */
    checkPerformanceOptimization() {
        const now = Date.now();
        
        // 冷却时间检查
        if (now - this.optimizationState.lastOptimization < this.optimizationState.optimizationCooldown) {
            return;
        }
        
        const avgFPS = this.getAverageFrameRate();
        const targetFPS = 60;
        
        // 性能不足，需要降级
        if (avgFPS < 45 && this.optimizationState.currentQuality !== 'low') {
            console.log(`📉 性能不足 (${avgFPS.toFixed(1)} FPS)，降级质量`);
            this.downgradeQuality();
            this.optimizationState.lastOptimization = now;
        }
        // 性能充足，可以升级
        else if (avgFPS > 55 && this.optimizationState.currentQuality !== 'high') {
            console.log(`📈 性能充足 (${avgFPS.toFixed(1)} FPS)，升级质量`);
            this.upgradeQuality();
            this.optimizationState.lastOptimization = now;
        }
    }
    
    /**
     * 获取平均帧率
     */
    getAverageFrameRate() {
        if (this.optimizationState.frameHistory.length === 0) return 60;
        
        const sum = this.optimizationState.frameHistory.reduce((a, b) => a + b, 0);
        return sum / this.optimizationState.frameHistory.length;
    }
    
    /**
     * 降级质量
     */
    downgradeQuality() {
        const qualityLevels = ['high', 'medium', 'low'];
        const currentIndex = qualityLevels.indexOf(this.optimizationState.currentQuality);
        
        if (currentIndex < qualityLevels.length - 1) {
            const newQuality = qualityLevels[currentIndex + 1];
            this.setQuality(newQuality);
            console.log(`⬇️ 质量降级: ${this.optimizationState.currentQuality} → ${newQuality}`);
        }
    }
    
    /**
     * 升级质量
     */
    upgradeQuality() {
        const qualityLevels = ['high', 'medium', 'low'];
        const currentIndex = qualityLevels.indexOf(this.optimizationState.currentQuality);
        
        if (currentIndex > 0) {
            const newQuality = qualityLevels[currentIndex - 1];
            this.setQuality(newQuality);
            console.log(`⬆️ 质量升级: ${this.optimizationState.currentQuality} → ${newQuality}`);
        }
    }
    
    /**
     * 设置质量级别
     */
    setQuality(qualityLevel) {
        if (!this.lodLevels[qualityLevel]) {
            console.warn('❌ 无效的质量级别:', qualityLevel);
            return;
        }
        
        this.optimizationState.currentQuality = qualityLevel;
        this.applyQualitySettings(qualityLevel);
    }
    
    /**
     * 应用质量设置
     */
    applyQualitySettings(qualityLevel) {
        const settings = this.lodLevels[qualityLevel];
        
        console.log(`🎛️ 应用${qualityLevel}质量设置:`, settings);
        
        // 应用到企业级渲染系统
        if (window.enterpriseDigitalHuman) {
            const renderingSystem = window.enterpriseDigitalHuman;
            
            // 更新质量配置
            if (renderingSystem.qualityConfig) {
                renderingSystem.qualityConfig.renderScale = settings.renderScale;
                renderingSystem.qualityConfig.shadowMapSize = settings.shadowMapSize;
                renderingSystem.qualityConfig.enableSSAO = settings.enableSSAO;
                renderingSystem.qualityConfig.enableBloom = settings.enableBloom;
                renderingSystem.qualityConfig.enableTAA = settings.enableTAA;
            }
            
            // 更新渲染器设置
            if (renderingSystem.renderer) {
                const canvas = renderingSystem.renderer.domElement;
                const rect = canvas.getBoundingClientRect();
                
                renderingSystem.renderer.setSize(
                    rect.width * settings.renderScale,
                    rect.height * settings.renderScale
                );
                
                // 更新阴影贴图大小
                if (renderingSystem.lights && renderingSystem.lights.keyLight) {
                    renderingSystem.lights.keyLight.shadow.mapSize.setScalar(settings.shadowMapSize);
                }
            }
            
            // 更新后处理合成器
            if (renderingSystem.composer) {
                const canvas = renderingSystem.renderer.domElement;
                const rect = canvas.getBoundingClientRect();
                
                renderingSystem.composer.setSize(
                    rect.width * settings.renderScale,
                    rect.height * settings.renderScale
                );
            }
        }
        
        // 通知其他系统质量变化
        this.notifyQualityChange(qualityLevel, settings);
    }
    
    /**
     * 通知质量变化
     */
    notifyQualityChange(qualityLevel, settings) {
        // 发送自定义事件
        window.dispatchEvent(new CustomEvent('quality-changed', {
            detail: { qualityLevel, settings }
        }));
        
        // 更新UI显示
        this.updateQualityIndicator(qualityLevel);
    }
    
    /**
     * 更新质量指示器
     */
    updateQualityIndicator(qualityLevel) {
        // 可以在UI中显示当前质量级别
        const indicator = document.getElementById('quality-indicator');
        if (indicator) {
            const qualityEmojis = {
                'high': '🔥 高画质',
                'medium': '⚖️ 平衡',
                'low': '🔋 省电'
            };
            indicator.textContent = qualityEmojis[qualityLevel] || qualityLevel;
        }
    }
    
    /**
     * 检查电源优化
     */
    checkPowerOptimization() {
        // 低电量时自动降级
        if (this.systemInfo.batteryLevel < 0.2) {
            console.log('🔋 低电量检测，启用省电模式');
            this.setQuality('low');
        }
    }
    
    /**
     * 设置设备状态监听器
     */
    setupDeviceStateListeners() {
        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseOptimization();
            } else {
                this.resumeOptimization();
            }
        });
        
        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            // 延迟应用设置，避免频繁调整
            clearTimeout(this.resizeTimeout);
            this.resizeTimeout = setTimeout(() => {
                this.applyQualitySettings(this.optimizationState.currentQuality);
            }, 500);
        });
        
        console.log('👂 设备状态监听器已设置');
    }
    
    /**
     * 暂停优化
     */
    pauseOptimization() {
        this.optimizationState.adaptiveEnabled = false;
        console.log('⏸️ 性能优化已暂停');
    }
    
    /**
     * 恢复优化
     */
    resumeOptimization() {
        this.optimizationState.adaptiveEnabled = true;
        console.log('▶️ 性能优化已恢复');
    }
    
    /**
     * 强制设置质量级别
     */
    forceQuality(qualityLevel) {
        console.log(`🔧 强制设置质量级别: ${qualityLevel}`);
        this.optimizationState.adaptiveEnabled = false;
        this.setQuality(qualityLevel);
    }
    
    /**
     * 启用自适应质量
     */
    enableAdaptiveQuality() {
        console.log('🤖 启用自适应质量');
        this.optimizationState.adaptiveEnabled = true;
    }
    
    /**
     * 获取性能报告
     */
    getPerformanceReport() {
        return {
            performance: { ...this.performance },
            systemInfo: { ...this.systemInfo },
            currentQuality: this.optimizationState.currentQuality,
            averageFPS: this.getAverageFrameRate(),
            adaptiveEnabled: this.optimizationState.adaptiveEnabled
        };
    }
    
    /**
     * 更新方法 - 在渲染循环中调用
     */
    update() {
        if (this.performanceMonitor) {
            this.performanceMonitor.update();
        }
    }
    
    /**
     * 获取优化建议
     */
    getOptimizationSuggestions() {
        const suggestions = [];
        const avgFPS = this.getAverageFrameRate();
        
        if (avgFPS < 30) {
            suggestions.push('考虑降低渲染质量或关闭高级特效');
        }
        
        if (this.performance.memoryUsage > 0.8) {
            suggestions.push('内存使用率过高，建议释放不必要的资源');
        }
        
        if (this.performance.drawCalls > 1000) {
            suggestions.push('绘制调用次数过多，考虑合并网格');
        }
        
        if (this.systemInfo.batteryLevel < 0.3) {
            suggestions.push('电池电量较低，建议启用省电模式');
        }
        
        return suggestions;
    }
    
    /**
     * 资源清理
     */
    dispose() {
        // 清理监听器
        this.optimizationState.adaptiveEnabled = false;
        
        if (this.resizeTimeout) {
            clearTimeout(this.resizeTimeout);
        }
        
        console.log('🧹 性能优化器资源已清理');
    }
}

// 全局导出
window.PerformanceOptimizer = PerformanceOptimizer;

console.log('⚡ 性能优化器模块加载完成');