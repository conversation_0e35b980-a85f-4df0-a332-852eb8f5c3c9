/**
 * 简化版豆包API - Linus式设计：直接有效
 * 只做一件事：把用户输入发给豆包API，返回回复
 */

class DoubaoAPI {
    constructor() {
        this.endpoint = 'http://localhost:5000/api/chat';  // Python代理服务器
        this.model = 'doubao-seed-1-6-thinking-250715';
        this.maxTokens = 2000;
        this.temperature = 0.7;
        
        console.log('豆包API初始化完成');
    }
    
    /**
     * 发送消息给豆包API
     */
    async sendMessage(userMessage) {
        try {
            console.log('发送消息到豆包API:', userMessage);
            
            const response = await fetch(this.endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: this.model,
                    messages: [
                        {
                            role: 'system',
                            content: '你是一个友好的企业客服助手，简洁地回答用户问题。'
                        },
                        {
                            role: 'user',
                            content: userMessage
                        }
                    ],
                    max_tokens: this.maxTokens,
                    temperature: this.temperature
                })
            });
            
            if (!response.ok) {
                throw new Error(`API调用失败: ${response.status}`);
            }
            
            const result = await response.json();

            // 兼容不同的后端响应格式：OpenAI-like 或 代理包装格式
            let aiReply = '';
            try {
                if (result.choices && result.choices[0] && result.choices[0].message && result.choices[0].message.content) {
                    aiReply = result.choices[0].message.content;
                } else if (result.data && result.data.content) {
                    aiReply = result.data.content;
                } else if (result.content) {
                    aiReply = result.content;
                } else {
                    aiReply = JSON.stringify(result).slice(0, 200);
                }
            } catch (e) {
                aiReply = JSON.stringify(result).slice(0, 200);
            }

            console.log('豆包API回复:', aiReply);
            return aiReply;
            
        } catch (error) {
            console.error('豆包API调用失败:', error);
            return '抱歉，我现在无法回复您的问题。';
        }
    }
}

// 全局实例
window.DoubaoAPI = DoubaoAPI;