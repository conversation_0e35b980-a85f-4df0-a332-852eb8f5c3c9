/**
 * 3D数字人核心渲染系统
 * Linus式设计：简单、直接、可靠
 * 
 * 核心数据结构：
 * - scene: 3D场景
 * - avatar: GLB模型实例  
 * - expressions: 表情权重数组
 * - animations: 动画状态机
 */

class DigitalHuman {
    constructor() {
        // 核心3D对象
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.avatar = null;
        this.mixer = null;
        
        // 表情和动画状态
        this.currentExpression = 'neutral';
        this.currentAnimation = null;
        this.morphTargets = {};
        this.animations = {};
        
        // WebSocket连接
        this.websocket = null;
        this.isConnected = false;
        
        // 预定义表情映射 - 简单有效
        this.expressionMap = {
            'neutral': { smile: 0, blink: 0.1, browUp: 0, browDown: 0 },
            'happy': { smile: 0.8, blink: 0.2, browUp: 0.3, browDown: 0 },
            'thinking': { smile: 0.1, blink: 0.3, browUp: 0.2, browDown: 0 },
            'concern': { smile: 0, blink: 0.1, browUp: 0, browDown: 0.6 }
        };
        
        // 预定义动画序列
        this.animationMap = {
            'greeting': 'wave_hand',
            'pointing': 'point_finger', 
            'explaining': 'open_hands',
            'thinking': 'touch_chin',
            'goodbye': 'wave_goodbye'
        };
    }
    
    /**
     * 初始化3D系统 - 增强版错误处理
     */
    async init() {
        try {
            console.log('🎮 开始初始化数字人核心系统');
            
            // 等待模型加载器就绪
            if (window.enterpriseModelLoader) {
                await this.waitForModelLoader();
            }
            
            this.updateStatus('初始化3D场景...');
            this.initScene();
            
            this.updateStatus('加载3D模型...');
            
            // 如果有企业级加载器，优先使用2D模式
            if (window.enterpriseModelLoader) {
                console.log('✅ 使用企业级模型加载器，优先2D模式');
                this.updateStatus('2D数字人就绪');
            } else {
                // 备用：尝试加载3D模型
                await this.loadAvatar();
            }
            
            this.updateStatus('启动渲染循环...');
            this.startRenderLoop();
            
            this.updateStatus('连接服务器...');
            this.initWebSocket();
            
            this.updateStatus('系统就绪');
            console.log('✅ 数字人核心系统初始化完成');
        } catch (error) {
            console.error('❌ 数字人系统初始化失败:', error);
            this.updateStatus('初始化失败 - 使用2D模式');
            
            // 失败时确保2D模式可用
            this.ensureFallbackMode();
        }
    }
    
    /**
     * 等待模型加载器就绪
     */
    async waitForModelLoader() {
        let attempts = 0;
        while (!window.enterpriseModelLoader?.isReady && attempts < 10) {
            console.log('⏳ 等待模型加载器就绪...');
            await new Promise(resolve => setTimeout(resolve, 500));
            attempts++;
        }
        
        if (window.enterpriseModelLoader?.isReady) {
            console.log('✅ 模型加载器就绪');
        } else {
            console.warn('⚠️ 模型加载器未就绪，继续初始化');
        }
    }
    
    /**
     * 确保备用模式可用
     */
    ensureFallbackMode() {
        console.log('🛡️ 启用备用模式');
        
        // 确保2D容器存在
        if (window.enterpriseModelLoader) {
            window.enterpriseModelLoader.load2DModel();
        }
        
        // 禁用可能有问题的功能
        this.disable3DFeatures();
    }
    
    /**
     * 禁用3D功能
     */
    disable3DFeatures() {
        // 停止渲染循环
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        
        // 隐藏3D画布
        const canvas = document.getElementById('avatar-canvas');
        if (canvas) {
            canvas.style.display = 'none';
        }
        
        console.log('🔇 3D功能已禁用，使用2D模式');
    }
    
    /**
     * 创建3D场景 - 最简单的设置
     */
    initScene() {
        const canvas = document.getElementById('avatar-canvas');
        const rect = canvas.getBoundingClientRect();
        
        // 场景
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x2c3e50);
        
        // 摄像机 - 固定视角，不需要复杂控制
        this.camera = new THREE.PerspectiveCamera(
            50, 
            rect.width / rect.height, 
            0.1, 
            1000
        );
        this.camera.position.set(0, 1.5, 2);
        this.camera.lookAt(0, 1, 0);
        
        // 渲染器
        this.renderer = new THREE.WebGLRenderer({ 
            canvas: canvas,
            antialias: true,
            alpha: true
        });
        this.renderer.setSize(rect.width, rect.height);
        this.renderer.outputColorSpace = THREE.SRGBColorSpace; // 使用新的API
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        
        // 基础光照 - 简单有效
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(1, 2, 1);
        directionalLight.castShadow = true;
        this.scene.add(directionalLight);
    }
    
    /**
     * 加载GLB模型 - 使用实际存在的模型文件
     */
    async loadAvatar() {
        return new Promise((resolve, reject) => {
            // 确保GLTFLoader已经加载
            if (!window.GLTFLoader) {
                reject(new Error('GLTFLoader未加载，请等待Three.js初始化完成'));
                return;
            }
            
            const loader = new window.GLTFLoader();
            
            // 尝试加载实际存在的GLB模型文件
            const modelPaths = [
                'look1_nayong_glb_test.glb',           // 第一个模型文件
                'look1_nayong_glb_test (1).glb',      // 第二个模型文件  
                'look1-nayong-glb-test/source/look1.glb' // 解压后的模型
            ];
            
            this.tryLoadModel(loader, modelPaths, 0, resolve, reject);
        });
    }
    
    /**
     * 尝试加载模型（多个路径备选）
     */
    tryLoadModel(loader, paths, index, resolve, reject) {
        if (index >= paths.length) {
            reject(new Error('所有GLB模型路径都加载失败'));
            return;
        }
        
        const currentPath = paths[index];
        console.log(`尝试加载模型: ${currentPath}`);
        
        loader.load(
            currentPath,
            (gltf) => {
                console.log('GLB模型加载成功:', currentPath, gltf);
                
                this.avatar = gltf.scene;
                this.avatar.scale.set(1, 1, 1);
                this.avatar.position.set(0, 0, 0);
                
                // 详细检查模型结构
                this.inspectModel(gltf);
                
                // 设置材质和阴影
                this.setupModelMaterials();
                
                // 检查并设置BlendShape
                this.setupBlendShapes();
                
                // 检查并设置动画
                this.setupAnimations(gltf);
                
                this.scene.add(this.avatar);
                resolve();
            },
            (progress) => {
                const percent = (progress.loaded / progress.total * 100).toFixed(0);
                this.updateStatus(`加载模型 ${currentPath}: ${percent}%`);
            },
            (error) => {
                console.warn(`模型加载失败: ${currentPath}`, error);
                // 尝试下一个路径
                this.tryLoadModel(loader, paths, index + 1, resolve, reject);
            }
        );
    }
    
    /**
     * 检查模型结构
     */
    inspectModel(gltf) {
        console.log('=== 模型结构检查 ===');
        console.log('场景对象数量:', gltf.scene.children.length);
        console.log('动画数量:', gltf.animations ? gltf.animations.length : 0);
        
        // 遍历所有网格，查找BlendShape
        let meshCount = 0;
        let morphTargetCount = 0;
        
        gltf.scene.traverse((child) => {
            if (child.isMesh) {
                meshCount++;
                console.log(`网格 ${meshCount}:`, {
                    name: child.name,
                    geometry: child.geometry.type,
                    morphTargets: child.morphTargetInfluences ? child.morphTargetInfluences.length : 0,
                    morphDict: child.morphTargetDictionary
                });
                
                if (child.morphTargetInfluences) {
                    morphTargetCount += child.morphTargetInfluences.length;
                }
            }
        });
        
        console.log(`总计: ${meshCount}个网格, ${morphTargetCount}个变形目标`);
        
        // 检查动画
        if (gltf.animations) {
            gltf.animations.forEach((clip, index) => {
                console.log(`动画 ${index}:`, {
                    name: clip.name,
                    duration: clip.duration,
                    tracks: clip.tracks.length
                });
            });
        }
        
        console.log('=== 检查完成 ===');
    }
    
    /**
     * 设置模型材质
     */
    setupModelMaterials() {
        this.avatar.traverse((child) => {
            if (child.isMesh) {
                child.castShadow = true;
                child.receiveShadow = true;
                
                // 如果材质太暗，稍微调亮
                if (child.material) {
                    if (child.material.emissive) {
                        child.material.emissive.setHex(0x111111); // 稍微发光
                    }
                }
            }
        });
    }
    
    /**
     * 设置BlendShape
     */
    setupBlendShapes() {
        let foundBlendShapes = false;
        
        this.avatar.traverse((child) => {
            if (child.isMesh && child.morphTargetInfluences) {
                console.log('发现BlendShape目标:', child.name, child.morphTargetDictionary);
                this.morphTargets[child.name] = child;
                foundBlendShapes = true;
                
                // 初始化所有BlendShape为0
                child.morphTargetInfluences.fill(0);
            }
        });
        
        if (!foundBlendShapes) {
            console.warn('未找到BlendShape目标，表情控制将不可用');
            // 创建模拟的morphTargets用于测试
            this.createFallbackMorphTargets();
        } else {
            console.log('BlendShape设置完成，找到目标数量:', Object.keys(this.morphTargets).length);
        }
    }
    
    /**
     * 创建备用的形变目标（用于没有BlendShape的模型）
     */
    createFallbackMorphTargets() {
        console.log('创建备用表情控制系统');
        
        // 创建一个虚拟的形变控制器
        this.fallbackExpressionController = {
            currentExpression: 'neutral',
            animateToExpression: (targetExpression, duration = 1000) => {
                console.log(`切换表情: ${this.fallbackExpressionController.currentExpression} -> ${targetExpression}`);
                this.fallbackExpressionController.currentExpression = targetExpression;
                
                // 这里可以添加其他视觉效果，比如改变材质颜色等
                this.animateModelForExpression(targetExpression);
            }
        };
    }
    
    /**
     * 为没有BlendShape的模型创建表情动画
     */
    animateModelForExpression(expression) {
        if (!this.avatar) return;
        
        // 通过轻微的缩放、旋转等方式模拟表情变化
        const head = this.findHeadBone() || this.avatar;
        
        switch (expression) {
            case 'happy':
                // 轻微向上倾斜表示高兴
                head.rotation.x = Math.PI * 0.02;
                break;
            case 'thinking':
                // 轻微向右倾斜表示思考
                head.rotation.z = Math.PI * 0.01;
                break;
            case 'concern':
                // 轻微向下表示关切
                head.rotation.x = Math.PI * -0.02;
                break;
            default:
                // 恢复中性状态
                head.rotation.set(0, 0, 0);
        }
    }
    
    /**
     * 查找头部骨骼
     */
    findHeadBone() {
        let headBone = null;
        
        this.avatar.traverse((child) => {
            if (child.type === 'Bone' && 
                (child.name.toLowerCase().includes('head') || 
                 child.name.toLowerCase().includes('neck'))) {
                headBone = child;
            }
        });
        
        return headBone;
    }
    
    /**
     * 设置动画系统
     */
    setupAnimations(gltf) {
        if (gltf.animations && gltf.animations.length > 0) {
            this.mixer = new THREE.AnimationMixer(this.avatar);
            
            gltf.animations.forEach((clip) => {
                console.log('设置动画:', clip.name, '时长:', clip.duration);
                const action = this.mixer.clipAction(clip);
                this.animations[clip.name] = action;
                
                // 设置循环模式
                action.setLoop(THREE.LoopRepeat);
            });
            
            console.log('动画系统设置完成，可用动画:', Object.keys(this.animations));
        } else {
            console.warn('模型中未找到动画，将创建程序化动画');
            this.createProceduralAnimations();
        }
    }
    
    /**
     * 创建程序化动画（用于没有内置动画的模型）
     */
    createProceduralAnimations() {
        console.log('创建程序化动画系统');
        
        this.proceduralAnimations = {
            greeting: () => this.animateGreeting(),
            pointing: () => this.animatePointing(), 
            explaining: () => this.animateExplaining()
        };
    }
    
    /**
     * 程序化打招呼动画
     */
    animateGreeting() {
        if (!this.avatar) return;
        
        const rightArm = this.findBone('arm', 'right') || this.avatar;
        
        // 简单的摆手动画
        const originalRotation = rightArm.rotation.z;
        
        const animate = (time) => {
            const wave = Math.sin(time * 0.01) * 0.5;
            rightArm.rotation.z = originalRotation + wave;
        };
        
        // 运行2秒动画
        const startTime = Date.now();
        const animateLoop = () => {
            const elapsed = Date.now() - startTime;
            if (elapsed < 2000) {
                animate(elapsed);
                requestAnimationFrame(animateLoop);
            } else {
                rightArm.rotation.z = originalRotation; // 恢复原状
            }
        };
        
        animateLoop();
    }
    
    /**
     * 查找特定骨骼
     */
    findBone(type, side) {
        let targetBone = null;
        
        this.avatar.traverse((child) => {
            if (child.type === 'Bone') {
                const name = child.name.toLowerCase();
                if (name.includes(type) && name.includes(side)) {
                    targetBone = child;
                }
            }
        });
        
        return targetBone;
    }
    
    /**
     * 启动渲染循环 - 60FPS目标
     */
    startRenderLoop() {
        const clock = new THREE.Clock();
        
        const render = () => {
            requestAnimationFrame(render);
            
            const deltaTime = clock.getDelta();
            
            // 更新动画
            if (this.mixer) {
                this.mixer.update(deltaTime);
            }
            
            // 渲染场景
            this.renderer.render(this.scene, this.camera);
        };
        
        render();
    }
    
    /**
     * 设置表情 - 支持BlendShape和备用系统
     */
    setExpression(expressionName, intensity = 1.0) {
        if (!this.expressionMap[expressionName]) {
            console.warn('未知表情:', expressionName);
            return;
        }
        
        console.log('设置表情:', expressionName, '强度:', intensity);
        
        // 如果有BlendShape，使用原来的方式
        if (Object.keys(this.morphTargets).length > 0) {
            this.setBlendShapeExpression(expressionName, intensity);
        } 
        // 否则使用备用表情系统
        else if (this.fallbackExpressionController) {
            this.fallbackExpressionController.animateToExpression(expressionName);
        }
        // 如果都没有，至少记录状态
        else {
            console.log(`表情切换到: ${expressionName} (无物理表情系统)`);
        }
        
        this.currentExpression = expressionName;
    }
    
    /**
     * 使用BlendShape设置表情
     */
    setBlendShapeExpression(expressionName, intensity) {
        const targetExpression = this.expressionMap[expressionName];
        
        // 遍历所有有BlendShape的Mesh
        Object.values(this.morphTargets).forEach(mesh => {
            if (mesh.morphTargetInfluences && mesh.morphTargetDictionary) {
                // 重置所有权重
                mesh.morphTargetInfluences.fill(0);
                
                // 应用目标表情
                Object.keys(targetExpression).forEach(blendShapeName => {
                    const index = mesh.morphTargetDictionary[blendShapeName];
                    if (index !== undefined) {
                        mesh.morphTargetInfluences[index] = targetExpression[blendShapeName] * intensity;
                        console.log(`应用BlendShape: ${blendShapeName} = ${targetExpression[blendShapeName] * intensity}`);
                    } else {
                        console.log(`BlendShape不存在: ${blendShapeName}`);
                    }
                });
            }
        });
    }
    
    /**
     * 播放动画 - 支持内置动画和程序化动画
     */
    playAnimation(animationName, loop = false) {
        console.log('播放动画:', animationName);
        
        // 先尝试内置动画
        if (this.mixer && this.animations[animationName]) {
            this.playBuiltInAnimation(animationName, loop);
        }
        // 然后尝试程序化动画
        else if (this.proceduralAnimations && this.proceduralAnimations[animationName]) {
            this.proceduralAnimations[animationName]();
        }
        // 最后尝试映射的动画名
        else {
            const mappedName = this.animationMap[animationName];
            if (mappedName && this.animations[mappedName]) {
                this.playBuiltInAnimation(mappedName, loop);
            } else {
                console.warn('动画不存在:', animationName, '可用动画:', Object.keys(this.animations));
                // 创建一个简单的替代动画
                this.createSimpleAnimation(animationName);
            }
        }
    }
    
    /**
     * 播放内置动画
     */
    playBuiltInAnimation(animationName, loop) {
        // 停止当前动画
        if (this.currentAnimation) {
            this.currentAnimation.stop();
        }
        
        // 播放新动画
        const action = this.animations[animationName];
        action.reset();
        action.setLoop(loop ? THREE.LoopRepeat : THREE.LoopOnce);
        action.play();
        
        this.currentAnimation = action;
        console.log('播放内置动画:', animationName);
    }
    
    /**
     * 创建简单的替代动画
     */
    createSimpleAnimation(animationName) {
        if (!this.avatar) return;
        
        console.log('创建替代动画:', animationName);
        
        const duration = 2000; // 2秒动画
        const startTime = Date.now();
        
        const animateStep = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            switch (animationName) {
                case 'greeting':
                case 'wave_hand':
                    this.simpleWaveAnimation(progress);
                    break;
                    
                case 'pointing':
                case 'point_finger':
                    this.simplePointAnimation(progress);
                    break;
                    
                case 'explaining':
                case 'open_hands':
                    this.simpleExplainAnimation(progress);
                    break;
                    
                default:
                    this.simpleGenericAnimation(progress);
            }
            
            if (progress < 1) {
                requestAnimationFrame(animateStep);
            } else {
                console.log('替代动画完成:', animationName);
            }
        };
        
        animateStep();
    }
    
    /**
     * 简单摆手动画
     */
    simpleWaveAnimation(progress) {
        const wave = Math.sin(progress * Math.PI * 4) * 0.3; // 4次摆动
        const rightArm = this.findBone('arm', 'right') || this.avatar;
        
        if (rightArm.rotation) {
            rightArm.rotation.z = wave;
        }
    }
    
    /**
     * 简单指向动画  
     */
    simplePointAnimation(progress) {
        const point = Math.sin(progress * Math.PI) * 0.2; // 单次指向
        const rightArm = this.findBone('arm', 'right') || this.avatar;
        
        if (rightArm.rotation) {
            rightArm.rotation.y = point;
            rightArm.rotation.x = -point * 0.5;
        }
    }
    
    /**
     * 简单解释动画
     */
    simpleExplainAnimation(progress) {
        const explain = Math.sin(progress * Math.PI * 2) * 0.1; // 轻微摆动
        
        if (this.avatar.rotation) {
            this.avatar.rotation.y = explain;
        }
    }
    
    /**
     * 通用简单动画
     */
    simpleGenericAnimation(progress) {
        const generic = Math.sin(progress * Math.PI) * 0.05; // 很轻微的动作
        
        if (this.avatar.position) {
            this.avatar.position.y = generic;
        }
    }
    
    /**
     * 更新状态显示
     */
    updateStatus(message) {
        const statusEl = document.getElementById('status');
        if (statusEl) {
            statusEl.textContent = message;
        }
        console.log('状态:', message);
    }
    
    /**
     * 测试用表情控制
     */
    testExpression(expression) {
        this.setExpression(expression);
    }
    
    /**
     * 测试用动画控制
     */
    testAnimation(animation) {
        // 如果有映射的动画名，使用映射后的
        const actualAnimation = this.animationMap[animation] || animation;
        this.playAnimation(actualAnimation);
    }
}

// 全局实例
const digitalHuman = new DigitalHuman();

/**
 * 将 viseme 权重应用到 morph targets（带平滑插值）
 * visemes: { jawOpen, mouthSmile, mouthFrown, mouthWide }
 */
DigitalHuman.prototype.applyVisemes = function(visemes, timestamp = Date.now()) {
    if (!this.morphTargets) return;

    // 平滑参数
    const smoothing = 0.3; // 0-1, 值越大越平滑

    Object.values(this.morphTargets).forEach(mesh => {
        if (!mesh.morphTargetInfluences || !mesh.morphTargetDictionary) return;

        // 为常见名称提供映射
        const mapping = {
            jawOpen: ['jawOpen', 'mouth_open', 'jaw_drop'],
            mouthSmile: ['mouthSmile', 'smile'],
            mouthFrown: ['mouthFrown', 'frown'],
            mouthWide: ['mouthWide', 'mouth_wide']
        };

        Object.keys(visemes).forEach(key => {
            const candidates = mapping[key] || [key];
            let idx = undefined;
            for (let name of candidates) {
                if (name in mesh.morphTargetDictionary) {
                    idx = mesh.morphTargetDictionary[name];
                    break;
                }
            }

            if (idx !== undefined) {
                const target = visemes[key];
                const current = mesh.morphTargetInfluences[idx] || 0;
                // 平滑插值
                mesh.morphTargetInfluences[idx] = current * (1 - smoothing) + target * smoothing;
            }
        });
    });
};