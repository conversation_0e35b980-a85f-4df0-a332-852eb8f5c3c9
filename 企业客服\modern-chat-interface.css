/* 
 * 现代化AI豆包聊天界面设计
 * 采用最新的UI/UX设计理念和现代CSS技术
 */

:root {
    /* 现代色彩系统 */
    --primary-color: #6366f1;
    --primary-hover: #5b5bd6;
    --secondary-color: #f1f5f9;
    --accent-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    
    /* 中性色调 */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* 语义化颜色 */
    --bg-primary: #ffffff;
    --bg-secondary: var(--gray-50);
    --bg-tertiary: var(--gray-100);
    --text-primary: var(--gray-900);
    --text-secondary: var(--gray-600);
    --text-muted: var(--gray-400);
    --border-color: var(--gray-200);
    --border-hover: var(--gray-300);
    
    /* 阴影系统 */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* 圆角系统 */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    
    /* 间距系统 */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    
    /* 字体系统 */
    --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    
    /* 动画时长 */
    --duration-fast: 150ms;
    --duration-normal: 250ms;
    --duration-slow: 350ms;
    
    /* 缓动函数 */
    --ease-out: cubic-bezier(0.0, 0.0, 0.2, 1);
    --ease-in: cubic-bezier(0.4, 0.0, 1, 1);
    --ease-in-out: cubic-bezier(0.4, 0.0, 0.2, 1);
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: var(--gray-900);
        --bg-secondary: var(--gray-800);
        --bg-tertiary: var(--gray-700);
        --text-primary: var(--gray-50);
        --text-secondary: var(--gray-300);
        --text-muted: var(--gray-400);
        --border-color: var(--gray-700);
        --border-hover: var(--gray-600);
    }
}

/* 现代化聊天容器 */
.modern-chat-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
}

/* 聊天头部 */
.modern-chat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-4) var(--space-6);
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    position: relative;
    overflow: hidden;
}

.modern-chat-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.chat-title-modern {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    font-weight: 600;
    font-size: 1.125rem;
    z-index: 1;
}

.ai-status-indicator {
    width: 8px;
    height: 8px;
    background: var(--success-color);
    border-radius: 50%;
    animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.chat-actions {
    display: flex;
    gap: var(--space-2);
    z-index: 1;
}

.action-button {
    width: 32px;
    height: 32px;
    border: none;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: var(--radius-md);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--duration-fast) var(--ease-out);
    backdrop-filter: blur(10px);
}

.action-button:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.action-button:active {
    transform: scale(0.95);
}

/* 消息区域 */
.modern-messages-container {
    flex: 1;
    overflow-y: auto;
    padding: var(--space-4);
    background: var(--bg-secondary);
    scroll-behavior: smooth;
}

/* 自定义滚动条 */
.modern-messages-container::-webkit-scrollbar {
    width: 6px;
}

.modern-messages-container::-webkit-scrollbar-track {
    background: transparent;
}

.modern-messages-container::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 3px;
}

.modern-messages-container::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* 消息气泡 */
.message-bubble {
    max-width: 80%;
    margin-bottom: var(--space-4);
    animation: slideInUp var(--duration-normal) var(--ease-out);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-bubble.user {
    margin-left: auto;
}

.message-bubble.ai {
    margin-right: auto;
}

.message-content {
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    line-height: 1.5;
    word-wrap: break-word;
    position: relative;
}

.message-bubble.user .message-content {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    color: white;
    border-bottom-right-radius: var(--radius-sm);
}

.message-bubble.ai .message-content {
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-bottom-left-radius: var(--radius-sm);
    box-shadow: var(--shadow-sm);
}

.message-meta {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-top: var(--space-2);
    font-size: 0.75rem;
    color: var(--text-muted);
}

.message-bubble.user .message-meta {
    justify-content: flex-end;
}

.message-time {
    opacity: 0.7;
}

.message-status {
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

/* 输入区域 */
.modern-input-container {
    padding: var(--space-4) var(--space-6);
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color);
}

.input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: var(--space-3);
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    padding: var(--space-2);
    border: 1px solid var(--border-color);
    transition: all var(--duration-fast) var(--ease-out);
}

.input-wrapper:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.message-input-modern {
    flex: 1;
    border: none;
    background: transparent;
    padding: var(--space-3) var(--space-4);
    font-size: 0.875rem;
    line-height: 1.5;
    color: var(--text-primary);
    resize: none;
    max-height: 120px;
    min-height: 20px;
    font-family: var(--font-sans);
}

.message-input-modern::placeholder {
    color: var(--text-muted);
}

.message-input-modern:focus {
    outline: none;
}

.input-actions {
    display: flex;
    gap: var(--space-2);
    align-items: flex-end;
}

.input-action-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border-radius: var(--radius-md);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--duration-fast) var(--ease-out);
    font-size: 1rem;
}

.input-action-btn:hover {
    background: var(--gray-300);
    color: var(--text-primary);
    transform: scale(1.05);
}

.input-action-btn.primary {
    background: var(--primary-color);
    color: white;
}

.input-action-btn.primary:hover {
    background: var(--primary-hover);
}

.input-action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* 打字指示器 */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    margin-bottom: var(--space-4);
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    max-width: 80px;
    animation: slideInUp var(--duration-normal) var(--ease-out);
}

.typing-dots {
    display: flex;
    gap: var(--space-1);
}

.typing-dot {
    width: 6px;
    height: 6px;
    background: var(--text-muted);
    border-radius: 50%;
    animation: typingDot 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typingDot {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 快捷回复 */
.quick-replies {
    display: flex;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.quick-replies::-webkit-scrollbar {
    display: none;
}

.quick-reply-btn {
    flex-shrink: 0;
    padding: var(--space-2) var(--space-4);
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    font-size: 0.75rem;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--duration-fast) var(--ease-out);
    white-space: nowrap;
}

.quick-reply-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

/* 消息增强功能 */
.message-enhanced {
    position: relative;
}

.message-actions {
    position: absolute;
    top: -12px;
    right: var(--space-3);
    display: flex;
    gap: var(--space-1);
    opacity: 0;
    transition: opacity var(--duration-fast) var(--ease-out);
}

.message-enhanced:hover .message-actions {
    opacity: 1;
}

.message-action {
    width: 24px;
    height: 24px;
    border: none;
    background: var(--bg-primary);
    color: var(--text-muted);
    border-radius: var(--radius-sm);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    box-shadow: var(--shadow-sm);
    transition: all var(--duration-fast) var(--ease-out);
}

.message-action:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .modern-chat-header {
        padding: var(--space-3) var(--space-4);
    }

    .modern-messages-container {
        padding: var(--space-3);
    }

    .modern-input-container {
        padding: var(--space-3) var(--space-4);
    }

    .message-bubble {
        max-width: 90%;
    }

    .quick-replies {
        padding: var(--space-2) var(--space-4);
    }
}

/* 无障碍访问 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-muted: var(--text-secondary);
    }
}
