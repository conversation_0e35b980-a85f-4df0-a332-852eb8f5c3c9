# 多模态豆包API集成使用说明

## 概述

本系统集成了三个豆包API，实现真正的多模态智能交互：

1. **豆包大模型1.6** - 主推理引擎 (`doubao-seed-1-6-thinking-250715`)
2. **豆包视觉理解** - 图像分析引擎 (支持多模态输入)
3. **豆包1.6-Flash** - 快速响应引擎 (用于语音对话)

## API配置

### 1. 获取API密钥

访问火山引擎控制台获取 `ARK_API_KEY`：
```
https://console.volcengine.com/ark
```

### 2. 配置环境变量

**方式1：环境变量 (推荐)**
```bash
export ARK_API_KEY="your_actual_api_key"
```

**方式2：代码中配置**
```javascript
// 在 multimodal-doubao.js 中修改
apiKey: 'your_actual_api_key'
```

### 3. 模型ID配置

确认以下模型ID是否正确：
```javascript
// 主推理模型
reasoning: 'doubao-seed-1-6-thinking-250715模型ID已自动填入'

// 视觉理解 (与主模型相同)
vision: 'doubao-seed-1-6-thinking-250715模型ID已自动填入'  

// 快速响应
speech: 'doubao-seed-1-6-flash-250715'
```

## 使用方法

### 启动系统
```bash
# 1. 配置API密钥
export ARK_API_KEY="your_api_key"

# 2. 启动Web服务器 (例如使用Python)
python -m http.server 8000

# 3. 访问页面
open http://localhost:8000
```

### 功能使用

#### 1. 文本对话
- 在聊天框输入文字，系统自动使用推理模型回复
- 支持上下文理解和多轮对话

#### 2. 图像分析
- 点击 📸 按钮分析当前摄像头画面
- 或者拖拽图片文件到聊天窗口
- 系统使用视觉理解模型分析图片内容

#### 3. 语音交互
- 系统检测到语音输入时自动使用Flash模型快速响应
- 支持TTS语音输出

#### 4. 多模态组合
- 可以同时发送文字+图片进行综合分析
- 系统智能调度不同的API处理不同模态的数据

## 控制面板按钮

```
😊 😐 🤔 - 表情测试按钮
👋 👉     - 动作测试按钮
🎬       - 功能演示
🔌       - 连接测试
📹       - 摄像头显示
📸       - 图像分析
🤖       - 多模态测试
🔄       - 切换模式
```

## API调用流程

### 单模态调用
```javascript
// 纯文本
await multiModalAPI.processTextInput("你好");

// 纯图像  
await multiModalAPI.processImageInput(imageBase64);
```

### 多模态组合调用
```javascript
// 自动根据输入类型选择处理策略
const strategy = multiModalAPI.determineProcessingStrategy({
    text: "请分析这张图片",
    visual: imageData,
    timestamp: Date.now()
});
```

## 响应优先级

系统按以下优先级处理请求：

1. **语音交互** (最高优先级) - 使用Flash模型保证低延迟
2. **视觉分析** (中等优先级) - 使用主模型的视觉能力  
3. **文本推理** (普通优先级) - 使用主推理模型

## 故障排查

### 常见问题

**1. API调用失败**
```
错误: API调用失败: 401 Unauthorized
解决: 检查API密钥是否正确设置
```

**2. 模型ID错误**
```
错误: model not found
解决: 确认模型ID是否正确，检查控制台中的推理接入点
```

**3. CORS错误**
```
错误: Access-Control-Allow-Origin
解决: 使用本地服务器，不能直接打开HTML文件
```

**4. 多模态功能不工作**
```
检查步骤:
1. 确认API密钥配置正确
2. 检查浏览器控制台错误信息
3. 验证模型ID是否匹配
4. 确认网络连接正常
```

### 调试模式

在浏览器控制台中启用详细日志：
```javascript
// 启用调试模式
multiModalAPI.debugMode = true;

// 查看系统状态
console.log(multiModalAPI.sessionState);

// 测试API连接
await multiModalAPI.callReasoningAPI("测试");
```

## 性能优化建议

### 1. API调用优化
- 合理设置 `max_tokens` 限制响应长度
- 根据场景调整 `temperature` 参数
- 使用Flash模型处理简单快速响应

### 2. 图像处理优化
- 压缩图片至合适尺寸 (建议不超过2MB)
- 使用JPEG格式以减少数据传输量

### 3. 缓存机制
- 实现对话历史缓存
- 避免重复的API调用

## 扩展开发

### 添加新的模态输入
```javascript
// 例：添加音频处理
multiModalAPI.processAudioInput = async function(audioData) {
    // 音频转文字
    const transcript = await this.audioToText(audioData);
    
    // 使用语音模型处理
    return await this.processSpeechInput(transcript);
};
```

### 自定义处理策略
```javascript
// 添加新的处理策略
const customStrategy = {
    type: 'audio_visual_combined',
    sequence: ['audio', 'vision', 'reasoning'],
    description: '音频+视觉综合分析'
};
```

## 安全注意事项

1. **API密钥安全**
   - 不要在客户端代码中硬编码API密钥
   - 生产环境使用服务器端代理

2. **数据隐私**
   - 图像和音频数据会发送到豆包API
   - 确保符合数据保护要求

3. **访问控制**
   - 实施适当的用户认证
   - 限制API调用频率

## 技术支持

如遇问题，请检查：

1. **官方文档**: https://www.volcengine.com/docs/82379
2. **控制台**: https://console.volcengine.com/ark  
3. **系统日志**: 浏览器开发者工具控制台
4. **网络状态**: 确认API服务可正常访问

---

*本文档基于豆包API实际配置编写，如有API变更请及时更新配置。*