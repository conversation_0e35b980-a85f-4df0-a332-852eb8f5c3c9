/**
 * 多模态豆包API调度系统
 * Linus式设计：简单的数据流，清晰的责任分离
 * 
 * 三个核心模型：
 * 1. 豆包大模型1.6 - 主推理引擎 (文本理解/生成)
 * 2. 豆包视觉理解 - 摄像头分析 (图像识别/场景理解) 
 * 3. 豆包实时语音 - 语音对话 (语音识别/合成)
 */

class MultiModalDoubaoAPI {
    constructor() {
        // API配置 - 使用代理服务器
        this.proxyUrl = 'http://localhost:5000';  // Python代理服务器
        this.apis = {
            reasoning: {
                name: '豆包大模型1.6',
                endpoint: `${this.proxyUrl}/api/chat`,
                model: 'doubao-seed-1-6-thinking-250715',  // 主推理模型
                priority: 2,
                maxTokens: 4000,
                temperature: 0.7
            },
            vision: {
                name: '豆包视觉理解',
                endpoint: `${this.proxyUrl}/api/vision`,
                model: 'doubao-seed-1-6-thinking-250715',  // 同样支持视觉
                priority: 1,
                maxTokens: 2000,
                temperature: 0.3  // 视觉分析用较低温度
            },
            speech: {
                name: '豆包1.6-Flash(语音优化)',
                endpoint: `${this.proxyUrl}/api/chat`,
                model: 'doubao-seed-1-6-flash-250715',  // 快速响应模型用于语音
                priority: 3, // 最高优先级
                maxTokens: 1000,  // 语音响应通常较短
                temperature: 0.8  // 语音对话稍微活泼一点
            }
        };
        
        // 数据流管理
        this.dataStreams = {
            visual: null,      // 视觉数据流
            audio: null,       // 音频数据流  
            text: null,        // 文本数据流
            context: []        // 对话上下文
        };
        
        // 时间同步管理
        this.timeSync = {
            baseTimestamp: Date.now(),
            visualLatency: 0,
            audioLatency: 0,
            syncThreshold: 500  // 500ms同步阈值
        };
        
        // 响应队列（按优先级）
        this.responseQueue = {
            high: [],    // 语音响应
            medium: [],  // 视觉分析
            low: []      // 文本推理
        };
        
        // 当前会话状态
        this.sessionState = {
            isActive: false,
            currentMode: 'idle', // idle, listening, processing, responding
            lastInteraction: null,
            userContext: {}
        };
        
        this.init();
    }
    
    /**
     * 初始化多模态系统
     */
    async init() {
        console.log('初始化多模态豆包API系统...');
        
        try {
            // 初始化各个API连接
            await this.initReasoningAPI();
            await this.initVisionAPI();
            await this.initSpeechAPI();
            
            // 同步服务器时间以便多模态时间线对齐
            try {
                await this.syncTimeWithServer();
            } catch (e) {
                console.warn('时间同步失败，继续使用本地时间:', e);
            }
            
            // 启动数据流处理器
            this.startDataFlowProcessor();
            
            // 启动响应调度器
            this.startResponseScheduler();
            
            console.log('多模态系统初始化完成');
            return true;
            
        } catch (error) {
            console.error('多模态系统初始化失败:', error);
            return false;
        }
    }

    /**
     * 与代理服务器做简单时间同步，计算 client -> server 偏移量
     */
    async syncTimeWithServer() {
        try {
            const url = `${this.proxyUrl}/health`;
            const t0 = Date.now();
            const resp = await fetch(url, { method: 'GET', cache: 'no-store' });
            const t1 = Date.now();

            // 优先使用返回体中的 server 时间字段
            let serverTime = null;
            try {
                const body = await resp.json();
                if (body && body.server_time) serverTime = Number(body.server_time);
            } catch (e) {
                // 忽略解析错误
            }

            // 如果没有 server_time，尝试使用 Date 响应头
            if (!serverTime) {
                const dateHeader = resp.headers.get('Date');
                if (dateHeader) {
                    serverTime = new Date(dateHeader).getTime();
                }
            }

            if (!serverTime) {
                throw new Error('未能从服务器获得时间戳');
            }

            // 估计往返延迟的一半作为偏移校正
            const rtt = t1 - t0;
            const estimatedServerNow = serverTime + rtt / 2;
            const offset = estimatedServerNow - t1; // server - client
            this.timeSync.offset = offset;
            console.log('时间同步完成，server-client 偏移(ms):', offset, 'rtt:', rtt);
            return offset;
        } catch (error) {
            console.warn('时间同步失败:', error);
            throw error;
        }
    }
    
    /**
     * 初始化推理API
     */
    async initReasoningAPI() {
        console.log('初始化豆包大模型1.6...');
        
        // 测试连接
        try {
            const testResponse = await this.callReasoningAPI('测试连接');
            console.log('推理模型连接成功:', testResponse);
        } catch (error) {
            console.error('推理模型连接失败:', error);
            throw error;
        }
    }
    
    /**
     * 初始化视觉API
     */
    async initVisionAPI() {
        console.log('初始化豆包视觉理解模型...');
        
        // 这里可以添加视觉API的初始化逻辑
        // 比如设置图像预处理参数等
    }
    
    /**
     * 初始化语音API (使用HTTP而非WebSocket)
     */
    async initSpeechAPI() {
        console.log('初始化豆包1.6-Flash语音模型...');
        
        // 测试语音模型连接
        try {
            const testResponse = await this.callSpeechAPI('你好，测试连接');
            console.log('语音模型连接成功:', testResponse);
        } catch (error) {
            console.error('语音模型连接失败:', error);
            // 语音模型失败不影响整体系统
            console.log('将使用推理模型作为语音备选');
        }
    }
    
    /**
     * 生成语音响应
     */
    async generateSpeechResponse(text) {
        // 使用现有的TTS系统
        if (window.digitalHuman && digitalHuman.cameraAudio) {
            digitalHuman.cameraAudio.speak(text);
        } else {
            console.log('语音输出:', text);
        }
    }
    
    /**
     * 启动数据流处理器
     */
    startDataFlowProcessor() {
        console.log('启动多模态数据流处理器');
        
        // 每50ms检查一次数据流
        setInterval(() => {
            this.processDataStreams();
        }, 50);
    }
    
    /**
     * 处理多模态数据流
     */
    processDataStreams() {
        const currentTime = Date.now();
        
        // 检查是否有新的多模态输入
        if (this.hasNewMultiModalInput()) {
            this.sessionState.currentMode = 'processing';
            this.processMultiModalInput(currentTime);
        }
        
        // 检查时间同步
        this.checkTimeSync(currentTime);
    }
    
    /**
     * 检查是否有新的多模态输入
     */
    hasNewMultiModalInput() {
        return this.dataStreams.visual || 
               this.dataStreams.audio || 
               this.dataStreams.text;
    }
    
    /**
     * 处理多模态输入
     */
    async processMultiModalInput(timestamp) {
        console.log('处理多模态输入:', {
            visual: !!this.dataStreams.visual,
            audio: !!this.dataStreams.audio,
            text: !!this.dataStreams.text,
            timestamp: timestamp
        });
        
        // 收集所有输入数据
        const inputData = {
            timestamp: timestamp,
            visual: this.dataStreams.visual,
            audio: this.dataStreams.audio,
            text: this.dataStreams.text,
            context: this.dataStreams.context
        };
        
        // 根据输入类型确定处理策略
        const strategy = this.determineProcessingStrategy(inputData);
        
        // 执行处理策略
        await this.executeProcessingStrategy(strategy, inputData);
        
        // 清理已处理的数据流
        this.clearProcessedStreams();
    }
    
    /**
     * 确定处理策略
     */
    determineProcessingStrategy(inputData) {
        // 语音输入优先级最高
        if (inputData.audio) {
            return {
                type: 'speech_first',
                sequence: ['speech', 'reasoning', 'vision'],
                description: '语音驱动的交互'
            };
        }
        
        // 视觉+文本组合
        if (inputData.visual && inputData.text) {
            return {
                type: 'vision_text_combined',
                sequence: ['vision', 'reasoning'],
                description: '视觉理解+文本推理'
            };
        }
        
        // 纯视觉输入
        if (inputData.visual) {
            return {
                type: 'vision_only',
                sequence: ['vision', 'reasoning'],
                description: '纯视觉分析'
            };
        }
        
        // 纯文本输入
        if (inputData.text) {
            return {
                type: 'text_only',
                sequence: ['reasoning'],
                description: '纯文本推理'
            };
        }
        
        return {
            type: 'idle',
            sequence: [],
            description: '空闲状态'
        };
    }
    
    /**
     * 执行处理策略
     */
    async executeProcessingStrategy(strategy, inputData) {
        console.log('执行处理策略:', strategy.description);
        
        let results = {};
        
        for (const step of strategy.sequence) {
            switch (step) {
                case 'speech':
                    results.speech = await this.processSpeechInput(inputData.audio);
                    break;
                    
                case 'vision':
                    results.vision = await this.processVisionInput(inputData.visual);
                    break;
                    
                case 'reasoning':
                    results.reasoning = await this.processReasoningInput(
                        inputData.text || results.speech?.text || results.vision?.description,
                        {
                            vision_context: results.vision,
                            speech_context: results.speech,
                            history: inputData.context
                        }
                    );
                    break;
            }
        }
        
        // 生成最终响应
        await this.generateFinalResponse(strategy, results, inputData.timestamp);
    }
    
    /**
     * 处理语音输入 (使用HTTP API)
     */
    async processSpeechInput(textInput) {
        console.log('处理语音输入(文本):', textInput);
        
        try {
            const response = await this.callSpeechAPI(textInput);
            return {
                text: textInput,  // 输入文本
                response: response,  // AI回复
                confidence: 1.0,
                timestamp: Date.now()
            };
        } catch (error) {
            console.error('语音API调用失败:', error);
            // 降级到推理模型
            const fallbackResponse = await this.processReasoningInput(textInput);
            return {
                text: textInput,
                response: fallbackResponse.text,
                confidence: 0.8,
                timestamp: Date.now()
            };
        }
    }
    
    /**
     * 调用语音API (HTTP方式)
     */
    async callSpeechAPI(textInput) {
        const payload = {
            model: this.apis.speech.model,
            messages: [
                {
                    role: 'system',
                    content: '你是一个友好的语音助手，请用简洁、自然的语言回复用户。回复要适合语音播放，避免复杂的格式。'
                },
                {
                    role: 'user',
                    content: textInput
                }
            ],
            max_tokens: this.apis.speech.maxTokens,
            temperature: this.apis.speech.temperature
        };
        
        try {
            const headers = {
                'Content-Type': 'application/json'
            };

            // 不在浏览器中发送敏感的 Authorization 头。后端代理应负责在服务器端使用 API Key。
            if (this.apis.speech.apiKey && this.apis.speech.apiKey !== 'YOUR_API_KEY_HERE') {
                console.warn('Warning: speech.apiKey is set in browser; avoid storing API keys client-side.');
                // 如果确实需要（仅开发临时场景），可在受控环境下启用，但默认不添加该头。
                // headers['Authorization'] = `Bearer ${this.apis.speech.apiKey}`;
            }

            const response = await fetch(this.apis.speech.endpoint, {
                method: 'POST',
                headers,
                body: JSON.stringify(payload)
            });
            
            const result = await response.json();
            return result.choices[0].message.content;
            
        } catch (error) {
            console.error('语音API HTTP调用失败:', error);
            throw error;
        }
    }
    
    /**
     * 处理视觉输入
     */
    async processVisionInput(imageData) {
        console.log('处理视觉输入...');
        
        const payload = {
            model: this.apis.vision.model,
            messages: [
                {
                    role: 'user',
                    content: [
                        {
                            type: 'text',
                            text: '请详细描述这张图片的内容，包括人物、物体、动作、场景等信息。如果有文字，请识别出来。'
                        },
                        {
                            type: 'image_url',
                            image_url: {
                                url: imageData
                            }
                        }
                    ]
                }
            ],
            max_tokens: this.apis.vision.maxTokens
        };
        
        try {
            const headers = {
                'Content-Type': 'application/json'
            };
            if (this.apis.vision.apiKey && this.apis.vision.apiKey !== 'YOUR_API_KEY_HERE') {
                console.warn('Warning: vision.apiKey is set in browser; avoid storing API keys client-side.');
                // headers['Authorization'] = `Bearer ${this.apis.vision.apiKey}`;
            }

            const response = await fetch(this.apis.vision.endpoint, {
                method: 'POST',
                headers,
                body: JSON.stringify(payload)
            });
            
            const result = await response.json();
            
            return {
                description: result.choices[0].message.content,
                confidence: result.usage?.completion_tokens || 0,
                timestamp: Date.now()
            };
            
        } catch (error) {
            console.error('视觉API调用失败:', error);
            throw error;
        }
    }
    
    /**
     * 处理推理输入
     */
    async processReasoningInput(textInput, context = {}) {
        console.log('处理推理输入:', textInput);
        
        // 构建系统提示
        let systemPrompt = `你是一个智能的企业客服助手，具备多模态理解能力。`;
        
        if (context.vision_context) {
            systemPrompt += `\n当前视觉场景：${context.vision_context.description}`;
        }
        
        if (context.speech_context) {
            systemPrompt += `\n语音输入：${context.speech_context.text}`;
        }
        
        const payload = {
            model: this.apis.reasoning.model,
            messages: [
                {
                    role: 'system',
                    content: systemPrompt
                },
                ...context.history || [],
                {
                    role: 'user',
                    content: textInput
                }
            ],
            max_tokens: this.apis.reasoning.maxTokens,
            temperature: 0.7
        };
        
        try {
            const response = await fetch(this.apis.reasoning.endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: this.apis.reasoning.model,
                    messages: payload.messages,
                    max_tokens: payload.max_tokens,
                    temperature: payload.temperature
                })
            });
            
            const result = await response.json();
            
            return {
                text: result.choices[0].message.content,
                reasoning_path: result.usage,
                timestamp: Date.now()
            };
            
        } catch (error) {
            console.error('推理API调用失败:', error);
            throw error;
        }
    }
    
    /**
     * 生成最终响应
     */
    async generateFinalResponse(strategy, results, inputTimestamp) {
        console.log('生成最终响应:', strategy.type);
        
        // 计算响应延迟
        const responseLatency = Date.now() - inputTimestamp;
        
        // 构建响应数据
        const response = {
            type: 'multimodal_response',
            strategy: strategy.type,
            results: results,
            latency: responseLatency,
            timestamp: Date.now()
        };
        
        // 确定输出方式
        if (strategy.type === 'speech_first') {
            // 语音优先：生成语音回复
            await this.generateSpeechResponse(results.reasoning?.text || '好的，我明白了。');
        }
        
        // 同时生成3D数字人动作
        await this.generate3DResponse(results);
        
        // 发送到聊天界面
        await this.sendToChatInterface(response);
        
        // 更新对话历史
        this.updateConversationHistory(response);
    }
    
    /**
     * 生成语音响应
     */
    async generateSpeechResponse(text) {
        if (this.speechWebSocket && this.speechWebSocket.readyState === WebSocket.OPEN) {
            this.speechWebSocket.send(JSON.stringify({
                type: 'tts',
                text: text,
                timestamp: Date.now()
            }));
        }
        
        // 同时使用现有的TTS系统作为备份
        if (window.digitalHuman && digitalHuman.cameraAudio) {
            digitalHuman.cameraAudio.speak(text);
        }
    }
    
    /**
     * 生成3D数字人响应
     */
    async generate3DResponse(results) {
        if (!window.digitalHuman || !digitalHuman.avatar) return;
        
        // 根据结果选择合适的表情和动作
        let expression = 'neutral';
        let animation = 'explaining';
        
        if (results.vision) {
            expression = 'thinking';
            animation = 'pointing';
        }
        
        if (results.speech) {
            expression = 'happy';
            animation = 'greeting';
        }
        
        // 应用到3D数字人
        digitalHuman.setExpression(expression);
        digitalHuman.testAnimation(animation);
    }
    
    /**
     * 发送到聊天界面
     */
    async sendToChatInterface(response) {
        const message = this.formatChatMessage(response);
        
        if (window.digitalHuman && digitalHuman.wsClient) {
            digitalHuman.wsClient.addMessageToChat('ai', message);
        }
    }
    
    /**
     * 格式化聊天消息
     */
    formatChatMessage(response) {
        let message = '';
        
        if (response.results.vision) {
            message += `👁️ 视觉分析：${response.results.vision.description}\n\n`;
        }
        
        if (response.results.speech) {
            message += `🎤 语音识别：${response.results.speech.text}\n\n`;
        }
        
        if (response.results.reasoning) {
            message += `🧠 AI回复：${response.results.reasoning.text}`;
        }
        
        return message || '我正在处理您的请求...';
    }
    
    /**
     * 公共接口：处理文本输入
     */
    async processTextInput(text) {
        this.dataStreams.text = text;
        this.dataStreams.timestamp = Date.now();
    }
    
    /**
     * 公共接口：处理图像输入
     */
    async processImageInput(imageData) {
        this.dataStreams.visual = imageData;
        this.dataStreams.timestamp = Date.now();
    }
    
    /**
     * 公共接口：处理音频输入
     */
    async processAudioInput(audioData) {
        this.dataStreams.audio = audioData;
        this.dataStreams.timestamp = Date.now();
    }
    
    /**
     * 处理语音WebSocket响应
     */
    handleSpeechResponse(data) {
        if (this.speechResponseCallback && data.type === 'transcript') {
            this.speechResponseCallback({
                text: data.text,
                confidence: data.confidence,
                timestamp: Date.now()
            });
            this.speechResponseCallback = null;
        }
    }
    
    /**
     * 检查时间同步
     */
    checkTimeSync(currentTime) {
        // 检查各模态数据的时间戳是否同步
        // 如果时间差超过阈值，进行同步调整
    }
    
    /**
     * 清理已处理的数据流
     */
    clearProcessedStreams() {
        this.dataStreams.visual = null;
        this.dataStreams.audio = null;
        this.dataStreams.text = null;
    }
    
    /**
     * 更新对话历史
     */
    updateConversationHistory(response) {
        this.dataStreams.context.push({
            role: 'assistant',
            content: response.results.reasoning?.text || '已处理',
            timestamp: response.timestamp
        });
        
        // 保持历史记录在合理长度
        if (this.dataStreams.context.length > 20) {
            this.dataStreams.context = this.dataStreams.context.slice(-10);
        }
    }
    
    /**
     * 启动响应调度器
     */
    startResponseScheduler() {
        setInterval(() => {
            this.processResponseQueue();
        }, 10); // 10ms检查一次
    }
    
    /**
     * 处理响应队列
     */
    processResponseQueue() {
        // 按优先级处理响应队列
        if (this.responseQueue.high.length > 0) {
            const task = this.responseQueue.high.shift();
            this.executeHighPriorityTask(task);
        } else if (this.responseQueue.medium.length > 0) {
            const task = this.responseQueue.medium.shift();
            this.executeMediumPriorityTask(task);
        } else if (this.responseQueue.low.length > 0) {
            const task = this.responseQueue.low.shift();
            this.executeLowPriorityTask(task);
        }
    }
    
    /**
     * 调用推理API (豆包大模型1.6)
     */
    async callReasoningAPI(prompt) {
        const payload = {
            model: this.apis.reasoning.model,
            messages: [
                {
                    role: 'user',
                    content: prompt
                }
            ],
            max_tokens: this.apis.reasoning.maxTokens,
            temperature: this.apis.reasoning.temperature
        };
        
        try {
            const response = await fetch(this.apis.reasoning.endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: this.apis.reasoning.model,
                    messages: payload.messages,
                    max_tokens: payload.max_tokens,
                    temperature: payload.temperature
                })
            });
            
            if (!response.ok) {
                throw new Error(`API调用失败: ${response.status} ${response.statusText}`);
            }
            
            const result = await response.json();
            return result.choices[0].message.content;
            
        } catch (error) {
            console.error('推理API调用失败:', error);
            throw error;
        }
    }
    
    /**
     * 执行高优先级任务 (语音相关)
     */
    executeHighPriorityTask(task) {
        console.log('执行高优先级任务:', task);
        // 语音响应处理逻辑
    }
    
    /**
     * 执行中优先级任务 (视觉相关)  
     */
    executeMediumPriorityTask(task) {
        console.log('执行中优先级任务:', task);
        // 视觉分析处理逻辑
    }
    
    /**
     * 执行低优先级任务 (文本推理)
     */
    executeLowPriorityTask(task) {
        console.log('执行低优先级任务:', task);
        // 文本推理处理逻辑
    }
}

// 创建全局多模态API实例
const multiModalAPI = new MultiModalDoubaoAPI();