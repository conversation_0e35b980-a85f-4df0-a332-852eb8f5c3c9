<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业数字人客服系统</title>
    
    <!-- 配置文件 -->
    <script src="digital-human-config.js"></script>
    
    <!-- 使用正确的Three.js ES模块 -->
    <script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.160.0/build/three.module.js",
            "three/addons/": "https://unpkg.com/three@0.160.0/examples/jsm/"
        }
    }
    </script>
    <script type="module">
        import * as THREE from 'three';
        import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js';
        
        // 将THREE和GLTFLoader暴露到全局
        window.THREE = THREE;
        window.GLTFLoader = GLTFLoader;
        
        // 确保加载完成后触发事件
        window.dispatchEvent(new CustomEvent('three-loaded', {
            detail: { THREE, GLTFLoader }
        }));
        
        console.log('✅ Three.js ES模块加载完成', { 
            THREE: !!THREE, 
            GLTFLoader: !!GLTFLoader,
            version: THREE.REVISION 
        });
    </script>
    <!-- 三栏布局样式 -->
    <link rel="stylesheet" href="three-column-layout.css">
</head>
<body>
    <!-- 数字人客服系统 - 三栏布局 -->
    <div id="digital-human-widget">
        <!-- 顶部标题栏 -->
        <div class="widget-header">
            <div class="widget-title">
                ✨ 企业数字人客服系统
            </div>
            <div class="toolbar">
                <button class="toolbar-btn" id="model-selector" title="模型选择">🎯</button>
                <button class="toolbar-btn" id="diagnostics-btn" title="系统诊断">📊</button>
                <button class="toolbar-btn" id="settings-btn" title="设置">⚙️</button>
            </div>
        </div>
        
        <!-- 主内容区 - 三栏 -->
        <div class="main-content">
            <!-- 左侧功能栏 -->
            <div class="left-panel">
                <!-- 表情控制 -->
                <div class="panel-section">
                    <div class="section-title">表情控制</div>
                    <div class="control-group">
                        <button class="control-btn" onclick="digitalHuman.testExpression('happy')">
                            😊 开心
                        </button>
                        <button class="control-btn" onclick="digitalHuman.testExpression('neutral')">
                            😐 中性
                        </button>
                        <button class="control-btn" onclick="digitalHuman.testExpression('thinking')">
                            🤔 思考
                        </button>
                        <button class="control-btn" onclick="digitalHuman.testExpression('concern')">
                            😟 担忧
                        </button>
                    </div>
                </div>
                
                <!-- 动作控制 -->
                <div class="panel-section">
                    <div class="section-title">动作控制</div>
                    <div class="control-group">
                        <button class="control-btn" onclick="digitalHuman.testAnimation('greeting')">
                            👋 打招呼
                        </button>
                        <button class="control-btn" onclick="digitalHuman.testAnimation('pointing')">
                            👉 指向
                        </button>
                        <button class="control-btn" onclick="digitalHuman.testAnimation('nodding')">
                            👍 点头
                        </button>
                        <button class="control-btn" onclick="digitalHuman.testAnimation('shaking')">
                            ❌ 摇头
                        </button>
                    </div>
                </div>
                
                <!-- 模型切换 -->
                <div class="panel-section">
                    <div class="section-title">模型选择</div>
                    <div class="control-group">
                        <button class="control-btn" onclick="digitalHuman.switchTo2DMode && digitalHuman.switchTo2DMode()">
                            🖼️ 2D模式
                        </button>
                        <button class="control-btn" onclick="digitalHuman.switchTo3DMode && digitalHuman.switchTo3DMode()">
                            🎮 3D模式
                        </button>
                    </div>
                </div>
                
                <!-- 特效控制 -->
                <div class="panel-section">
                    <div class="section-title">特效</div>
                    <div class="control-group">
                        <button class="control-btn" onclick="digitalHuman.play2DEffect && digitalHuman.play2DEffect('attention')">
                            ✨ 吸引注意
                        </button>
                        <button class="control-btn" onclick="digitalHuman.play2DEffect && digitalHuman.play2DEffect('success')">
                            ✅ 成功效果
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 中间3D渲染区 -->
            <div class="center-panel">
                <!-- 3D渲染画布 -->
                <canvas id="avatar-canvas"></canvas>
                
                <!-- 2D数字人容器 -->
                <div id="digital-human-2d-container">
                    <img id="digital-human-2d-image" alt="数字人客服" />
                </div>
                
                <!-- 状态指示器 -->
                <div class="status-indicator">
                    <span id="status">初始化中...</span>
                </div>
            </div>
            
            <!-- 右侧聊天栏 -->
            <div class="right-panel">
                <div class="chat-section">
                    <div class="chat-header">
                        <div class="chat-title">
                            💬 AI智能对话
                        </div>
                    </div>
                    <div class="chat-messages" id="messages">
                        <!-- 聊天消息将动态插入这里 -->
                    </div>
                    <div class="chat-input">
                        <input type="text" id="message-input" placeholder="输入您的问题..." />
                        <button id="send-btn" onclick="digitalHuman.sendMessage()">➤</button>
                    </div>
                </div>
            </div>
        </div>
        
    </div>

    <!-- 核心JS脚本 - 精简版本 -->
    <script src="digital-human-config.js"></script>
    <script src="enterprise-model-loader.js"></script>
    <script src="digital-human-core.js"></script>
    <script src="websocket-client.js"></script>
    <script src="camera-audio.js"></script>
    <script src="simple-doubao-api.js"></script>
    
    <script>
        // 初始化3D数字人系统
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                await digitalHuman.init();
                await digitalHuman.initCameraAudio();
                
                // 关键：确保豆包API正确初始化
                if (window.DoubaoAPI) {
                    console.log('✅ 豆包API已加载');
                    digitalHuman.doubaoAPI = new DoubaoAPI();
                } else {
                    console.log('❌ 豆包API未加载');
                }
                
                // 禁用WebSocket连接（不需要后端服务器）
                if (digitalHuman.wsClient) {
                    digitalHuman.wsClient = null;
                    console.log('⚠️ WebSocket已禁用，使用直接API调用');
                }
                
                console.log('3D数字人系统初始化完成');
            } catch (error) {
                console.error('系统初始化失败:', error);
                document.getElementById('status').textContent = '初始化失败';
            }
        });
        
        // 回车发送消息
        document.getElementById('message-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                digitalHuman.sendMessage();
            }
        });
        
        // 发送按钮点击事件
        document.getElementById('send-btn').addEventListener('click', function() {
            digitalHuman.sendMessage();
        });
    </script>
</body>
</html>