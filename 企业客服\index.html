<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业数字人客服系统 - 现代化AI豆包版</title>

    <!-- 配置文件 -->
    <script src="digital-human-config.js"></script>

    <!-- 使用正确的Three.js ES模块 -->
    <script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.160.0/build/three.module.js",
            "three/addons/": "https://unpkg.com/three@0.160.0/examples/jsm/"
        }
    }
    </script>
    <script type="module">
        import * as THREE from 'three';
        import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js';

        // 将THREE和GLTFLoader暴露到全局
        window.THREE = THREE;
        window.GLTFLoader = GLTFLoader;

        // 确保加载完成后触发事件
        window.dispatchEvent(new CustomEvent('three-loaded', {
            detail: { THREE, GLTFLoader }
        }));

        console.log('✅ Three.js ES模块加载完成', {
            THREE: !!THREE,
            GLTFLoader: !!GLTFLoader,
            version: THREE.REVISION
        });
    </script>

    <!-- 现代化样式文件 -->
    <link rel="stylesheet" href="modern-chat-interface.css">
    <link rel="stylesheet" href="three-column-layout.css">
</head>
<body>
    <!-- 数字人客服系统 - 三栏布局 -->
    <div id="digital-human-widget">
        <!-- 顶部标题栏 -->
        <div class="widget-header">
            <div class="widget-title">
                ✨ 企业数字人客服系统
            </div>
            <div class="toolbar">
                <button class="toolbar-btn" id="model-selector" title="模型选择">🎯</button>
                <button class="toolbar-btn" id="model-info-btn" title="模型信息">📊</button>
                <button class="toolbar-btn" id="diagnostics-btn" title="系统诊断">⚙️</button>
                <button class="toolbar-btn" id="settings-btn" title="设置">🔧</button>
            </div>
        </div>
        
        <!-- 主内容区 - 三栏 -->
        <div class="main-content">
            <!-- 左侧功能栏 -->
            <div class="left-panel">
                <!-- 表情控制 -->
                <div class="panel-section">
                    <div class="section-title">表情控制</div>
                    <div class="control-group">
                        <button class="control-btn" onclick="digitalHuman.testExpression('happy')">
                            😊 开心
                        </button>
                        <button class="control-btn" onclick="digitalHuman.testExpression('neutral')">
                            😐 中性
                        </button>
                        <button class="control-btn" onclick="digitalHuman.testExpression('thinking')">
                            🤔 思考
                        </button>
                        <button class="control-btn" onclick="digitalHuman.testExpression('concern')">
                            😟 担忧
                        </button>
                    </div>
                </div>
                
                <!-- 动作控制 -->
                <div class="panel-section">
                    <div class="section-title">动作控制</div>
                    <div class="control-group">
                        <button class="control-btn" onclick="digitalHuman.testAnimation('greeting')">
                            👋 打招呼
                        </button>
                        <button class="control-btn" onclick="digitalHuman.testAnimation('pointing')">
                            👉 指向
                        </button>
                        <button class="control-btn" onclick="digitalHuman.testAnimation('nodding')">
                            👍 点头
                        </button>
                        <button class="control-btn" onclick="digitalHuman.testAnimation('shaking')">
                            ❌ 摇头
                        </button>
                    </div>
                </div>
                
                <!-- 模型切换 -->
                <div class="panel-section">
                    <div class="section-title">3D模型管理</div>
                    <div class="control-group">
                        <button class="control-btn model-primary" onclick="digitalHuman.loadGLBModel('look1-nayong-glb-test/source/look1.glb')">
                            🎯 主要模型
                            <div class="model-details">
                                <div class="model-name">Nayong Look1 (GLB)</div>
                                <div class="model-specs">
                                    • 格式: GLB 2.0<br>
                                    • 骨骼: 92个关节<br>
                                    • 材质: 79个PBR材质<br>
                                    • 贴图: 32个高清纹理<br>
                                    • 动画: 完整面部表情<br>
                                    • 大小: ~35MB
                                </div>
                            </div>
                        </button>
                        <button class="control-btn model-secondary" onclick="digitalHuman.loadGLBModel('look1_nayong_glb_test.glb')">
                            📦 备用模型A
                            <div class="model-details">
                                <div class="model-name">Nayong GLB Variant</div>
                                <div class="model-specs">
                                    • 格式: GLB 2.0<br>
                                    • 优化版本<br>
                                    • 快速加载<br>
                                    • 兼容性强
                                </div>
                            </div>
                        </button>
                        <button class="control-btn model-secondary" onclick="digitalHuman.loadGLBModel('look1_nayong_glb_test (1).glb')">
                            📦 备用模型B
                            <div class="model-details">
                                <div class="model-name">Nayong GLB Copy</div>
                                <div class="model-specs">
                                    • 格式: GLB 2.0<br>
                                    • 测试版本<br>
                                    • 实验特性<br>
                                    • 开发用途
                                </div>
                            </div>
                        </button>
                        <button class="control-btn model-fallback" onclick="digitalHuman.switchTo2DMode && digitalHuman.switchTo2DMode()">
                            🖼️ 2D后备模式
                            <div class="model-details">
                                <div class="model-name">图片模式</div>
                                <div class="model-specs">
                                    • 低性能设备<br>
                                    • 快速响应<br>
                                    • 兼容所有浏览器
                                </div>
                            </div>
                        </button>
                    </div>
                </div>
                
                <!-- 特效控制 -->
                <div class="panel-section">
                    <div class="section-title">特效</div>
                    <div class="control-group">
                        <button class="control-btn" onclick="digitalHuman.play2DEffect && digitalHuman.play2DEffect('attention')">
                            ✨ 吸引注意
                        </button>
                        <button class="control-btn" onclick="digitalHuman.play2DEffect && digitalHuman.play2DEffect('success')">
                            ✅ 成功效果
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 中间3D渲染区 -->
            <div class="center-panel">
                <!-- 3D渲染画布 -->
                <canvas id="avatar-canvas"></canvas>
                
                <!-- 2D数字人容器 -->
                <div id="digital-human-2d-container">
                    <img id="digital-human-2d-image" alt="数字人客服" />
                </div>
                
                <!-- 状态指示器 -->
                <div class="status-indicator">
                    <span id="status">初始化中...</span>
                </div>
                
                <!-- 专业级模型信息面板 -->
                <div class="model-info-panel" id="model-info-panel">
                    <div class="info-header">
                        <h3>🎯 当前模型信息</h3>
                        <button class="close-btn" onclick="this.parentElement.parentElement.style.display='none'">×</button>
                    </div>
                    <div class="info-content" id="model-info-content">
                        <div class="info-row">
                            <span class="info-label">状态:</span>
                            <span class="info-value" id="model-status">未加载</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">格式:</span>
                            <span class="info-value" id="model-format">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">节点数:</span>
                            <span class="info-value" id="model-nodes">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">网格数:</span>
                            <span class="info-value" id="model-meshes">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">材质数:</span>
                            <span class="info-value" id="model-materials">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">贴图数:</span>
                            <span class="info-value" id="model-textures">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">动画数:</span>
                            <span class="info-value" id="model-animations">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">顶点数:</span>
                            <span class="info-value" id="model-vertices">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">BlendShapes:</span>
                            <span class="info-value" id="model-blendshapes">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">骨骼动画:</span>
                            <span class="info-value" id="model-skeleton">-</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">生成器:</span>
                            <span class="info-value" id="model-generator">-</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧现代化聊天栏 -->
            <div class="right-panel">
                <div id="modern-chat-container">
                    <!-- 现代化聊天界面将在这里动态生成 -->
                </div>
            </div>
        </div>
        
    </div>

    <!-- 现代化AI豆包系统脚本 -->
    <!-- 核心系统 -->
    <script src="digital-human-config.js"></script>
    <script src="enterprise-model-loader.js"></script>
    <script src="enterprise-rendering-pipeline.js"></script>
    <script src="enterprise-digital-human-core.js"></script>
    
    <!-- 企业级高级系统 -->
    <script src="advanced-facial-system.js"></script>
    <script src="cinematic-lighting.js"></script>
    <script src="procedural-animation-system.js"></script>
    <script src="performance-optimizer.js"></script>
    
    <!-- 原始兼容系统 -->
    <script src="digital-human-core.js"></script>

    <!-- 现代化聊天系统 -->
    <script src="modern-chat-interface.js"></script>
    <script src="streaming-message-handler.js"></script>
    <script src="intelligent-conversation-manager.js"></script>

    <!-- 多模态交互 -->
    <script src="enhanced-multimodal.js"></script>

    <!-- 高级功能 -->
    <script src="advanced-features.js"></script>

    <!-- 系统检查工具 -->
    <script src="system-check.js"></script>

    <!-- 专业GLB模型加载器 -->
    <script src="professional-glb-loader.js"></script>

    <!-- 兼容性脚本 -->
    <script src="websocket-client.js"></script>
    <script src="camera-audio.js"></script>
    <script src="simple-doubao-api.js"></script>
    
    <script>
        // 现代化AI豆包数字人系统初始化
        let modernChatInterface = null;
        let streamingHandler = null;
        let conversationManager = null;
        let multimodalSystem = null;
        let advancedFeatures = null;

        document.addEventListener('DOMContentLoaded', async function() {
            try {
                console.log('🚀 启动现代化AI豆包数字人系统');

                // 1. 初始化数字人核心系统
                await initializeDigitalHumanCore();

                // 2. 初始化现代化聊天界面
                await initializeModernChatInterface();

                // 3. 初始化智能功能
                await initializeIntelligentFeatures();

                // 4. 集成企业级表情控制
                setupEnterpriseControls();

                console.log('✅ 现代化AI豆包系统初始化完成');
                document.getElementById('status').textContent = '现代化系统就绪';

            } catch (error) {
                console.error('❌ 系统初始化失败:', error);
                document.getElementById('status').textContent = '初始化失败';
            }
        });

        // 初始化数字人核心系统
        async function initializeDigitalHumanCore() {
            console.log('🏢 初始化企业级数字人核心系统');
            
            // 1. 初始化性能优化器
            if (window.PerformanceOptimizer) {
                window.performanceOptimizer = new PerformanceOptimizer();
                console.log('⚡ 性能优化器已启动');
            }
            
            // 2. 优先使用企业级系统
            if (window.enterpriseDigitalHuman) {
                await enterpriseDigitalHuman.init();
                
                // 3. 集成高级面部表情系统
                if (window.AdvancedFacialSystem && enterpriseDigitalHuman.avatar) {
                    window.advancedFacialSystem = new AdvancedFacialSystem();
                    console.log('🎭 高级面部表情系统已启动');
                }
                
                // 4. 集成电影级光照系统
                if (window.CinematicLighting && enterpriseDigitalHuman.renderingSystem) {
                    window.cinematicLighting = new CinematicLighting(
                        enterpriseDigitalHuman.renderingSystem.scene,
                        enterpriseDigitalHuman.renderingSystem.renderer
                    );
                    cinematicLighting.setupRealisticLighting();
                    cinematicLighting.setupImageBasedLighting();
                    console.log('🎬 电影级光照系统已启动');
                }
                
                // 5. 集成程序化动画系统
                if (window.ProceduralAnimationSystem && enterpriseDigitalHuman.avatar) {
                    window.proceduralAnimationSystem = new ProceduralAnimationSystem(enterpriseDigitalHuman.avatar);
                    proceduralAnimationSystem.start();
                    console.log('🤖 程序化动画系统已启动');
                }
                
                console.log('✅ 企业级数字人核心已完全启动');
            } else if (window.digitalHuman) {
                await digitalHuman.init();
                console.log('✅ 标准数字人核心已启动');
            }

            // 初始化摄像头和音频
            if (window.digitalHuman && digitalHuman.initCameraAudio) {
                await digitalHuman.initCameraAudio();
            }
        }

        // 初始化现代化聊天界面
        async function initializeModernChatInterface() {
            const chatContainer = document.getElementById('modern-chat-container');

            if (window.ModernChatInterface) {
                modernChatInterface = new ModernChatInterface(chatContainer);
                console.log('✅ 现代化聊天界面已启动');

                // 初始化流式消息处理
                if (window.StreamingMessageHandler) {
                    streamingHandler = new StreamingMessageHandler(modernChatInterface);
                    console.log('✅ 流式消息处理器已启动');
                }
            }
        }

        // 初始化智能功能
        async function initializeIntelligentFeatures() {
            if (!modernChatInterface) return;

            // 智能对话管理
            if (window.IntelligentConversationManager) {
                conversationManager = new IntelligentConversationManager(modernChatInterface);
                console.log('✅ 智能对话管理器已启动');
            }

            // 多模态交互
            if (window.EnhancedMultimodal) {
                multimodalSystem = new EnhancedMultimodal(modernChatInterface);
                console.log('✅ 增强多模态系统已启动');
            }

            // 高级功能
            if (window.AdvancedFeatures) {
                advancedFeatures = new AdvancedFeatures(modernChatInterface);
                console.log('✅ 高级功能系统已启动');
            }
        }

        // 设置企业级控制集成
        function setupEnterpriseControls() {
            // 保持原有的表情和动画控制功能
            window.digitalHuman = window.digitalHuman || {};

            // 企业级表情控制 - 集成高级面部系统
            window.digitalHuman.testExpression = function(expression) {
                console.log('🎭 企业级表情测试:', expression);

                // 优先使用高级面部表情系统
                if (window.advancedFacialSystem) {
                    const enterpriseMapping = {
                        'happy': 'professional_confidence',
                        'neutral': 'neutral',
                        'thinking': 'thoughtful_consideration',
                        'concern': 'empathetic_concern'
                    };
                    
                    const advancedExpression = enterpriseMapping[expression] || expression;
                    advancedFacialSystem.setExpression(advancedExpression, 1.0, 1000);
                }
                // 备用：使用企业级数字人系统
                else if (window.enterpriseDigitalHuman) {
                    const enterpriseMapping = {
                        'happy': 'professional_smile',
                        'neutral': 'neutral',
                        'thinking': 'attentive_listening',
                        'concern': 'concern_empathy'
                    };

                    const enterpriseExpression = enterpriseMapping[expression] || expression;
                    enterpriseDigitalHuman.testEnterpriseExpression(enterpriseExpression);
                } 
                // 最后备用：原始系统
                else if (window.digitalHuman && digitalHuman.setExpression) {
                    digitalHuman.setExpression(expression);
                }

                // 更新智能指示器
                updateEmotionIndicator(expression);
            };

            // 企业级动画控制 - 集成程序化动画系统
            window.digitalHuman.testAnimation = function(animation) {
                console.log('🎬 企业级动画测试:', animation);

                // 优先使用程序化动画系统
                if (window.proceduralAnimationSystem) {
                    const proceduralMapping = {
                        'greeting': 'greeting_wave',
                        'pointing': 'pointing',
                        'nodding': 'explaining',
                        'explaining': 'explaining'
                    };
                    
                    const proceduralAnim = proceduralMapping[animation];
                    if (proceduralAnim) {
                        proceduralAnimationSystem.playGesture(proceduralAnim, 1.0);
                    }
                }
                // 备用：企业级系统
                else if (window.enterpriseDigitalHuman) {
                    const enterpriseAnimationMapping = {
                        'greeting': 'professional_greeting',
                        'pointing': 'confident_explaining',
                        'nodding': 'understanding_nod',
                        'explaining': 'confident_explaining'
                    };

                    const enterpriseAnim = enterpriseAnimationMapping[animation] || animation;
                    enterpriseDigitalHuman.testEnterpriseAnimation(enterpriseAnim);
                } 
                // 最后备用：原始系统
                else if (window.digitalHuman && digitalHuman.testAnimation) {
                    digitalHuman.testAnimation(animation);
                }
            };

            // 光照控制
            window.digitalHuman.setLightingMood = function(mood) {
                console.log('💡 设置光照情绪:', mood);
                if (window.cinematicLighting) {
                    cinematicLighting.adjustLightingForMood(mood);
                }
            };

            // 性能控制
            window.digitalHuman.setQuality = function(quality) {
                console.log('🎛️ 设置质量级别:', quality);
                if (window.performanceOptimizer) {
                    performanceOptimizer.forceQuality(quality);
                }
            };

            // 启用自适应性能
            window.digitalHuman.enableAdaptiveQuality = function() {
                console.log('🤖 启用自适应质量');
                if (window.performanceOptimizer) {
                    performanceOptimizer.enableAdaptiveQuality();
                }
            };

            // 微表情控制
            window.digitalHuman.setMicroExpressions = function(enabled) {
                console.log('✨ 微表情控制:', enabled);
                if (window.advancedFacialSystem) {
                    advancedFacialSystem.setMicroExpressionsEnabled(enabled);
                }
                if (window.proceduralAnimationSystem) {
                    proceduralAnimationSystem.microExpressionSystem.setEnabled(enabled);
                }
            };

            // 眼球追踪控制
            window.digitalHuman.setEyeTracking = function(enabled, target) {
                console.log('👁️ 眼球追踪控制:', enabled);
                if (window.proceduralAnimationSystem) {
                    if (enabled && target) {
                        proceduralAnimationSystem.setEyeTrackingTarget(target);
                    } else {
                        proceduralAnimationSystem.stopEyeTracking();
                    }
                }
            };

            // 获取性能报告
            window.digitalHuman.getPerformanceReport = function() {
                if (window.performanceOptimizer) {
                    return performanceOptimizer.getPerformanceReport();
                }
                return null;
            };

            // 2D/3D模式切换
            window.digitalHuman.switchTo2DMode = function() {
                console.log('🖼️ 切换到2D模式');
                if (window.enterpriseDigitalHuman && enterpriseDigitalHuman.switchTo2DMode) {
                    enterpriseDigitalHuman.switchTo2DMode();
                }
            };

            window.digitalHuman.switchTo3DMode = function() {
                console.log('🎮 切换到3D模式');
                if (window.enterpriseDigitalHuman && enterpriseDigitalHuman.switchTo3DMode) {
                    enterpriseDigitalHuman.switchTo3DMode();
                }
            };

            // 2D特效
            window.digitalHuman.play2DEffect = function(effect) {
                console.log('✨ 播放2D特效:', effect);
                if (window.enterpriseDigitalHuman && enterpriseDigitalHuman.play2DEffect) {
                    enterpriseDigitalHuman.play2DEffect(effect);
                }
            };
            
            // 模型信息按钮事件
            document.getElementById('model-info-btn').addEventListener('click', function() {
                const panel = document.getElementById('model-info-panel');
                if (panel.style.display === 'block') {
                    panel.style.display = 'none';
                } else {
                    panel.style.display = 'block';
                    updateModelInfoDisplay();
                }
            });
        }
        
        // 更新模型信息显示
        function updateModelInfoDisplay() {
            if (window.professionalGLBLoader && window.professionalGLBLoader.currentModel) {
                const model = window.professionalGLBLoader.currentModel;
                const metadata = model.metadata;
                
                document.getElementById('model-status').textContent = '✅ 已加载';
                document.getElementById('model-format').textContent = metadata.format;
                document.getElementById('model-nodes').textContent = metadata.nodeCount.toLocaleString();
                document.getElementById('model-meshes').textContent = metadata.meshCount.toLocaleString();
                document.getElementById('model-materials').textContent = metadata.materialCount.toLocaleString();
                document.getElementById('model-textures').textContent = metadata.textureCount.toLocaleString();
                document.getElementById('model-animations').textContent = metadata.animationCount.toLocaleString();
                document.getElementById('model-vertices').textContent = metadata.vertexCount.toLocaleString();
                document.getElementById('model-blendshapes').textContent = metadata.hasBlendShapes ? '✅ 支持' : '❌ 不支持';
                document.getElementById('model-skeleton').textContent = metadata.hasSkeleton ? '✅ 支持' : '❌ 不支持';
                document.getElementById('model-generator').textContent = metadata.generator;
            } else {
                document.getElementById('model-status').textContent = '❌ 未加载';
                document.getElementById('model-format').textContent = '-';
                document.getElementById('model-nodes').textContent = '-';
                document.getElementById('model-meshes').textContent = '-';
                document.getElementById('model-materials').textContent = '-';
                document.getElementById('model-textures').textContent = '-';
                document.getElementById('model-animations').textContent = '-';
                document.getElementById('model-vertices').textContent = '-';
                document.getElementById('model-blendshapes').textContent = '-';
                document.getElementById('model-skeleton').textContent = '-';
                document.getElementById('model-generator').textContent = '-';
            }
        }
        
        // 监听GLB模型加载事件
        window.addEventListener('glb-model-loaded', function(event) {
            console.log('🎯 GLB模型加载完成，更新信息面板');
            updateModelInfoDisplay();
        });

        // 更新情感指示器
        function updateEmotionIndicator(emotion) {
            const indicator = document.getElementById('emotion-indicator');
            if (indicator) {
                const emotionEmojis = {
                    'happy': '😊',
                    'neutral': '😐',
                    'thinking': '🤔',
                    'concern': '😟'
                };

                const emoji = emotionEmojis[emotion] || '😐';
                indicator.innerHTML = `${emoji} <span>${emotion}</span>`;
            }
        }

        // 全局错误处理
        window.addEventListener('error', function(event) {
            console.error('全局错误:', event.error);
        });

        window.addEventListener('unhandledrejection', function(event) {
            console.error('未处理的Promise拒绝:', event.reason);
        });
    </script>
</body>
</html>