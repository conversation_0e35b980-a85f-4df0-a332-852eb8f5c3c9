/**
 * 现代化AI豆包系统检查工具
 * 检查新增功能配置情况和系统完整性
 */

class SystemChecker {
    constructor() {
        this.checkResults = {
            core: {},
            modernChat: {},
            streaming: {},
            multimodal: {},
            intelligence: {},
            advanced: {},
            dependencies: {}
        };
        
        this.requiredFeatures = [
            'ModernChatInterface',
            'StreamingMessageHandler', 
            'IntelligentConversationManager',
            'EnhancedMultimodal',
            'AdvancedFeatures'
        ];
        
        this.requiredDependencies = [
            'THREE',
            'GLTFLoader',
            'hljs',
            'katex',
            'Chart',
            'mermaid'
        ];
    }
    
    /**
     * 执行完整系统检查
     */
    async runFullCheck() {
        console.log('🔍 开始系统完整性检查...');
        
        this.checkCoreSystem();
        this.checkModernChatInterface();
        this.checkStreamingFeatures();
        this.checkMultimodalFeatures();
        this.checkIntelligentFeatures();
        this.checkAdvancedFeatures();
        this.checkDependencies();
        this.checkConfiguration();
        
        this.generateReport();
        
        return this.checkResults;
    }
    
    /**
     * 检查核心系统
     */
    checkCoreSystem() {
        console.log('📋 检查核心系统...');
        
        this.checkResults.core = {
            digitalHumanCore: !!window.digitalHuman,
            enterpriseCore: !!window.enterpriseDigitalHuman,
            threeJS: !!window.THREE,
            gltfLoader: !!window.GLTFLoader,
            config: !!window.DIGITAL_HUMAN_CONFIG,
            doubaoAPI: !!window.DoubaoAPI
        };
        
        const coreScore = this.calculateScore(this.checkResults.core);
        console.log(`✅ 核心系统检查完成 (${coreScore}%)`);
    }
    
    /**
     * 检查现代化聊天界面
     */
    checkModernChatInterface() {
        console.log('💬 检查现代化聊天界面...');
        
        this.checkResults.modernChat = {
            modernChatInterface: !!window.ModernChatInterface,
            cssLoaded: this.checkCSSLoaded('modern-chat-interface.css'),
            containerExists: !!document.getElementById('modern-chat-container'),
            eventHandlers: this.checkEventHandlers(),
            responsiveDesign: this.checkResponsiveDesign()
        };
        
        const chatScore = this.calculateScore(this.checkResults.modernChat);
        console.log(`✅ 现代化聊天界面检查完成 (${chatScore}%)`);
    }
    
    /**
     * 检查流式功能
     */
    checkStreamingFeatures() {
        console.log('🔄 检查流式消息功能...');
        
        this.checkResults.streaming = {
            streamingHandler: !!window.StreamingMessageHandler,
            typingIndicator: this.checkTypingIndicator(),
            realTimeRendering: this.checkRealTimeRendering(),
            markdownSupport: this.checkMarkdownSupport(),
            soundEffects: this.checkSoundEffects()
        };
        
        const streamScore = this.calculateScore(this.checkResults.streaming);
        console.log(`✅ 流式消息功能检查完成 (${streamScore}%)`);
    }
    
    /**
     * 检查多模态功能
     */
    checkMultimodalFeatures() {
        console.log('🎯 检查多模态交互功能...');
        
        this.checkResults.multimodal = {
            enhancedMultimodal: !!window.EnhancedMultimodal,
            voiceRecognition: this.checkVoiceRecognition(),
            cameraAccess: this.checkCameraAccess(),
            fileUpload: this.checkFileUpload(),
            screenShare: this.checkScreenShare(),
            gestureRecognition: this.checkGestureRecognition()
        };
        
        const multimodalScore = this.calculateScore(this.checkResults.multimodal);
        console.log(`✅ 多模态功能检查完成 (${multimodalScore}%)`);
    }
    
    /**
     * 检查智能功能
     */
    checkIntelligentFeatures() {
        console.log('🧠 检查智能对话功能...');
        
        this.checkResults.intelligence = {
            conversationManager: !!window.IntelligentConversationManager,
            emotionAnalysis: this.checkEmotionAnalysis(),
            intentRecognition: this.checkIntentRecognition(),
            contextMemory: this.checkContextMemory(),
            personalization: this.checkPersonalization(),
            knowledgeBase: this.checkKnowledgeBase()
        };
        
        const intelligenceScore = this.calculateScore(this.checkResults.intelligence);
        console.log(`✅ 智能功能检查完成 (${intelligenceScore}%)`);
    }
    
    /**
     * 检查高级功能
     */
    checkAdvancedFeatures() {
        console.log('🚀 检查高级功能...');
        
        this.checkResults.advanced = {
            advancedFeatures: !!window.AdvancedFeatures,
            codeHighlighting: this.checkCodeHighlighting(),
            mathRendering: this.checkMathRendering(),
            filePreview: this.checkFilePreview(),
            chartGeneration: this.checkChartGeneration(),
            tableFormatting: this.checkTableFormatting(),
            mermaidDiagrams: this.checkMermaidDiagrams()
        };
        
        const advancedScore = this.calculateScore(this.checkResults.advanced);
        console.log(`✅ 高级功能检查完成 (${advancedScore}%)`);
    }
    
    /**
     * 检查依赖项
     */
    checkDependencies() {
        console.log('📦 检查外部依赖...');
        
        this.checkResults.dependencies = {
            highlightJS: !!window.hljs,
            katex: !!window.katex,
            chartJS: !!window.Chart,
            mermaid: !!window.mermaid,
            webAPIs: this.checkWebAPIs()
        };
        
        const depScore = this.calculateScore(this.checkResults.dependencies);
        console.log(`✅ 依赖项检查完成 (${depScore}%)`);
    }
    
    /**
     * 检查配置
     */
    checkConfiguration() {
        console.log('⚙️ 检查系统配置...');
        
        const config = window.DIGITAL_HUMAN_CONFIG || {};
        
        this.checkResults.configuration = {
            image2D: !!config.image2D,
            models3D: !!(config.models3D && config.models3D.length > 0),
            ui: !!config.ui,
            websocket: !!config.websocket,
            doubaoAPI: !!config.doubaoAPI,
            apiKeySecurity: this.checkAPIKeySecurity(config)
        };
        
        const configScore = this.calculateScore(this.checkResults.configuration);
        console.log(`✅ 配置检查完成 (${configScore}%)`);
    }
    
    /**
     * 辅助检查方法
     */
    checkCSSLoaded(filename) {
        const links = document.querySelectorAll('link[rel="stylesheet"]');
        return Array.from(links).some(link => link.href.includes(filename));
    }
    
    checkEventHandlers() {
        // 检查是否有事件监听器
        return document.getElementById('modern-chat-container') !== null;
    }
    
    checkResponsiveDesign() {
        // 检查响应式设计
        const viewport = document.querySelector('meta[name="viewport"]');
        return !!viewport;
    }
    
    checkTypingIndicator() {
        return document.querySelector('.typing-indicator') !== null ||
               document.querySelector('.streaming-cursor') !== null;
    }
    
    checkRealTimeRendering() {
        return typeof requestAnimationFrame !== 'undefined';
    }
    
    checkMarkdownSupport() {
        // 检查是否支持Markdown渲染
        return true; // 内置支持
    }
    
    checkSoundEffects() {
        return typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined';
    }
    
    checkVoiceRecognition() {
        return 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;
    }
    
    checkCameraAccess() {
        return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
    }
    
    checkFileUpload() {
        return typeof FileReader !== 'undefined';
    }
    
    checkScreenShare() {
        return !!(navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia);
    }
    
    checkGestureRecognition() {
        return !!window.Hands; // MediaPipe Hands
    }
    
    checkEmotionAnalysis() {
        return !!window.EmotionAnalyzer;
    }
    
    checkIntentRecognition() {
        return !!window.IntentRecognizer;
    }
    
    checkContextMemory() {
        return localStorage !== undefined;
    }
    
    checkPersonalization() {
        return !!window.PersonalizationEngine;
    }
    
    checkKnowledgeBase() {
        return !!window.ConversationKnowledgeBase;
    }
    
    checkCodeHighlighting() {
        return !!window.hljs;
    }
    
    checkMathRendering() {
        return !!window.katex;
    }
    
    checkFilePreview() {
        return typeof FileReader !== 'undefined';
    }
    
    checkChartGeneration() {
        return !!window.Chart;
    }
    
    checkTableFormatting() {
        return true; // 内置支持
    }
    
    checkMermaidDiagrams() {
        return !!window.mermaid;
    }
    
    checkWebAPIs() {
        const apis = [
            'fetch',
            'Promise',
            'localStorage',
            'sessionStorage',
            'WebSocket',
            'FileReader',
            'Blob',
            'URL'
        ];
        
        return apis.every(api => typeof window[api] !== 'undefined');
    }
    
    checkAPIKeySecurity(config) {
        if (!config.doubaoAPI) return false;
        
        // 检查是否有硬编码的API密钥
        const hasHardcodedKey = config.doubaoAPI.apiKey && 
                               config.doubaoAPI.apiKey !== 'YOUR_API_KEY_HERE' &&
                               !config.doubaoAPI.apiKey.startsWith('${');
        
        return !hasHardcodedKey; // 返回true表示安全（没有硬编码）
    }
    
    /**
     * 计算分数
     */
    calculateScore(results) {
        const total = Object.keys(results).length;
        const passed = Object.values(results).filter(Boolean).length;
        return Math.round((passed / total) * 100);
    }
    
    /**
     * 生成检查报告
     */
    generateReport() {
        console.log('\n📊 系统检查报告');
        console.log('='.repeat(50));
        
        const categories = [
            { name: '核心系统', key: 'core' },
            { name: '现代化聊天', key: 'modernChat' },
            { name: '流式消息', key: 'streaming' },
            { name: '多模态交互', key: 'multimodal' },
            { name: '智能功能', key: 'intelligence' },
            { name: '高级功能', key: 'advanced' },
            { name: '外部依赖', key: 'dependencies' },
            { name: '系统配置', key: 'configuration' }
        ];
        
        let totalScore = 0;
        
        categories.forEach(category => {
            const results = this.checkResults[category.key];
            const score = this.calculateScore(results);
            totalScore += score;
            
            const status = score >= 80 ? '✅' : score >= 60 ? '⚠️' : '❌';
            console.log(`${status} ${category.name}: ${score}%`);
            
            // 显示失败的项目
            Object.entries(results).forEach(([key, value]) => {
                if (!value) {
                    console.log(`   ❌ ${key}`);
                }
            });
        });
        
        const overallScore = Math.round(totalScore / categories.length);
        console.log('='.repeat(50));
        console.log(`🎯 总体评分: ${overallScore}%`);
        
        if (overallScore >= 90) {
            console.log('🎉 系统状态优秀！所有功能运行正常。');
        } else if (overallScore >= 70) {
            console.log('👍 系统状态良好，建议修复部分问题。');
        } else {
            console.log('⚠️ 系统存在问题，需要立即修复。');
        }
        
        // 生成修复建议
        this.generateFixSuggestions();
    }
    
    /**
     * 生成修复建议
     */
    generateFixSuggestions() {
        console.log('\n🔧 修复建议:');
        
        const suggestions = [];
        
        // 检查核心问题
        if (!this.checkResults.core.doubaoAPI) {
            suggestions.push('- 检查豆包API配置和网络连接');
        }
        
        if (!this.checkResults.dependencies.highlightJS) {
            suggestions.push('- 加载highlight.js库以支持代码高亮');
        }
        
        if (!this.checkResults.dependencies.katex) {
            suggestions.push('- 加载KaTeX库以支持数学公式渲染');
        }
        
        if (!this.checkResults.multimodal.voiceRecognition) {
            suggestions.push('- 在HTTPS环境下使用以启用语音识别');
        }
        
        if (!this.checkResults.configuration.apiKeySecurity) {
            suggestions.push('- 移除硬编码的API密钥，使用环境变量');
        }
        
        if (suggestions.length === 0) {
            console.log('✅ 无需修复，系统运行良好！');
        } else {
            suggestions.forEach(suggestion => console.log(suggestion));
        }
    }
    
    /**
     * 导出检查结果
     */
    exportResults() {
        const report = {
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            results: this.checkResults,
            summary: {
                totalCategories: Object.keys(this.checkResults).length,
                overallScore: this.calculateOverallScore()
            }
        };
        
        const blob = new Blob([JSON.stringify(report, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `system-check-${Date.now()}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
        console.log('📄 检查报告已导出');
    }
    
    calculateOverallScore() {
        const categories = Object.keys(this.checkResults);
        const totalScore = categories.reduce((sum, key) => {
            return sum + this.calculateScore(this.checkResults[key]);
        }, 0);
        
        return Math.round(totalScore / categories.length);
    }
}

// 自动运行系统检查
document.addEventListener('DOMContentLoaded', function() {
    // 延迟执行，确保所有脚本都已加载
    setTimeout(async () => {
        const checker = new SystemChecker();
        const results = await checker.runFullCheck();
        
        // 将检查器暴露到全局，方便手动调用
        window.systemChecker = checker;
        
        console.log('💡 提示: 使用 systemChecker.exportResults() 导出详细报告');
    }, 3000);
});

// 导出类
window.SystemChecker = SystemChecker;
