/**
 * 企业级模型加载器
 * Linus设计哲学：一次编写，永远可靠
 * 
 * 解决所有模型加载问题的终极方案
 */

class EnterpriseModelLoader {
    constructor() {
        this.isReady = false;
        this.loadQueue = [];
        this.loadedModels = new Map();
        this.loadingPromises = new Map();
        
        // 2D默认图片 - 从配置文件获取
        this.default2DImage = this.getImageFromConfig();
        
        this.init();
    }
    
    /**
     * 从配置文件获取图片路径
     */
    getImageFromConfig() {
        if (window.DIGITAL_HUMAN_CONFIG && window.DIGITAL_HUMAN_CONFIG.image2D) {
            return window.DIGITAL_HUMAN_CONFIG.image2D.primaryPath;
        }
        // 默认备用图片
        return './digital-human.jpg';
    }
    
    async init() {
        console.log('🚀 初始化企业级模型加载器');
        
        // 等待Three.js加载完成
        await this.waitForThreeJS();
        
        // 立即加载2D模型
        await this.load2DModel();
        
        this.isReady = true;
        console.log('✅ 企业级模型加载器就绪');
        
        // 处理加载队列
        this.processQueue();
    }
    
    /**
     * 等待Three.js加载完成
     */
    async waitForThreeJS() {
        return new Promise((resolve) => {
            // 如果已经加载，直接返回
            if (window.THREE && window.GLTFLoader) {
                console.log('✅ Three.js已就绪');
                resolve();
                return;
            }
            
            // 监听three-loaded事件
            window.addEventListener('three-loaded', () => {
                console.log('✅ Three.js事件加载完成');
                resolve();
            });
            
            // 备用检查机制
            let attempts = 0;
            const checkInterval = setInterval(() => {
                attempts++;
                console.log(`🔍 检查Three.js状态 (尝试 ${attempts}/20)`, {
                    THREE: !!window.THREE,
                    GLTFLoader: !!window.GLTFLoader
                });
                
                if (window.THREE && window.GLTFLoader) {
                    clearInterval(checkInterval);
                    console.log('✅ Three.js轮询检查完成');
                    resolve();
                } else if (attempts >= 20) {
                    clearInterval(checkInterval);
                    console.error('❌ Three.js加载超时');
                    resolve(); // 即使失败也继续，使用降级方案
                }
            }, 500);
        });
    }
    
    /**
     * 加载2D模型 - 保证成功
     */
    async load2DModel() {
        console.log('📸 开始加载2D数字人模型');
        
        try {
            // 创建2D容器和图片
            await this.create2DContainer();
            
            console.log('✅ 2D数字人模型加载成功');
            
            // 立即显示2D模式
            this.switchTo2DMode();
            
            // 更新状态
            this.updateStatus('2D数字人就绪');
            
            return true;
        } catch (error) {
            console.error('❌ 2D模型加载失败:', error);
            return false;
        }
    }
    
    /**
     * 创建2D容器 - 适配现有HTML结构
     */
    async create2DContainer() {
        const widget = document.getElementById('digital-human-widget');
        if (!widget) {
            throw new Error('数字人组件容器未找到');
        }
        
        // 检查是否已存在2D容器
        let container = document.getElementById('digital-human-2d-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'digital-human-2d-container';
            
            // 使用更高的z-index和固定定位以确保可见性
            container.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                z-index: 9999;
                flex-direction: column;
                pointer-events: auto;
            `;
            
            // 直接插入到body中以确保最高优先级显示
            console.log('🎯 插入2D容器到body根部确保可见');
            document.body.appendChild(container);
            
            // 同时在widget中也创建一个相对定位的容器作为备用
            const fallbackContainer = document.createElement('div');
            fallbackContainer.id = 'digital-human-2d-fallback';
            fallbackContainer.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                display: none;
                align-items: center;
                justify-content: center;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                z-index: 50;
                flex-direction: column;
            `;
            widget.appendChild(fallbackContainer);
        }
        
        // 创建关闭按钮
        let closeBtn = container.querySelector('.close-2d-btn');
        if (!closeBtn) {
            closeBtn = document.createElement('button');
            closeBtn.className = 'close-2d-btn';
            closeBtn.style.cssText = `
                position: absolute;
                top: 20px;
                right: 20px;
                background: rgba(255,255,255,0.2);
                border: none;
                color: white;
                font-size: 24px;
                width: 40px;
                height: 40px;
                border-radius: 20px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
                z-index: 10000;
            `;
            closeBtn.innerHTML = '✕';
            closeBtn.title = '关闭2D数字人';
            closeBtn.onclick = () => {
                console.log('🔄 关闭2D全屏模式');
                container.style.display = 'none';
                // 显示备用容器
                const fallback = document.getElementById('digital-human-2d-fallback');
                if (fallback) {
                    fallback.style.display = 'flex';
                }
            };
            container.appendChild(closeBtn);
        }
        
        // 创建提示文本
        let textInfo = container.querySelector('.digital-human-info');
        if (!textInfo) {
            textInfo = document.createElement('div');
            // 为三栏布局优化文字信息显示
            textInfo.className = 'digital-human-info';
            textInfo.style.cssText = `
                position: absolute;
                top: 20px;
                left: 20px;
                color: white;
                background: rgba(0, 0, 0, 0.8);
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 600;
                z-index: 100;
                max-width: 300px;
                backdrop-filter: blur(10px);
            `;
            textInfo.innerHTML = `
                <div style="margin-bottom: 8px;">🤖 AI客服助手</div>
                <div style="font-size: 14px; opacity: 0.8;">2D数字人已就绪</div>
                <div style="font-size: 12px; opacity: 0.6; margin-top: 8px;">
                    点击右上角 ✕ 关闭全屏，或通过控制面板切换模式
                </div>
            `;
            container.appendChild(textInfo);
        }
        
        // 创建2D图片元素
        let image = document.getElementById('digital-human-2d-image');
        if (!image) {
            image = document.createElement('img');
            image.id = 'digital-human-2d-image';
            image.alt = '企业数字人客服';
            image.style.cssText = `
                width: 300px;
                height: 400px;
                object-fit: cover;
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.4);
                transition: all 0.3s ease;
                border: 3px solid rgba(255,255,255,0.3);
                position: relative;
                z-index: 10001;
                background: white;
                cursor: pointer;
            `;
            
            // 添加鼠标悬停效果
            image.onmouseover = () => {
                image.style.transform = 'scale(1.02)';
                image.style.boxShadow = '0 12px 40px rgba(0,0,0,0.5)';
            };
            image.onmouseout = () => {
                image.style.transform = 'scale(1)';
                image.style.boxShadow = '0 8px 32px rgba(0,0,0,0.4)';
            };
            
            container.appendChild(image);
        }
        
        // 设置图片源
        return new Promise((resolve) => {
            image.onload = () => {
                console.log('✅ 2D图片加载成功 - 图片已插入DOM并可见');
                console.log('📊 图片状态:', {
                    naturalWidth: image.naturalWidth,
                    naturalHeight: image.naturalHeight,
                    displayWidth: image.offsetWidth,
                    displayHeight: image.offsetHeight,
                    visible: image.offsetParent !== null
                });
                if (textInfo) {
                    textInfo.querySelector('div:nth-child(2)').textContent = '2D数字人已加载';
                }
                resolve();
            };
            
            image.onerror = () => {
                console.warn('⚠️ 主图片加载失败，尝试备用方案');
                
                // 从配置文件获取备用路径
                let fallbackPaths = ['./digital-human.png', './digital-human.webp'];
                if (window.DIGITAL_HUMAN_CONFIG && window.DIGITAL_HUMAN_CONFIG.image2D && window.DIGITAL_HUMAN_CONFIG.image2D.fallbackPaths) {
                    fallbackPaths = window.DIGITAL_HUMAN_CONFIG.image2D.fallbackPaths;
                }
                
                let currentIndex = 0;
                const tryNextPath = () => {
                    if (currentIndex < fallbackPaths.length) {
                        const nextPath = fallbackPaths[currentIndex];
                        console.log(`🔄 尝试备用路径: ${nextPath}`);
                        image.src = nextPath;
                        currentIndex++;
                    } else {
                        console.log('⚠️ 所有路径都失败，显示占位符');
                        image.style.display = 'none';
                        this.createImagePlaceholder(container);
                        resolve();
                    }
                };
                
                // 重新设置错误处理器以尝试下一个路径
                image.onerror = tryNextPath;
                tryNextPath();
            };
            
            // 设置图片源
            console.log('📸 设置2D图片源...', this.default2DImage.substring(0, 50) + '...');
            image.src = this.default2DImage;
            
            // 强制显示容器
            container.style.display = 'flex';
            
            // 备用超时机制
            setTimeout(() => {
                console.log('⏰ 2D图片加载超时检查');
                console.log('📊 容器状态:', {
                    display: container.style.display,
                    visible: container.offsetParent !== null,
                    width: container.offsetWidth,
                    height: container.offsetHeight
                });
                if (!image.complete || !image.naturalWidth) {
                    console.log('🔧 图片未完全加载，创建占位符');
                    this.createImagePlaceholder(container);
                }
                resolve();
            }, 3000);
        });
    }
    
    /**
     * 创建图片占位符
     */
    createImagePlaceholder(container) {
        let placeholder = container.querySelector('.image-placeholder');
        if (!placeholder) {
            placeholder = document.createElement('div');
            placeholder.className = 'image-placeholder';
            placeholder.style.cssText = `
                width: 250px;
                height: 320px;
                background: rgba(255,255,255,0.15);
                border: 2px dashed rgba(255,255,255,0.4);
                border-radius: 12px;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;
                color: rgba(255,255,255,0.9);
                font-size: 48px;
                text-align: center;
                position: relative;
                z-index: 10001;
                animation: pulse 2s infinite;
            `;
            placeholder.innerHTML = `
                <div style="animation: bounce 2s infinite;">🤖</div>
                <div style="font-size: 16px; margin-top: 15px; font-weight: bold;">2D数字人</div>
                <div style="font-size: 12px; margin-top: 5px; opacity: 0.8;">准备就绪</div>
            `;
            
            // 添加CSS动画
            const style = document.createElement('style');
            style.textContent = `
                @keyframes pulse {
                    0%, 100% { opacity: 0.8; transform: scale(1); }
                    50% { opacity: 1; transform: scale(1.05); }
                }
                @keyframes bounce {
                    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
                    40% { transform: translateY(-10px); }
                    60% { transform: translateY(-5px); }
                }
            `;
            document.head.appendChild(style);
            
            container.appendChild(placeholder);
        }
        console.log('🖼️ 创建2D图片占位符 - 高可见性版本');
    }
    
    /**
     * 加载3D模型
     */
    async load3DModel(modelPath) {
        console.log('🎮 开始加载3D模型:', modelPath);
        
        // 检查Three.js是否可用
        if (!window.THREE || !window.GLTFLoader) {
            console.error('❌ Three.js未加载，无法加载3D模型');
            throw new Error('Three.js未加载');
        }
        
        // 检查是否已经在加载
        if (this.loadingPromises.has(modelPath)) {
            console.log('⏳ 模型正在加载中，返回现有Promise');
            return this.loadingPromises.get(modelPath);
        }
        
        // 检查是否已经加载过
        if (this.loadedModels.has(modelPath)) {
            console.log('📦 使用缓存的模型');
            return this.loadedModels.get(modelPath);
        }
        
        // 创建加载Promise
        const loadPromise = this.doLoad3DModel(modelPath);
        this.loadingPromises.set(modelPath, loadPromise);
        
        try {
            const result = await loadPromise;
            this.loadedModels.set(modelPath, result);
            return result;
        } catch (error) {
            console.error('❌ 3D模型加载失败:', error);
            throw error;
        } finally {
            this.loadingPromises.delete(modelPath);
        }
    }
    
    /**
     * 实际执行3D模型加载
     */
    async doLoad3DModel(modelPath) {
        const loader = new window.GLTFLoader();
        
        return new Promise((resolve, reject) => {
            console.log('📁 开始下载GLB文件:', modelPath);
            
            loader.load(
                modelPath,
                (gltf) => {
                    console.log('✅ GLB文件加载成功:', modelPath);
                    console.log('📊 模型信息:', {
                        场景对象: gltf.scene.children.length,
                        动画数量: gltf.animations ? gltf.animations.length : 0,
                        文件路径: modelPath
                    });
                    resolve(gltf);
                },
                (progress) => {
                    const percent = Math.round((progress.loaded / progress.total) * 100);
                    console.log(`📥 下载进度: ${percent}% (${progress.loaded}/${progress.total})`);
                    this.updateStatus(`加载3D模型: ${percent}%`);
                },
                (error) => {
                    console.error('❌ GLB文件加载失败:', error);
                    reject(error);
                }
            );
        });
    }
    
    /**
     * 切换到2D模式
     */
    switchTo2DMode() {
        console.log('🔄 切换到2D模式');
        
        const canvas = document.getElementById('avatar-canvas');
        const container = document.getElementById('digital-human-2d-container');
        
        if (canvas) {
            canvas.style.display = 'none';
        }
        
        if (container) {
            container.style.display = 'flex';
        }
        
        // 更新按钮状态
        const toggleBtn = document.querySelector('.model-toggle-btn');
        if (toggleBtn) {
            toggleBtn.textContent = '3D';
        }
        
        console.log('✅ 已切换到2D模式');
    }
    
    /**
     * 切换到3D模式
     */
    switchTo3DMode() {
        console.log('🔄 切换到3D模式');
        
        const canvas = document.getElementById('avatar-canvas');
        const container = document.getElementById('digital-human-2d-container');
        
        if (canvas) {
            canvas.style.display = 'block';
        }
        
        if (container) {
            container.style.display = 'none';
        }
        
        // 更新按钮状态
        const toggleBtn = document.querySelector('.model-toggle-btn');
        if (toggleBtn) {
            toggleBtn.textContent = '2D';
        }
        
        console.log('✅ 已切换到3D模式');
    }
    
    /**
     * 添加到加载队列
     */
    queueLoad(task) {
        if (this.isReady) {
            return task();
        } else {
            this.loadQueue.push(task);
        }
    }
    
    /**
     * 处理加载队列
     */
    processQueue() {
        while (this.loadQueue.length > 0) {
            const task = this.loadQueue.shift();
            try {
                task();
            } catch (error) {
                console.error('队列任务执行失败:', error);
            }
        }
    }
    
    /**
     * 更新状态
     */
    updateStatus(message) {
        const status = document.getElementById('status');
        if (status) {
            status.textContent = message;
        }
        console.log('📊', message);
    }
    
    /**
     * 诊断模型加载状态
     */
    diagnose() {
        console.log('🔍 模型加载器诊断报告');
        console.log('================');
        console.log('Three.js状态:', {
            THREE: !!window.THREE,
            GLTFLoader: !!window.GLTFLoader,
            version: window.THREE ? window.THREE.REVISION : 'N/A'
        });
        console.log('加载器状态:', {
            isReady: this.isReady,
            queueLength: this.loadQueue.length,
            loadedModels: this.loadedModels.size,
            loadingPromises: this.loadingPromises.size
        });
        console.log('2D模型状态:', {
            container: !!document.getElementById('digital-human-2d-container'),
            image: !!document.getElementById('digital-human-2d-image')
        });
        console.log('================');
    }
}

// 创建全局模型加载器
window.enterpriseModelLoader = new EnterpriseModelLoader();

// 替换原有的模型加载逻辑
document.addEventListener('DOMContentLoaded', function() {
    console.log('📱 企业级模型加载器已启动');
    
    // 5秒后运行诊断
    setTimeout(() => {
        if (window.enterpriseModelLoader) {
            window.enterpriseModelLoader.diagnose();
        }
    }, 5000);
});

console.log('🏭 企业级模型加载器脚本加载完成');