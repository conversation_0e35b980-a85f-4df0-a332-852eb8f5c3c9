/**
 * 3D数字人演示脚本
 * 展示系统的完整功能
 */

// 扩展DigitalHuman添加演示功能
DigitalHuman.prototype.startDemo = function() {
    console.log('开始3D数字人功能演示');
    this.updateStatus('演示模式');
    
    // 演示序列
    const demoSequence = [
        { delay: 1000, action: () => this.demoGreeting() },
        { delay: 3000, action: () => this.demoExpressions() },
        { delay: 6000, action: () => this.demoAnimations() },
        { delay: 10000, action: () => this.demoSpeech() },
        { delay: 15000, action: () => this.demoComplete() }
    ];
    
    demoSequence.forEach(step => {
        setTimeout(step.action, step.delay);
    });
};

DigitalHuman.prototype.demoGreeting = function() {
    this.updateStatus('演示：打招呼');
    this.setExpression('happy');
    this.testAnimation('greeting');
    
    if (this.cameraAudio) {
        this.speak('你好！欢迎使用3D数字人客服系统！我是你的专属AI助手。');
    }
};

DigitalHuman.prototype.demoExpressions = function() {
    this.updateStatus('演示：表情变化');
    
    const expressions = ['happy', 'thinking', 'concern', 'neutral'];
    let index = 0;
    
    const showNextExpression = () => {
        if (index < expressions.length) {
            this.setExpression(expressions[index]);
            console.log('展示表情:', expressions[index]);
            index++;
            setTimeout(showNextExpression, 800);
        }
    };
    
    showNextExpression();
};

DigitalHuman.prototype.demoAnimations = function() {
    this.updateStatus('演示：动作展示');
    
    const animations = ['pointing', 'explaining', 'thinking'];
    let index = 0;
    
    const showNextAnimation = () => {
        if (index < animations.length) {
            this.testAnimation(animations[index]);
            console.log('展示动作:', animations[index]);
            index++;
            setTimeout(showNextAnimation, 1500);
        }
    };
    
    showNextAnimation();
};

DigitalHuman.prototype.demoSpeech = function() {
    this.updateStatus('演示：语音功能');
    this.setExpression('happy');
    
    if (this.cameraAudio) {
        this.speak('现在我来展示语音功能。我可以通过TTS技术将文字转换为语音，并且具备口型同步功能。');
    }
};

DigitalHuman.prototype.demoComplete = function() {
    this.updateStatus('演示完成');
    this.setExpression('happy');
    this.testAnimation('greeting');
    
    if (this.cameraAudio) {
        this.speak('演示完成！现在你可以开始与我对话了。');
    }
    
    setTimeout(() => {
        this.updateStatus('系统就绪');
    }, 3000);
};

// 测试WebSocket连接的演示函数
function testWebSocketConnection() {
    if (digitalHuman.wsClient && digitalHuman.wsClient.isConnected) {
        console.log('WebSocket连接正常');
        digitalHuman.updateStatus('连接测试成功');
        
        // 发送测试消息
        digitalHuman.wsClient.sendAIChatRequest('请介绍一下你自己');
        
    } else {
        console.log('WebSocket连接失败');
        digitalHuman.updateStatus('连接测试失败');
        
        // 模拟本地响应用于演示
        setTimeout(() => {
            const mockResponse = {
                response: '我是3D数字人客服助手，可以为您提供专业的服务支持。',
                animation: 'explaining',
                expression: 'happy'
            };
            
            digitalHuman.wsClient.handleAIChatResponse(mockResponse);
        }, 1000);
    }
}

// 添加到HTML控制面板
function addDemoControls() {
    const controls = document.getElementById('controls');
    if (controls) {
        controls.innerHTML += `
            <br>
            <button onclick="digitalHuman.startDemo()">🎬 演示</button>
            <button onclick="testWebSocketConnection()">🔌 测试连接</button>
            <button onclick="digitalHuman.cameraAudio?.showCameraFeed()">📹 显示摄像头</button>
        `;
    }
}

// 页面加载完成后添加演示控制
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(addDemoControls, 1000);
});

// 错误处理和降级方案
function handleSystemError(error) {
    console.error('系统错误:', error);
    
    const statusEl = document.getElementById('status');
    if (statusEl) {
        statusEl.textContent = '系统错误，启用降级模式';
        statusEl.style.color = '#ff4444';
    }
    
    // 显示错误信息给用户
    const messagesEl = document.getElementById('messages');
    if (messagesEl) {
        const errorMsg = document.createElement('div');
        errorMsg.className = 'message ai';
        errorMsg.textContent = '抱歉，3D渲染功能暂时不可用，但聊天功能正常。';
        errorMsg.style.color = '#ff4444';
        messagesEl.appendChild(errorMsg);
    }
    
    // 启用纯文本模式
    enableTextOnlyMode();
}

function enableTextOnlyMode() {
    console.log('启用纯文本模式');
    
    // 隐藏3D画布
    const canvas = document.getElementById('avatar-canvas');
    if (canvas) {
        canvas.style.display = 'none';
    }
    
    // 显示替代信息
    const widget = document.getElementById('digital-human-widget');
    if (widget) {
        const fallbackDiv = document.createElement('div');
        fallbackDiv.innerHTML = `
            <div style="padding: 20px; text-align: center; background: #f8f9fa; height: 350px; display: flex; align-items: center; justify-content: center; flex-direction: column;">
                <h3 style="color: #666;">🤖 AI客服助手</h3>
                <p style="color: #888;">3D功能暂时不可用<br>聊天功能正常运行</p>
            </div>
        `;
        
        widget.insertBefore(fallbackDiv, widget.firstChild);
    }
}

// 全局错误处理
window.addEventListener('error', handleSystemError);
window.addEventListener('unhandledrejection', function(event) {
    handleSystemError(event.reason);
});

console.log('3D数字人演示脚本加载完成');