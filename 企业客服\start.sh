#!/bin/bash

echo "========================================"
echo "企业数字人客服系统 - 启动脚本 (Linux/Mac)"
echo "========================================"
echo

# 加载环境变量
if [ -f .env ]; then
    source .env
    echo "✅ 环境变量已加载"
else
    echo "⚠️ 未找到.env文件，请先运行 ./install.sh"
    exit 1
fi

echo
echo "[1/2] 启动火山引擎API代理服务器..."
python3 doubao_server.py &
API_PID=$!
echo "✅ API代理服务器启动中 (PID: $API_PID, 端口 5000)"

echo
echo "[2/2] 启动HTTP文件服务器..."
python3 -m http.server 8000 &
HTTP_PID=$!
echo "✅ 文件服务器启动中 (PID: $HTTP_PID, 端口 8000)"

echo
echo "========================================"
echo "✅ 所有服务已启动！"
echo
echo "请在浏览器中访问:"
echo "http://localhost:8000/index-three-column.html"
echo
echo "停止服务: Ctrl+C 或运行 kill $API_PID $HTTP_PID"
echo "========================================"
echo
echo "3秒后自动打开浏览器..."
sleep 3

# 根据操作系统打开浏览器
if [[ "$OSTYPE" == "darwin"* ]]; then
    open http://localhost:8000/index-three-column.html
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    xdg-open http://localhost:8000/index-three-column.html
fi

# 等待用户按键
echo
echo "按 Ctrl+C 停止所有服务..."
wait