@echo off
echo ========================================
echo 企业数字人客服系统 - 启动脚本
echo ========================================
echo.

:: 加载环境变量
if exist .env (
    for /f "tokens=*" %%a in (.env) do set %%a
    echo ✅ 环境变量已加载
) else (
    echo ⚠️ 未找到.env文件，请先运行install.bat
    pause
    exit /b 1
)

echo.
echo [1/2] 启动火山引擎API代理服务器...
start "API代理服务器" cmd /k "python doubao_server.py"
echo ✅ API代理服务器启动中 (端口 5000)

echo.
echo [2/2] 启动HTTP文件服务器...
echo 正在8000端口启动文件服务器...
start "文件服务器" cmd /k "python -m http.server 8000"
echo ✅ 文件服务器启动中 (端口 8000)

echo.
echo ========================================
echo ✅ 所有服务已启动！
echo.
echo 请在浏览器中访问:
echo http://localhost:8000/index-three-column.html
echo.
echo 注意事项:
echo - 确保端口 5000 和 8000 未被占用
echo - 如需停止服务，关闭弹出的命令行窗口
echo ========================================
echo.
echo 3秒后自动打开浏览器...
timeout /t 3 >nul
start http://localhost:8000/index-three-column.html

pause