/**
 * 现代化AI豆包聊天界面
 * 采用最新的Web技术和设计模式
 */

class ModernChatInterface {
    constructor(container) {
        this.container = container;
        this.messages = [];
        this.isTyping = false;
        this.currentUser = 'user';
        this.messageId = 0;
        
        // 配置选项
        this.options = {
            enableTypingIndicator: true,
            enableQuickReplies: true,
            enableMessageActions: true,
            enableVoiceInput: true,
            enableFileUpload: true,
            maxMessages: 1000,
            typingDelay: 1000,
            autoScroll: true
        };
        
        // 事件监听器
        this.eventListeners = new Map();
        
        this.init();
    }
    
    /**
     * 初始化聊天界面
     */
    init() {
        this.createChatInterface();
        this.bindEvents();
        this.setupQuickReplies();
        this.setupVoiceInput();
        this.setupFileUpload();
        
        console.log('✅ 现代化聊天界面初始化完成');
    }
    
    /**
     * 创建聊天界面HTML结构
     */
    createChatInterface() {
        this.container.innerHTML = `
            <div class="modern-chat-container">
                <!-- 聊天头部 -->
                <div class="modern-chat-header">
                    <div class="chat-title-modern">
                        <div class="ai-status-indicator"></div>
                        <span>🤖 AI豆包助手</span>
                    </div>
                    <div class="chat-actions">
                        <button class="action-button" data-action="clear" title="清空对话">
                            🗑️
                        </button>
                        <button class="action-button" data-action="export" title="导出对话">
                            📤
                        </button>
                        <button class="action-button" data-action="settings" title="设置">
                            ⚙️
                        </button>
                    </div>
                </div>
                
                <!-- 消息区域 -->
                <div class="modern-messages-container" id="messages-container">
                    <!-- 欢迎消息 -->
                    <div class="message-bubble ai">
                        <div class="message-content">
                            <div class="welcome-message">
                                <h3>👋 欢迎使用AI豆包助手</h3>
                                <p>我是您的智能助手，可以帮您解答问题、处理任务。您可以：</p>
                                <ul>
                                    <li>💬 文字对话</li>
                                    <li>🎤 语音输入</li>
                                    <li>📷 图片分析</li>
                                    <li>📄 文件处理</li>
                                </ul>
                            </div>
                        </div>
                        <div class="message-meta">
                            <span class="message-time">${this.formatTime(new Date())}</span>
                        </div>
                    </div>
                </div>
                
                <!-- 打字指示器 -->
                <div class="typing-indicator" id="typing-indicator" style="display: none;">
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                    <span>AI正在思考...</span>
                </div>
                
                <!-- 快捷回复 -->
                <div class="quick-replies" id="quick-replies">
                    <!-- 动态生成 -->
                </div>
                
                <!-- 输入区域 -->
                <div class="modern-input-container">
                    <div class="input-wrapper">
                        <textarea 
                            class="message-input-modern" 
                            id="message-input"
                            placeholder="输入您的问题..."
                            rows="1"
                        ></textarea>
                        <div class="input-actions">
                            <button class="input-action-btn" id="voice-btn" title="语音输入">
                                🎤
                            </button>
                            <button class="input-action-btn" id="file-btn" title="上传文件">
                                📎
                            </button>
                            <button class="input-action-btn" id="emoji-btn" title="表情">
                                😊
                            </button>
                            <button class="input-action-btn primary" id="send-btn" title="发送">
                                ➤
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 隐藏的文件输入 -->
            <input type="file" id="file-input" style="display: none;" multiple 
                   accept="image/*,audio/*,video/*,.pdf,.doc,.docx,.txt">
        `;
        
        // 获取关键元素引用
        this.messagesContainer = this.container.querySelector('#messages-container');
        this.messageInput = this.container.querySelector('#message-input');
        this.sendBtn = this.container.querySelector('#send-btn');
        this.typingIndicator = this.container.querySelector('#typing-indicator');
        this.quickRepliesContainer = this.container.querySelector('#quick-replies');
        this.fileInput = this.container.querySelector('#file-input');
    }
    
    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 发送按钮
        this.sendBtn.addEventListener('click', () => this.sendMessage());
        
        // 回车发送
        this.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        // 自动调整输入框高度
        this.messageInput.addEventListener('input', () => {
            this.autoResizeTextarea();
        });
        
        // 头部操作按钮
        this.container.addEventListener('click', (e) => {
            const action = e.target.dataset.action;
            if (action) {
                this.handleHeaderAction(action);
            }
        });
        
        // 语音输入
        this.container.querySelector('#voice-btn').addEventListener('click', () => {
            this.toggleVoiceInput();
        });
        
        // 文件上传
        this.container.querySelector('#file-btn').addEventListener('click', () => {
            this.fileInput.click();
        });
        
        this.fileInput.addEventListener('change', (e) => {
            this.handleFileUpload(e.target.files);
        });
        
        // 表情选择
        this.container.querySelector('#emoji-btn').addEventListener('click', () => {
            this.showEmojiPicker();
        });
    }
    
    /**
     * 发送消息
     */
    async sendMessage() {
        const text = this.messageInput.value.trim();
        if (!text) return;
        
        // 添加用户消息
        this.addMessage({
            type: 'user',
            content: text,
            timestamp: new Date()
        });
        
        // 清空输入框
        this.messageInput.value = '';
        this.autoResizeTextarea();
        
        // 显示打字指示器
        this.showTypingIndicator();
        
        try {
            // 调用AI API
            const response = await this.callAIAPI(text);
            
            // 隐藏打字指示器
            this.hideTypingIndicator();
            
            // 添加AI回复
            this.addMessage({
                type: 'ai',
                content: response,
                timestamp: new Date()
            });
            
            // 更新快捷回复
            this.updateQuickReplies(response);
            
        } catch (error) {
            console.error('AI回复失败:', error);
            this.hideTypingIndicator();
            
            this.addMessage({
                type: 'ai',
                content: '抱歉，我现在无法回复您的问题。请稍后再试。',
                timestamp: new Date(),
                isError: true
            });
        }
    }
    
    /**
     * 添加消息到聊天界面
     */
    addMessage(message) {
        const messageId = ++this.messageId;
        message.id = messageId;
        this.messages.push(message);
        
        // 限制消息数量
        if (this.messages.length > this.options.maxMessages) {
            this.messages.shift();
        }
        
        const messageElement = this.createMessageElement(message);
        this.messagesContainer.appendChild(messageElement);
        
        // 自动滚动到底部
        if (this.options.autoScroll) {
            this.scrollToBottom();
        }
        
        // 触发消息添加事件
        this.emit('messageAdded', message);
    }
    
    /**
     * 创建消息元素
     */
    createMessageElement(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message-bubble ${message.type} message-enhanced`;
        messageDiv.dataset.messageId = message.id;
        
        const content = this.formatMessageContent(message.content);
        const time = this.formatTime(message.timestamp);
        
        messageDiv.innerHTML = `
            <div class="message-content ${message.isError ? 'error' : ''}">
                ${content}
            </div>
            <div class="message-meta">
                <span class="message-time">${time}</span>
                ${message.type === 'user' ? '<span class="message-status">✓</span>' : ''}
            </div>
            ${this.options.enableMessageActions ? this.createMessageActions(message) : ''}
        `;
        
        return messageDiv;
    }
    
    /**
     * 格式化消息内容
     */
    formatMessageContent(content) {
        // 支持Markdown基本语法
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }
    
    /**
     * 创建消息操作按钮
     */
    createMessageActions(message) {
        return `
            <div class="message-actions">
                <button class="message-action" data-action="copy" title="复制">📋</button>
                <button class="message-action" data-action="like" title="点赞">👍</button>
                <button class="message-action" data-action="dislike" title="点踩">👎</button>
            </div>
        `;
    }
    
    /**
     * 格式化时间
     */
    formatTime(date) {
        return date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    
    /**
     * 自动调整文本框高度
     */
    autoResizeTextarea() {
        this.messageInput.style.height = 'auto';
        this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
    }
    
    /**
     * 显示打字指示器
     */
    showTypingIndicator() {
        if (!this.options.enableTypingIndicator) return;
        
        this.isTyping = true;
        this.typingIndicator.style.display = 'flex';
        this.scrollToBottom();
    }
    
    /**
     * 隐藏打字指示器
     */
    hideTypingIndicator() {
        this.isTyping = false;
        this.typingIndicator.style.display = 'none';
    }
    
    /**
     * 滚动到底部
     */
    scrollToBottom() {
        setTimeout(() => {
            this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
        }, 100);
    }
    
    /**
     * 调用AI API
     */
    async callAIAPI(message) {
        // 集成现有的豆包API
        if (window.digitalHuman && digitalHuman.doubaoAPI) {
            return await digitalHuman.doubaoAPI.sendMessage(message);
        }
        
        // 备用方案
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve(`这是对"${message}"的AI回复。`);
            }, 1000 + Math.random() * 2000);
        });
    }
    
    /**
     * 事件发射器
     */
    emit(eventName, data) {
        const listeners = this.eventListeners.get(eventName) || [];
        listeners.forEach(listener => listener(data));
    }
    
    /**
     * 添加事件监听器
     */
    on(eventName, listener) {
        if (!this.eventListeners.has(eventName)) {
            this.eventListeners.set(eventName, []);
        }
        this.eventListeners.get(eventName).push(listener);
    }

    /**
     * 设置快捷回复
     */
    setupQuickReplies() {
        if (!this.options.enableQuickReplies) return;

        const defaultReplies = [
            '你好',
            '谢谢',
            '请详细说明',
            '我需要帮助',
            '这个怎么操作？'
        ];

        this.updateQuickReplies(null, defaultReplies);
    }

    /**
     * 更新快捷回复
     */
    updateQuickReplies(aiResponse, customReplies = null) {
        if (!this.options.enableQuickReplies) return;

        let replies = customReplies;

        if (!replies && aiResponse) {
            // 基于AI回复智能生成快捷回复
            replies = this.generateSmartReplies(aiResponse);
        }

        if (!replies) return;

        this.quickRepliesContainer.innerHTML = replies.map(reply =>
            `<button class="quick-reply-btn" data-reply="${reply}">${reply}</button>`
        ).join('');

        // 绑定快捷回复点击事件
        this.quickRepliesContainer.addEventListener('click', (e) => {
            if (e.target.classList.contains('quick-reply-btn')) {
                this.messageInput.value = e.target.dataset.reply;
                this.sendMessage();
            }
        });
    }

    /**
     * 智能生成快捷回复
     */
    generateSmartReplies(aiResponse) {
        const replies = [];

        if (aiResponse.includes('问题') || aiResponse.includes('疑问')) {
            replies.push('还有其他问题吗？');
        }

        if (aiResponse.includes('帮助') || aiResponse.includes('协助')) {
            replies.push('需要更多帮助');
        }

        if (aiResponse.includes('操作') || aiResponse.includes('步骤')) {
            replies.push('请演示一下');
        }

        replies.push('谢谢', '继续', '明白了');

        return replies.slice(0, 5);
    }

    /**
     * 设置语音输入
     */
    setupVoiceInput() {
        if (!this.options.enableVoiceInput) return;

        this.speechRecognition = null;
        this.isRecording = false;

        // 检查浏览器支持
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.speechRecognition = new SpeechRecognition();

            this.speechRecognition.continuous = false;
            this.speechRecognition.interimResults = true;
            this.speechRecognition.lang = 'zh-CN';

            this.speechRecognition.onstart = () => {
                this.isRecording = true;
                this.updateVoiceButton();
            };

            this.speechRecognition.onresult = (event) => {
                let transcript = '';
                for (let i = event.resultIndex; i < event.results.length; i++) {
                    transcript += event.results[i][0].transcript;
                }
                this.messageInput.value = transcript;
                this.autoResizeTextarea();
            };

            this.speechRecognition.onend = () => {
                this.isRecording = false;
                this.updateVoiceButton();
            };

            this.speechRecognition.onerror = (event) => {
                console.error('语音识别错误:', event.error);
                this.isRecording = false;
                this.updateVoiceButton();
            };
        }
    }

    /**
     * 切换语音输入
     */
    toggleVoiceInput() {
        if (!this.speechRecognition) {
            alert('您的浏览器不支持语音识别功能');
            return;
        }

        if (this.isRecording) {
            this.speechRecognition.stop();
        } else {
            this.speechRecognition.start();
        }
    }

    /**
     * 更新语音按钮状态
     */
    updateVoiceButton() {
        const voiceBtn = this.container.querySelector('#voice-btn');
        if (this.isRecording) {
            voiceBtn.innerHTML = '🔴';
            voiceBtn.style.background = 'var(--error-color)';
            voiceBtn.style.color = 'white';
        } else {
            voiceBtn.innerHTML = '🎤';
            voiceBtn.style.background = '';
            voiceBtn.style.color = '';
        }
    }

    /**
     * 设置文件上传
     */
    setupFileUpload() {
        if (!this.options.enableFileUpload) return;

        // 拖拽上传
        this.messagesContainer.addEventListener('dragover', (e) => {
            e.preventDefault();
            this.messagesContainer.classList.add('drag-over');
        });

        this.messagesContainer.addEventListener('dragleave', (e) => {
            e.preventDefault();
            this.messagesContainer.classList.remove('drag-over');
        });

        this.messagesContainer.addEventListener('drop', (e) => {
            e.preventDefault();
            this.messagesContainer.classList.remove('drag-over');
            this.handleFileUpload(e.dataTransfer.files);
        });
    }

    /**
     * 处理文件上传
     */
    async handleFileUpload(files) {
        for (const file of files) {
            if (file.size > 10 * 1024 * 1024) { // 10MB限制
                alert(`文件 ${file.name} 太大，请选择小于10MB的文件`);
                continue;
            }

            // 添加文件消息
            this.addMessage({
                type: 'user',
                content: this.createFileMessage(file),
                timestamp: new Date(),
                isFile: true
            });

            // 处理不同类型的文件
            if (file.type.startsWith('image/')) {
                await this.processImageFile(file);
            } else if (file.type.startsWith('audio/')) {
                await this.processAudioFile(file);
            } else {
                await this.processDocumentFile(file);
            }
        }
    }

    /**
     * 创建文件消息
     */
    createFileMessage(file) {
        const fileSize = this.formatFileSize(file.size);
        const fileIcon = this.getFileIcon(file.type);

        return `
            <div class="file-message">
                <div class="file-icon">${fileIcon}</div>
                <div class="file-info">
                    <div class="file-name">${file.name}</div>
                    <div class="file-size">${fileSize}</div>
                </div>
            </div>
        `;
    }

    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 获取文件图标
     */
    getFileIcon(mimeType) {
        if (mimeType.startsWith('image/')) return '🖼️';
        if (mimeType.startsWith('audio/')) return '🎵';
        if (mimeType.startsWith('video/')) return '🎬';
        if (mimeType.includes('pdf')) return '📄';
        if (mimeType.includes('word')) return '📝';
        if (mimeType.includes('excel')) return '📊';
        return '📎';
    }

    /**
     * 处理图片文件
     */
    async processImageFile(file) {
        const reader = new FileReader();
        reader.onload = async (e) => {
            // 显示图片预览
            this.addMessage({
                type: 'user',
                content: `<img src="${e.target.result}" style="max-width: 100%; border-radius: 8px;" alt="上传的图片">`,
                timestamp: new Date()
            });

            // 调用图像分析API
            try {
                this.showTypingIndicator();
                const analysis = await this.analyzeImage(e.target.result);
                this.hideTypingIndicator();

                this.addMessage({
                    type: 'ai',
                    content: `📷 图像分析结果：\n${analysis}`,
                    timestamp: new Date()
                });
            } catch (error) {
                this.hideTypingIndicator();
                this.addMessage({
                    type: 'ai',
                    content: '抱歉，图像分析失败。请稍后再试。',
                    timestamp: new Date(),
                    isError: true
                });
            }
        };
        reader.readAsDataURL(file);
    }

    /**
     * 分析图像
     */
    async analyzeImage(imageData) {
        // 集成豆包视觉API
        if (window.multiModalAPI) {
            return await multiModalAPI.processImageInput(imageData);
        }

        // 模拟分析结果
        return '这是一张图片，包含了丰富的视觉信息。我可以看到图片中的主要元素和内容。';
    }

    /**
     * 处理头部操作
     */
    handleHeaderAction(action) {
        switch (action) {
            case 'clear':
                this.clearChat();
                break;
            case 'export':
                this.exportChat();
                break;
            case 'settings':
                this.showSettings();
                break;
        }
    }

    /**
     * 清空聊天
     */
    clearChat() {
        if (confirm('确定要清空所有对话吗？')) {
            this.messages = [];
            this.messagesContainer.innerHTML = '';
            this.emit('chatCleared');
        }
    }

    /**
     * 导出聊天记录
     */
    exportChat() {
        const chatData = {
            timestamp: new Date().toISOString(),
            messages: this.messages
        };

        const blob = new Blob([JSON.stringify(chatData, null, 2)], {
            type: 'application/json'
        });

        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `chat-export-${Date.now()}.json`;
        a.click();

        URL.revokeObjectURL(url);
    }

    /**
     * 显示设置
     */
    showSettings() {
        // 实现设置面板
        console.log('显示设置面板');
    }

    /**
     * 显示表情选择器
     */
    showEmojiPicker() {
        const emojis = ['😊', '😂', '🤔', '👍', '❤️', '🎉', '🔥', '💯'];
        const emojiHtml = emojis.map(emoji =>
            `<span class="emoji-option" data-emoji="${emoji}">${emoji}</span>`
        ).join('');

        // 创建临时表情面板
        const emojiPanel = document.createElement('div');
        emojiPanel.className = 'emoji-panel';
        emojiPanel.innerHTML = emojiHtml;
        emojiPanel.style.cssText = `
            position: absolute;
            bottom: 60px;
            right: 20px;
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 8px;
            box-shadow: var(--shadow-lg);
            z-index: 1000;
        `;

        this.container.appendChild(emojiPanel);

        // 点击表情
        emojiPanel.addEventListener('click', (e) => {
            if (e.target.classList.contains('emoji-option')) {
                this.messageInput.value += e.target.dataset.emoji;
                this.messageInput.focus();
                emojiPanel.remove();
            }
        });

        // 点击外部关闭
        setTimeout(() => {
            document.addEventListener('click', function closeEmoji(e) {
                if (!emojiPanel.contains(e.target)) {
                    emojiPanel.remove();
                    document.removeEventListener('click', closeEmoji);
                }
            });
        }, 100);
    }
}

// 导出类
window.ModernChatInterface = ModernChatInterface;
