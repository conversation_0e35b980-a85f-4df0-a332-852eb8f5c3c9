// 简单的音频分析 Worker：接收 Float32Array PCM 帧，返回基于能量与谱质心的简易 viseme 权重
// 注意：这是 PoC 实现，精确的 phoneme -> viseme 需要更复杂的模型或服务端支持

self.onmessage = function(e) {
    try {
        const data = e.data;
        // 传入的是 Float32Array
        const buffer = data.buffer;
        const sampleRate = data.sampleRate || 48000;
        const floatArr = new Float32Array(buffer);
        const N = floatArr.length;
        let sum = 0;
        for (let i = 0; i < N; i++) {
            sum += floatArr[i] * floatArr[i];
        }
        const rms = Math.sqrt(sum / N);

        // 计算谱质心（简化版：对频域使用 DFT 简单估计）
        const half = Math.min(128, Math.floor(N/2));
        const magnitudes = new Float32Array(half);
        for (let k = 0; k < half; k++) {
            let re = 0, im = 0;
            for (let n = 0; n < N; n++) {
                const angle = (2 * Math.PI * k * n) / N;
                re += floatArr[n] * Math.cos(angle);
                im -= floatArr[n] * Math.sin(angle);
            }
            magnitudes[k] = Math.sqrt(re*re + im*im);
        }
        let weighted = 0, magSum = 0;
        for (let k = 0; k < half; k++) {
            weighted += k * magnitudes[k];
            magSum += magnitudes[k];
        }
        const centroid = magSum > 0 ? (weighted / magSum) / half : 0;

        // 简单映射到 viseme / mouthWeights
        // jawOpen 基于 rms
        const jawOpen = Math.min(1, Math.max(0, (rms - 0.01) * 30));
        // vowelness 基于谱质心（低频 -> o,u；高频 -> e,i）
        const vowel = centroid; // 0 - 1
        // 简单权重分配
        const visemes = {
            jawOpen: jawOpen,
            mouthSmile: Math.max(0, vowel - 0.6) * 1.2, // 高频偏向笑
            mouthFrown: Math.max(0, 0.5 - vowel) * 1.2, // 低频偏向收嘴
            mouthWide: Math.max(0, vowel - 0.3) * 0.8
        };

        // 限制在 0-1
        Object.keys(visemes).forEach(k => {
            if (!isFinite(visemes[k]) || visemes[k] < 0) visemes[k] = 0;
            if (visemes[k] > 1) visemes[k] = 1;
        });

        self.postMessage({ visemes: visemes, timestamp: Date.now() });
    } catch (err) {
        self.postMessage({ error: err.message });
    }
};
