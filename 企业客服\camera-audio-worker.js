// 简单的音频分析 Worker：接收 Float32Array PCM 帧，返回基于能量与谱质心的简易 viseme 权重
// 注意：这是 PoC 实现，精确的 phoneme -> viseme 需要更复杂的模型或服务端支持

// 接受两种消息格式：
// 1) { freq: Float32Array } - 频谱幅值（来自 AnalyserNode.getFloatFrequencyData 或 getByteFrequencyData 的归一化值）
// 2) { buffer: ArrayBuffer, sampleRate } - PCM 流（退回兼容）

let lastVisemes = { jawOpen: 0, mouthSmile: 0, mouthFrown: 0, mouthWide: 0 };
const smoothing = 0.4; // 输出平滑

self.onmessage = function(e) {
    try {
        const data = e.data;

        let centroid = 0;
        let rms = 0;

        if (data.freq) {
            // data.freq 是 Float32Array（可能是 dB 或 0-255），我们先取绝对值/线性化
            const freq = data.freq;
            let magSum = 0, weighted = 0;
            let localSum = 0;
            for (let k = 0; k < freq.length; k++) {
                const v = Math.max(0, Math.abs(freq[k]));
                magSum += v;
                weighted += k * v;
                localSum += v*v;
            }
            centroid = magSum > 0 ? (weighted / magSum) / freq.length : 0;
            rms = Math.sqrt(localSum / Math.max(1, freq.length));
        } else if (data.buffer) {
            const floatArr = new Float32Array(data.buffer);
            const N = floatArr.length;
            let sum = 0;
            for (let i = 0; i < N; i++) sum += floatArr[i]*floatArr[i];
            rms = Math.sqrt(sum / N);
            // 不能直接计算谱质心，保守设置 centroid
            centroid = Math.min(1, rms * 50);
        } else {
            self.postMessage({ error: 'unknown payload' });
            return;
        }

        // 基于 rms 与 centroid 计算 viseme 权重
        const jawOpen = Math.min(1, Math.max(0, (rms - 0.01) * 30));
        const vowel = centroid; // 0-1

        const target = {
            jawOpen: jawOpen,
            mouthSmile: Math.max(0, vowel - 0.6) * 1.2,
            mouthFrown: Math.max(0, 0.5 - vowel) * 1.2,
            mouthWide: Math.max(0, vowel - 0.3) * 0.8
        };

        // 平滑输出
        const visemes = {};
        Object.keys(target).forEach(k => {
            let v = target[k];
            if (!isFinite(v) || v < 0) v = 0;
            if (v > 1) v = 1;
            visemes[k] = lastVisemes[k] * (1 - smoothing) + v * smoothing;
            lastVisemes[k] = visemes[k];
        });

        self.postMessage({ visemes: visemes, timestamp: Date.now() });
    } catch (err) {
        self.postMessage({ error: err.message });
    }
};
