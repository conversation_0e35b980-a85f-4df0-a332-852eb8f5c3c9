import os
from flask import Flask, request, jsonify
from flask_cors import CORS
from volcenginesdkarkruntime import Ark

app = Flask(__name__)

# 限制 CORS 来源：通过环境变量 ALLOWED_ORIGINS 配置，多个 origin 使用逗号分隔
allowed_origins = os.environ.get('ALLOWED_ORIGINS')
if allowed_origins:
    origins = [o.strip() for o in allowed_origins.split(',') if o.strip()]
    CORS(app, resources={r"/api/*": {"origins": origins}})
else:
    # 默认不开启全局 CORS，以减少误配置风险；可按需在部署时设置 ALLOWED_ORIGINS
    CORS(app, resources={r"/api/*": {"origins": []}})

# 初始化火山引擎Ark客户端
client = Ark(
    base_url="https://ark.cn-beijing.volces.com/api/v3",
    api_key=os.environ.get("ARK_API_KEY", "YOUR_API_KEY_HERE")  # 请设置环境变量或直接替换
)

@app.route('/api/chat', methods=['POST'])
def chat():
    """代理豆包API聊天请求"""
    try:
        data = request.json
        messages = data.get('messages', [])
        model = data.get('model', 'doubao-seed-1-6-thinking-250715')
        
        # 调用豆包API
        response = client.chat.completions.create(
            model=model,
            messages=messages
        )
        
        # 返回响应
        return jsonify({
            'success': True,
            'data': {
                'content': response.choices[0].message.content,
                'role': response.choices[0].message.role
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/vision', methods=['POST'])
def vision():
    """代理豆包视觉理解API请求"""
    try:
        data = request.json
        image_url = data.get('image_url')
        question = data.get('question', '请描述这张图片')
        model = data.get('model', 'doubao-seed-1-6-thinking-250715')
        
        # 构建消息
        messages = [{
            "role": "user",
            "content": [
                {
                    "type": "image_url",
                    "image_url": {"url": image_url}
                },
                {"type": "text", "text": question}
            ]
        }]
        
        # 调用豆包API
        response = client.chat.completions.create(
            model=model,
            messages=messages
        )
        
        return jsonify({
            'success': True,
            'data': {
                'content': response.choices[0].message.content,
                'role': response.choices[0].message.role
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/health', methods=['GET'])
def health():
    """健康检查端点"""
    return jsonify({'status': 'ok', 'service': 'doubao-api-proxy'})

if __name__ == '__main__':
    print("🚀 火山引擎豆包API代理服务器启动中...")
    print("📍 服务地址: http://localhost:5000")
    print("💡 请确保已设置环境变量 ARK_API_KEY")
    print("📝 API端点:")
    print("   - POST /api/chat - 文本聊天")
    print("   - POST /api/vision - 视觉理解")
    print("   - GET /health - 健康检查")
    # 默认 debug=False，使用 FLASK_DEBUG=1 来开启调试模式（仅在开发环境）
    debug_mode = bool(os.environ.get('FLASK_DEBUG', ''))
    app.run(host='0.0.0.0', port=5000, debug=debug_mode)