<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业数字人客服系统 - 三栏布局</title>
    
    <!-- 配置文件 -->
    <script src="digital-human-config.js"></script>
    
    <!-- Three.js ES模块 -->
    <script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.160.0/build/three.module.js",
            "three/addons/": "https://unpkg.com/three@0.160.0/examples/jsm/"
        }
    }
    </script>
    <script type="module">
        import * as THREE from 'three';
        import { GLTFLoader } from 'three/addons/loaders/GLTFLoader.js';
        
        window.THREE = THREE;
        window.GLTFLoader = GLTFLoader;
        
        window.dispatchEvent(new CustomEvent('three-loaded', {
            detail: { THREE, GLTFLoader }
        }));
        
        console.log('✅ Three.js加载完成', { THREE: !!THREE, GLTFLoader: !!GLTFLoader });
    </script>
    
    <!-- 三栏布局样式 -->
    <link rel="stylesheet" href="three-column-layout.css">
</head>
<body>
    <!-- 数字人客服系统 - 三栏布局 -->
    <div id="digital-human-widget">
        <!-- 顶部标题栏 -->
        <div class="widget-header">
            <div class="widget-title">
                🤖 企业数字人客服系统
            </div>
            <div class="toolbar">
                <button class="toolbar-btn" id="model-selector" title="模型选择">📱</button>
                <button class="toolbar-btn" id="diagnostics-btn" title="系统诊断">🔍</button>
                <button class="toolbar-btn" id="settings-btn" title="设置">⚙️</button>
            </div>
        </div>
        
        <!-- 主内容区 - 三栏 -->
        <div class="main-content">
            <!-- 左侧功能栏 -->
            <div class="left-panel">
                <!-- 表情控制 -->
                <div class="panel-section">
                    <div class="section-title">表情控制</div>
                    <div class="control-group">
                        <button class="control-btn" onclick="digitalHuman.testExpression('happy')">
                            😊 开心
                        </button>
                        <button class="control-btn" onclick="digitalHuman.testExpression('neutral')">
                            😐 中性
                        </button>
                        <button class="control-btn" onclick="digitalHuman.testExpression('thinking')">
                            🤔 思考
                        </button>
                        <button class="control-btn" onclick="digitalHuman.testExpression('concern')">
                            😟 担忧
                        </button>
                    </div>
                </div>
                
                <!-- 动作控制 -->
                <div class="panel-section">
                    <div class="section-title">动作控制</div>
                    <div class="control-group">
                        <button class="control-btn" onclick="digitalHuman.testAnimation('greeting')">
                            👋 打招呼
                        </button>
                        <button class="control-btn" onclick="digitalHuman.testAnimation('pointing')">
                            👉 指向
                        </button>
                        <button class="control-btn" onclick="digitalHuman.testAnimation('nodding')">
                            👍 点头
                        </button>
                        <button class="control-btn" onclick="digitalHuman.testAnimation('shaking')">
                            ❌ 摇头
                        </button>
                    </div>
                </div>
                
                <!-- 模型切换 -->
                <div class="panel-section">
                    <div class="section-title">模型选择</div>
                    <div class="control-group">
                        <button class="control-btn" onclick="digitalHuman.switchTo2DMode && digitalHuman.switchTo2DMode()">
                            🖼️ 2D模式
                        </button>
                        <button class="control-btn" onclick="digitalHuman.switchTo3DMode && digitalHuman.switchTo3DMode()">
                            🎮 3D模式
                        </button>
                    </div>
                </div>
                
                <!-- 特效控制 -->
                <div class="panel-section">
                    <div class="section-title">特效</div>
                    <div class="control-group">
                        <button class="control-btn" onclick="digitalHuman.play2DEffect && digitalHuman.play2DEffect('attention')">
                            ✨ 吸引注意
                        </button>
                        <button class="control-btn" onclick="digitalHuman.play2DEffect && digitalHuman.play2DEffect('success')">
                            ✅ 成功效果
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 中间3D渲染区 -->
            <div class="center-panel">
                <!-- 3D渲染画布 -->
                <canvas id="avatar-canvas"></canvas>
                
                <!-- 2D数字人容器 -->
                <div id="digital-human-2d-container">
                    <img id="digital-human-2d-image" alt="数字人客服" />
                </div>
                
                <!-- 状态指示器 -->
                <div class="status-indicator">
                    <span id="status">初始化中...</span>
                </div>
            </div>
            
            <!-- 右侧聊天栏 -->
            <div class="right-panel">
                <div class="chat-section">
                    <div class="chat-header">
                        <div class="chat-title">
                            💬 AI智能对话
                        </div>
                    </div>
                    <div class="chat-messages" id="messages">
                        <!-- 聊天消息将动态插入这里 -->
                    </div>
                    <div class="chat-input">
                        <input type="text" id="message-input" placeholder="输入您的问题..." />
                        <button id="send-btn" onclick="digitalHuman.sendMessage()">➤</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 模型选择面板（弹出层）-->
        <div class="model-selection-panel" id="model-selection-panel">
            <div class="panel-title">选择数字人模型</div>
            <div id="model-options-container">
                <!-- 动态生成模型选项 -->
            </div>
        </div>
    </div>

    <!-- 核心JS脚本 -->
    <script src="enterprise-model-loader.js"></script>
    <script src="digital-human-core.js"></script>
    <script src="websocket-client.js"></script>
    <script src="camera-audio.js"></script>
    <script src="multimodal-doubao.js"></script>
    <script src="multimodal-integration.js"></script>
    <script src="model-diagnostics.js"></script>
    <script src="image-2d-animator.js"></script>
    <script src="enhanced-2d-animations.js"></script>
    <script src="model-selection-manager.js"></script>
    <script src="modern-ui-controller.js"></script>
    <script src="offline-optimizer.js"></script>
    <script src="demo.js"></script>
    
    <script>
        // 初始化系统
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                // 初始化数字人核心
                await digitalHuman.init();
                
                // 初始化摄像头和音频
                await digitalHuman.initCameraAudio();
                
                // 初始化多模态API
                await digitalHuman.initMultiModalAPI();
                
                console.log('✅ 系统初始化完成');
                document.getElementById('status').textContent = '系统就绪';
            } catch (error) {
                console.error('❌ 系统初始化失败:', error);
                document.getElementById('status').textContent = '初始化失败';
            }
        });
        
        // 回车发送消息
        document.getElementById('message-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                digitalHuman.sendMessage();
            }
        });
        
        // 模型选择器
        document.getElementById('model-selector').addEventListener('click', function() {
            const panel = document.getElementById('model-selection-panel');
            panel.classList.toggle('active');
        });
        
        // 点击外部关闭模型选择面板
        document.addEventListener('click', function(e) {
            const panel = document.getElementById('model-selection-panel');
            const btn = document.getElementById('model-selector');
            if (!panel.contains(e.target) && e.target !== btn) {
                panel.classList.remove('active');
            }
        });
    </script>
</body>
</html>