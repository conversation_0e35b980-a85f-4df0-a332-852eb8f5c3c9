digital-human-config.js:67 Uncaught ReferenceError: process is not defined
    at digital-human-config.js:67:17
digital-human-config.js:67 Uncaught ReferenceError: process is not defined
    at digital-human-config.js:67:17
enterprise-model-loader.js:33 🚀 初始化企业级模型加载器
enterprise-model-loader.js:590 🏭 企业级模型加载器脚本加载完成
enterprise-model-loader.js:62 ✅ Three.js事件加载完成
index.html:33 ✅ Three.js ES模块加载完成 Object
enterprise-model-loader.js:92 📸 开始加载2D数字人模型
enterprise-model-loader.js:314 📸 设置2D图片源... ./digital-human.jpg...
enterprise-model-loader.js:580 📱 企业级模型加载器已启动
digital-human-core.js:54 🎮 开始初始化数字人核心系统
digital-human-core.js:98 ⏳ 等待模型加载器就绪...
enterprise-model-loader.js:285 ⚠️ 主图片加载失败，尝试备用方案
image.onerror @ enterprise-model-loader.js:285
enterprise-model-loader.js:297 🔄 尝试备用路径: ./digital-human.png
digital-human.png:1 
        
        
       Failed to load resource: net::ERR_FILE_NOT_FOUND
enterprise-model-loader.js:297 🔄 尝试备用路径: ./digital-human.webp
digital-human.webp:1 
        
        
       Failed to load resource: net::ERR_FILE_NOT_FOUND
enterprise-model-loader.js:301 ⚠️ 所有路径都失败，显示占位符
enterprise-model-loader.js:386 🖼️ 创建2D图片占位符 - 高可见性版本
enterprise-model-loader.js:98 ✅ 2D数字人模型加载成功
enterprise-model-loader.js:466 🔄 切换到2D模式
enterprise-model-loader.js:485 ✅ 已切换到2D模式
enterprise-model-loader.js:547 📊 2D数字人就绪
enterprise-model-loader.js:42 ✅ 企业级模型加载器就绪
enterprise-model-loader.js:70 🔍 检查Three.js状态 (尝试 1/20) Object
enterprise-model-loader.js:77 ✅ Three.js轮询检查完成
digital-human-core.js:104 ✅ 模型加载器就绪
digital-human-core.js:717 状态: 初始化3D场景...
digital-human-core.js:717 状态: 加载3D模型...
digital-human-core.js:68 ✅ 使用企业级模型加载器，优先2D模式
digital-human-core.js:717 状态: 2D数字人就绪
digital-human-core.js:717 状态: 启动渲染循环...
digital-human-core.js:717 状态: 连接服务器...
digital-human-core.js:717 状态: 系统就绪
digital-human-core.js:82 ✅ 数字人核心系统初始化完成
websocket-client.js:41 WebSocket connection to 'ws://localhost:8080/ws' failed: 
initWebSocket @ websocket-client.js:41
websocket-client.js:91 WebSocket错误: Event
ws.onerror @ websocket-client.js:91
websocket-client.js:249 WebSocket错误: Event
handleError @ websocket-client.js:249
digital-human-core.js:717 状态: 连接错误
digital-human-core.js:717 状态: 连接错误
websocket-client.js:79 WebSocket连接关闭
digital-human-core.js:717 状态: 连接断开，尝试重连...
enterprise-model-loader.js:322 ⏰ 2D图片加载超时检查
enterprise-model-loader.js:323 📊 容器状态: Object
enterprise-model-loader.js:330 🔧 图片未完全加载，创建占位符
enterprise-model-loader.js:386 🖼️ 创建2D图片占位符 - 高可见性版本
camera-audio.js:67 摄像头访问成功
websocket-client.js:240 第1次重连尝试...
websocket-client.js:41 WebSocket connection to 'ws://localhost:8080/ws' failed: 
initWebSocket @ websocket-client.js:41
websocket-client.js:91 WebSocket错误: Event
ws.onerror @ websocket-client.js:91
websocket-client.js:249 WebSocket错误: Event
handleError @ websocket-client.js:249
digital-human-core.js:717 状态: 连接错误
digital-human-core.js:717 状态: 连接错误
websocket-client.js:79 WebSocket连接关闭
digital-human-core.js:717 状态: 连接断开，尝试重连...
enterprise-model-loader.js:554 🔍 模型加载器诊断报告
enterprise-model-loader.js:555 ================
enterprise-model-loader.js:556 Three.js状态: Object
enterprise-model-loader.js:561 加载器状态: Object
enterprise-model-loader.js:567 2D模型状态: Object
enterprise-model-loader.js:571 ================
camera-audio.js:90 音频访问成功
camera-audio.js:43 摄像头和音频初始化完成
digital-human-core.js:717 状态: 摄像头和音频就绪
index.html:137 ✅ 豆包API已加载
simple-doubao-api.js:13 豆包API初始化完成
index.html:141 3D数字人系统初始化完成
websocket-client.js:240 第2次重连尝试...
websocket-client.js:41 WebSocket connection to 'ws://localhost:8080/ws' failed: 
initWebSocket @ websocket-client.js:41
websocket-client.js:91 WebSocket错误: Event
ws.onerror @ websocket-client.js:91
websocket-client.js:249 WebSocket错误: Event
handleError @ websocket-client.js:249
digital-human-core.js:717 状态: 连接错误
digital-human-core.js:717 状态: 连接错误
websocket-client.js:79 WebSocket连接关闭
digital-human-core.js:717 状态: 连接断开，尝试重连...
websocket-client.js:240 第3次重连尝试...
websocket-client.js:41 WebSocket connection to 'ws://localhost:8080/ws' failed: 
initWebSocket @ websocket-client.js:41
websocket-client.js:91 WebSocket错误: Event
ws.onerror @ websocket-client.js:91
websocket-client.js:249 WebSocket错误: Event
handleError @ websocket-client.js:249
digital-human-core.js:717 状态: 连接错误
digital-human-core.js:717 状态: 连接错误
websocket-client.js:79 WebSocket连接关闭
digital-human-core.js:717 状态: 连接断开，尝试重连...
websocket-client.js:240 第4次重连尝试...
websocket-client.js:41 WebSocket connection to 'ws://localhost:8080/ws' failed: 
initWebSocket @ websocket-client.js:41
websocket-client.js:91 WebSocket错误: Event
ws.onerror @ websocket-client.js:91
websocket-client.js:249 WebSocket错误: Event
handleError @ websocket-client.js:249
digital-human-core.js:717 状态: 连接错误
digital-human-core.js:717 状态: 连接错误
websocket-client.js:79 WebSocket连接关闭
digital-human-core.js:717 状态: 连接断开，尝试重连...
websocket-client.js:240 第5次重连尝试...
websocket-client.js:41 WebSocket connection to 'ws://localhost:8080/ws' failed: 
initWebSocket @ websocket-client.js:41
websocket-client.js:91 WebSocket错误: Event
ws.onerror @ websocket-client.js:91
websocket-client.js:249 WebSocket错误: Event
handleError @ websocket-client.js:249
digital-human-core.js:717 状态: 连接错误
digital-human-core.js:717 状态: 连接错误
websocket-client.js:79 WebSocket连接关闭
digital-human-core.js:717 状态: 连接断开，尝试重连...
websocket-client.js:232 重连次数超限，停止重连
attemptReconnect @ websocket-client.js:232
digital-human-core.js:717 状态: 连接失败
camera-audio.js:212 检测到语音开始
