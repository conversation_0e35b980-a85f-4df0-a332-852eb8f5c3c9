# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **plugin component** for integrating 3D digital human functionality into an existing corporate OA (Office Automation) chat system. The system enhances existing chat windows with interactive 3D avatars featuring facial expressions, gesture animations, and AI-powered conversations using the Douyin (ByteDance) API.

**Critical Constraint**: This is NOT a standalone system - it must integrate seamlessly with existing OA infrastructure while minimizing changes to current workflows.

## Technology Stack Constraints

**IMPORTANT**: This project must adhere to company technical standards:
- Frontend: Pure HTML + CSS + JavaScript (no frameworks like Vue.js despite package.json)
- Backend: C language 
- Communication: WebSocket protocol
- Runtime: Web browser environment

The package.json currently shows Vue.js dependencies but should be treated as a legacy artifact. New development must use vanilla JavaScript only.

## Integration Architecture

### Three-Layer Integration Pattern
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   OA Chat UI     │    │ 3D Human Widget │    │   C Backend     │
│ (Existing)      │◄──►│ (New Component) │◄──►│ (Extended)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

**Integration Strategy**:
- 🎯 **Minimal Intrusion**: Embed as `<div id="digital-human-widget">` in existing chat
- 📦 **Plugin Architecture**: Self-contained 3D component with defined interfaces  
- 🔧 **Protocol Extension**: Extend existing WebSocket with AI chat message types
- 📱 **UI Enhancement**: Overlay 3D avatar on existing chat without replacing functionality

### Message Flow Integration
OA Chat Messages → WebSocket → C Backend → Douyin API → Animation Commands → 3D Avatar

### Key Components
1. **AvatarRenderer**: GLB model loading and 3D rendering
2. **ExpressionController**: Facial expression management via BlendShapes
3. **AnimationManager**: Gesture animations (greeting, pointing, etc.)
4. **ChatInterface**: Integration with existing OA chat window
5. **CameraHandler**: WebRTC camera input processing

## Available Models

3D models are stored in:
- `look1-nayong-glb-test/source/look1.glb` - Main 3D avatar model
- `look1-nayong-glb-test/textures/` - Associated texture files
- `look1_nayong_glb_test.glb` and `look1_nayong_glb_test (1).glb` - Alternative model versions

## Development Commands

Based on package.json (use only for reference):
- `npm run dev` - Development server (Vite-based, may not align with company standards)
- `npm run build` - Production build
- `npm run server` - Node.js server (for development testing only)

**Note**: Actual deployment must use C backend with WebSocket extensions for AI integration.

## C Backend Integration Requirements

The existing C backend requires these extensions:
```c
// WebSocket handler extension for AI chat
websocket_handler() {
  // 1. Receive OA chat messages  
  // 2. Call Douyin API via HTTP client
  // 3. Return AI response + animation commands
  // 4. Maintain existing OA protocol compatibility
}
```

**Key Integration Points**:
- Extend existing WebSocket message handlers (do not replace)
- Add HTTP client library for Douyin API calls
- Preserve all existing OA authentication and session management
- Add new message types while maintaining backward compatibility

## Message Protocol Extensions

**New message types to add to existing OA WebSocket protocol**:

```json
{
  "type": "ai_chat_request",
  "data": {
    "user_id": "existing_oa_user_id",
    "message": "user input text",
    "session_id": "existing_oa_session"
  }
}
```

```json
{
  "type": "ai_chat_response", 
  "data": {
    "response": "AI generated response",
    "animation": "greeting|pointing|thinking|explaining",
    "expression": "happy|neutral|concern|thinking",
    "audio_url": "optional_tts_audio_path"
  }
}
```

**Critical**: These extend existing OA message types - do not modify current chat protocols.

## Implementation Constraints

**Integration-Specific Requirements**:
- 3D widget must be completely self-contained (no dependencies on OA internals)
- All existing OA chat functionality must remain unchanged
- User authentication/authorization handled by existing OA system
- 3D component communicates only through defined WebSocket message extensions
- No modification of existing database schemas or user management

**Technical Constraints**:
- GLB models must support BlendShape morphing for facial expressions
- Animation sequences must be pre-defined and reliable (no procedural generation)
- Audio-visual sync uses simple volume-based lip-sync (not complex phoneme analysis)
- Camera input processing should be lightweight (basic interaction triggers only)
- Widget must gracefully degrade if WebGL/3D capabilities are unavailable

## Company Resource References

See `《企业客服形象模型搜索与下载指南》 (1).md` for additional 3D model sourcing options beyond the included assets.