/* 
 * 现代化数字人界面设计
 * Linus设计哲学：简洁、高效、直观
 * 灵感来源：Linux桌面环境 + 现代Material Design
 */

/* CSS Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 全局变量 - Linux风格配色 */
:root {
    /* 主色调 - Linux Terminal风格 */
    --primary-bg: #1e1e1e;
    --secondary-bg: #2d2d2d;
    --accent-bg: #3c3c3c;
    --surface-bg: #252525;
    
    /* 文字颜色 */
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-accent: #00ff88;
    --text-warning: #ffaa00;
    --text-error: #ff4444;
    
    /* 功能颜色 */
    --success: #00ff88;
    --warning: #ffaa00;
    --error: #ff4444;
    --info: #44aaff;
    
    /* 阴影和边框 */
    --shadow-light: 0 2px 8px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 16px rgba(0,0,0,0.2);
    --shadow-heavy: 0 8px 32px rgba(0,0,0,0.3);
    --border-radius: 8px;
    --border-color: #404040;
    
    /* 动画 */
    --transition-fast: 0.15s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.6s ease;
}

/* 主容器 - 全屏现代卡片设计 */
#digital-human-widget {
    position: relative;
    width: 100vw;
    height: 100vh;
    background: linear-gradient(145deg, var(--primary-bg), var(--secondary-bg));
    border-radius: 0; /* 全屏无圆角 */
    box-shadow: none; /* 全屏无阴影 */
    overflow: hidden;
    font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    border: none;
    display: flex;
    flex-direction: column;
    margin: 0;
    padding: 0;
}

/* 头部工具栏 - Linux窗口管理器风格 */
.widget-header {
    background: var(--accent-bg);
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    border-bottom: 1px solid var(--border-color);
    user-select: none;
}

.widget-title {
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.widget-title::before {
    content: '🤖';
    font-size: 16px;
}

/* 工具按钮组 */
.toolbar {
    display: flex;
    gap: 4px;
}

.toolbar-btn {
    width: 32px;
    height: 32px;
    background: rgba(255,255,255,0.1);
    border: none;
    border-radius: 4px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.toolbar-btn:hover {
    background: rgba(255,255,255,0.2);
    color: var(--text-primary);
    transform: scale(1.05);
}

.toolbar-btn.active {
    background: var(--success);
    color: var(--primary-bg);
}

/* 主显示区域 */
.main-display {
    flex: 1;
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow: hidden;
}

/* 3D画布 */
#avatar-canvas {
    width: 100%;
    height: 100%;
    display: block;
    transition: opacity var(--transition-medium);
}

/* 2D容器 */
#digital-human-2d-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

#digital-human-2d-image {
    max-width: 85%;
    max-height: 85%;
    object-fit: contain;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    transition: all var(--transition-medium);
}

#digital-human-2d-image:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-heavy);
}

/* 控制面板 - 现代化设计 */
.control-panel {
    position: absolute;
    top: 16px;
    right: 16px;
    background: rgba(0,0,0,0.8);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 60px;
    border: 1px solid rgba(255,255,255,0.1);
    z-index: 10;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.control-group + .control-group {
    border-top: 1px solid rgba(255,255,255,0.2);
    padding-top: 8px;
}

.control-btn {
    width: 36px;
    height: 36px;
    background: rgba(255,255,255,0.1);
    border: none;
    border-radius: 6px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    position: relative;
}

.control-btn:hover {
    background: rgba(255,255,255,0.2);
    color: var(--text-primary);
    transform: scale(1.1);
}

.control-btn:active {
    transform: scale(0.95);
}

/* 悬浮提示 */
.control-btn::after {
    content: attr(title);
    position: absolute;
    right: 44px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--primary-bg);
    color: var(--text-primary);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity var(--transition-fast);
    border: 1px solid var(--border-color);
}

.control-btn:hover::after {
    opacity: 1;
}

/* 状态指示器 */
.status-indicator {
    position: absolute;
    bottom: 16px;
    left: 16px;
    background: rgba(0,0,0,0.8);
    backdrop-filter: blur(10px);
    color: var(--text-primary);
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    border: 1px solid rgba(255,255,255,0.1);
    display: flex;
    align-items: center;
    gap: 8px;
    z-index: 5;
}

.status-indicator::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 聊天界面 - 现代消息设计 */
.chat-section {
    height: 240px; /* 增加聊天区域高度 */
    background: var(--surface-bg);
    border-top: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    z-index: 1;
    position: relative;
}

.chat-header {
    height: 40px;
    background: var(--accent-bg);
    display: flex;
    align-items: center;
    padding: 0 16px;
    border-bottom: 1px solid var(--border-color);
}

.chat-title {
    color: var(--text-primary);
    font-size: 13px;
    font-weight: 500;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.message {
    max-width: 80%;
    padding: 8px 12px;
    border-radius: 12px;
    font-size: 13px;
    line-height: 1.4;
    animation: messageSlide 0.3s ease;
}

@keyframes messageSlide {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message.user {
    background: var(--info);
    color: white;
    align-self: flex-end;
    margin-left: auto;
}

.message.ai {
    background: var(--accent-bg);
    color: var(--text-primary);
    align-self: flex-start;
    border: 1px solid var(--border-color);
}

/* 输入区域 */
.chat-input {
    height: 48px;
    padding: 8px 12px;
    display: flex;
    gap: 8px;
    background: var(--primary-bg);
    border-top: 1px solid var(--border-color);
    position: relative;
    z-index: 2;
}

#message-input {
    flex: 1;
    background: var(--accent-bg);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 8px 16px;
    color: var(--text-primary);
    font-size: 13px;
    outline: none;
    transition: border-color var(--transition-fast);
}

#message-input:focus {
    border-color: var(--info);
}

#message-input::placeholder {
    color: var(--text-secondary);
}

#send-btn {
    width: 32px;
    height: 32px;
    background: var(--info);
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

#send-btn:hover {
    background: #3388cc;
    transform: scale(1.1);
}

#send-btn:disabled {
    background: var(--text-secondary);
    cursor: not-allowed;
    transform: none;
}

/* 模型选择面板 */
.model-selection-panel {
    position: absolute;
    top: 56px;
    right: 16px;
    background: rgba(0,0,0,0.95);
    backdrop-filter: blur(15px);
    border-radius: var(--border-radius);
    padding: 16px;
    min-width: 280px;
    max-height: 400px;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-heavy);
    z-index: 100;
    display: none;
    flex-direction: column;
    gap: 12px;
}

.panel-title {
    color: var(--text-primary);
    font-size: 14px;
    font-weight: 600;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 8px;
}

.model-option {
    background: rgba(255,255,255,0.05);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 12px;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.model-option:hover {
    background: rgba(255,255,255,0.1);
    border-color: var(--info);
    transform: translateY(-1px);
}

.model-option.active {
    background: rgba(0,255,136,0.1);
    border-color: var(--success);
}

.model-name {
    color: var(--text-primary);
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 4px;
}

.model-description {
    color: var(--text-secondary);
    font-size: 11px;
    margin-bottom: 6px;
}

.model-status {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 11px;
    color: var(--success);
}

.model-features {
    color: var(--text-secondary);
    font-size: 10px;
    margin-top: 4px;
}

/* 加载进度条 */
.progress-bar {
    width: 100%;
    height: 2px;
    background: var(--accent-bg);
    border-radius: 1px;
    overflow: hidden;
    margin-top: 4px;
}

.progress-fill {
    height: 100%;
    background: var(--info);
    border-radius: 1px;
    transition: width var(--transition-medium);
}

/* 响应式设计 */
@media (max-width: 600px) {
    .model-selection-panel {
        right: 8px;
        left: 8px;
        width: auto;
    }
    
    .control-panel {
        scale: 0.9; /* 移动端缩小控制面板 */
    }
}

/* 全屏样式重置 */
body {
    margin: 0;
    padding: 0;
    overflow: hidden; /* 防止滚动条 */
    background: var(--primary-bg);
}

html {
    margin: 0;
    padding: 0;
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar {
    width: 4px;
}

.chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: var(--text-secondary);
    border-radius: 2px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: var(--text-primary);
}

/* 焦点可访问性 */
.control-btn:focus,
.toolbar-btn:focus,
#send-btn:focus {
    outline: 2px solid var(--info);
    outline-offset: 2px;
}

/* 主题切换动画 */
* {
    transition: background-color var(--transition-medium), border-color var(--transition-medium);
}