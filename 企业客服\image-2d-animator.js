/**
 * 2D图片动画控制器
 * 让静态2D数字人图片具备表情和动作
 */

class Image2DAnimator {
    constructor(imageElement) {
        this.image = imageElement;
        this.originalTransform = '';
        this.currentAnimation = null;
        
        // 预定义动画效果
        this.animations = {
            // 表情动画
            happy: {
                keyframes: [
                    { transform: 'scale(1)', filter: 'brightness(1)' },
                    { transform: 'scale(1.02)', filter: 'brightness(1.1)' },
                    { transform: 'scale(1)', filter: 'brightness(1)' }
                ],
                duration: 1000,
                description: '微笑表情 - 轻微放大+增亮'
            },
            
            thinking: {
                keyframes: [
                    { transform: 'rotate(0deg)', opacity: '1' },
                    { transform: 'rotate(1deg)', opacity: '0.9' },
                    { transform: 'rotate(-1deg)', opacity: '0.9' },
                    { transform: 'rotate(0deg)', opacity: '1' }
                ],
                duration: 2000,
                description: '思考表情 - 轻微摇摆'
            },
            
            concern: {
                keyframes: [
                    { filter: 'brightness(1) contrast(1)' },
                    { filter: 'brightness(0.9) contrast(1.1)' },
                    { filter: 'brightness(1) contrast(1)' }
                ],
                duration: 1500,
                description: '关切表情 - 微调明暗'
            },
            
            // 动作动画
            greeting: {
                keyframes: [
                    { transform: 'translateX(0px) rotate(0deg)' },
                    { transform: 'translateX(-2px) rotate(1deg)' },
                    { transform: 'translateX(2px) rotate(-1deg)' },
                    { transform: 'translateX(-1px) rotate(0.5deg)' },
                    { transform: 'translateX(0px) rotate(0deg)' }
                ],
                duration: 2000,
                description: '打招呼 - 轻微摆动'
            },
            
            pointing: {
                keyframes: [
                    { transform: 'translateX(0px)' },
                    { transform: 'translateX(3px)' },
                    { transform: 'translateX(0px)' }
                ],
                duration: 1000,
                description: '指向 - 向右移动'
            },
            
            explaining: {
                keyframes: [
                    { transform: 'scale(1) translateY(0px)' },
                    { transform: 'scale(1.01) translateY(-1px)' },
                    { transform: 'scale(1) translateY(0px)' },
                    { transform: 'scale(1.01) translateY(-1px)' },
                    { transform: 'scale(1) translateY(0px)' }
                ],
                duration: 3000,
                description: '解释 - 轻微浮动'
            }
        };
        
        this.init();
    }
    
    init() {
        // 保存原始变换
        this.originalTransform = this.image.style.transform || '';
        
        // 设置基础样式
        this.image.style.transition = 'all 0.3s ease';
        this.image.style.transformOrigin = 'center center';
        
        console.log('2D图片动画控制器初始化完成');
    }
    
    /**
     * 播放动画
     */
    playAnimation(animationName, callback = null) {
        const animation = this.animations[animationName];
        if (!animation) {
            console.warn('动画不存在:', animationName);
            return;
        }
        
        console.log('播放2D动画:', animationName, '-', animation.description);
        
        // 停止当前动画
        if (this.currentAnimation) {
            this.currentAnimation.cancel();
        }
        
        // 播放新动画
        this.currentAnimation = this.image.animate(
            animation.keyframes,
            {
                duration: animation.duration,
                easing: 'ease-in-out',
                fill: 'forwards'
            }
        );
        
        // 动画完成后回调
        this.currentAnimation.addEventListener('finish', () => {
            this.resetToOriginal();
            if (callback) callback();
        });
    }
    
    /**
     * 恢复原始状态
     */
    resetToOriginal() {
        this.image.style.transform = this.originalTransform;
        this.image.style.filter = '';
        this.image.style.opacity = '1';
        this.currentAnimation = null;
    }
    
    /**
     * 设置表情
     */
    setExpression(expression, duration = 1000) {
        console.log('设置2D表情:', expression);
        this.playAnimation(expression);
    }
    
    /**
     * 播放动作
     */
    playGesture(gesture) {
        console.log('播放2D动作:', gesture);
        this.playAnimation(gesture);
    }
    
    /**
     * 创建自定义动画
     */
    createCustomAnimation(name, keyframes, duration = 1000, description = '') {
        this.animations[name] = {
            keyframes: keyframes,
            duration: duration,
            description: description
        };
        
        console.log('创建自定义动画:', name);
    }
    
    /**
     * 连续播放多个动画
     */
    async playSequence(animationNames) {
        for (const animationName of animationNames) {
            await new Promise(resolve => {
                this.playAnimation(animationName, resolve);
            });
            
            // 动画间间隔
            await new Promise(resolve => setTimeout(resolve, 500));
        }
    }
    
    /**
     * 获取可用动画列表
     */
    getAvailableAnimations() {
        return Object.keys(this.animations).map(name => ({
            name: name,
            description: this.animations[name].description
        }));
    }
}

/**
 * 集成到现有3D数字人系统
 */
function integrate2DImageAnimation() {
    // 查找2D数字人图片
    const digitalHumanImage = document.querySelector('#digital-human-2d-image');
    
    if (!digitalHumanImage) {
        console.log('未找到2D数字人图片，创建一个用于演示');
        create2DDigitalHumanDisplay();
        return;
    }
    
    // 创建2D动画控制器
    const animator = new Image2DAnimator(digitalHumanImage);
    
    // 扩展现有数字人系统
    if (window.digitalHuman) {
        digitalHuman.image2DAnimator = animator;
        
        // 重写表情设置方法，同时控制2D和3D
        const originalSetExpression = digitalHuman.setExpression;
        digitalHuman.setExpression = function(expression, intensity = 1.0) {
            // 调用原来的3D表情
            originalSetExpression.call(this, expression, intensity);
            
            // 同时设置2D表情
            if (this.image2DAnimator) {
                this.image2DAnimator.setExpression(expression);
            }
        };
        
        // 重写动画播放方法
        const originalTestAnimation = digitalHuman.testAnimation;
        digitalHuman.testAnimation = function(animation) {
            // 调用原来的3D动画
            originalTestAnimation.call(this, animation);
            
            // 同时播放2D动画
            if (this.image2DAnimator) {
                this.image2DAnimator.playGesture(animation);
            }
        };
        
        console.log('2D动画已集成到3D数字人系统');
    }
    
    return animator;
}

/**
 * 创建2D数字人显示区域
 */
function create2DDigitalHumanDisplay() {
    const widget = document.getElementById('digital-human-widget');
    if (!widget) return;
    
    // 创建2D/3D切换按钮
    const toggleButton = document.createElement('button');
    toggleButton.innerHTML = '2D';
    toggleButton.style.cssText = `
        position: absolute;
        top: 10px;
        left: 10px;
        background: rgba(0,0,0,0.7);
        color: white;
        border: none;
        padding: 4px 8px;
        border-radius: 3px;
        cursor: pointer;
        z-index: 1000;
    `;
    
    // 创建2D图片容器
    const imageContainer = document.createElement('div');
    imageContainer.id = 'digital-human-2d-container';
    imageContainer.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 70%;
        display: none;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    `;
    
    // 创建2D数字人图片
    const digitalHumanImg = document.createElement('img');
    digitalHumanImg.id = 'digital-human-2d-image';
    digitalHumanImg.style.cssText = `
        max-width: 90%;
        max-height: 90%;
        object-fit: contain;
        border-radius: 8px;
    `;
    
    // 设置图片源（你提供的图片）
    digitalHumanImg.src = 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAgACADASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD5/ooooA//2Q=='; // 这里应该是你提供的图片的base64或URL
    
    imageContainer.appendChild(digitalHumanImg);
    widget.appendChild(imageContainer);
    widget.appendChild(toggleButton);
    
    // 切换2D/3D显示
    let is2DMode = false;
    toggleButton.onclick = () => {
        const canvas = document.getElementById('avatar-canvas');
        
        if (is2DMode) {
            // 切换到3D
            canvas.style.display = 'block';
            imageContainer.style.display = 'none';
            toggleButton.innerHTML = '2D';
            is2DMode = false;
        } else {
            // 切换到2D
            canvas.style.display = 'none';
            imageContainer.style.display = 'flex';
            toggleButton.innerHTML = '3D';
            is2DMode = true;
            
            // 初始化2D动画
            setTimeout(() => {
                integrate2DImageAnimation();
            }, 100);
        }
    };
}

// 自动初始化
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        create2DDigitalHumanDisplay();
    }, 2000);
});

console.log('2D图片动画系统加载完成');