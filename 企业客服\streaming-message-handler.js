/**
 * 流式消息处理器
 * 实现实时消息流、打字效果、流式响应
 */

class StreamingMessageHandler {
    constructor(chatInterface) {
        this.chatInterface = chatInterface;
        this.currentStreamingMessage = null;
        this.streamingSpeed = 30; // 字符/秒
        this.isStreaming = false;
        
        // 流式响应配置
        this.streamConfig = {
            enableStreaming: true,
            enableTypingEffect: true,
            enableSoundEffects: false,
            typingSpeed: 50, // 毫秒/字符
            chunkSize: 3, // 每次显示的字符数
            enableMarkdownRealtime: true
        };
        
        this.init();
    }
    
    /**
     * 初始化流式处理器
     */
    init() {
        this.setupStreamingAPI();
        this.setupTypingEffects();
        this.setupSoundEffects();
        
        console.log('✅ 流式消息处理器初始化完成');
    }
    
    /**
     * 设置流式API
     */
    setupStreamingAPI() {
        // 重写聊天界面的API调用方法
        const originalCallAIAPI = this.chatInterface.callAIAPI;
        
        this.chatInterface.callAIAPI = async (message) => {
            if (this.streamConfig.enableStreaming) {
                return await this.streamingAPICall(message);
            } else {
                return await originalCallAIAPI.call(this.chatInterface, message);
            }
        };
    }
    
    /**
     * 流式API调用
     */
    async streamingAPICall(message) {
        try {
            // 创建流式消息容器
            const streamingMessageId = this.createStreamingMessage();
            
            // 检查是否有豆包API支持流式响应
            if (window.digitalHuman && digitalHuman.doubaoAPI) {
                return await this.callDoubaoStreamingAPI(message, streamingMessageId);
            } else {
                // 模拟流式响应
                return await this.simulateStreamingResponse(message, streamingMessageId);
            }
            
        } catch (error) {
            console.error('流式API调用失败:', error);
            throw error;
        }
    }
    
    /**
     * 调用豆包流式API
     */
    async callDoubaoStreamingAPI(message, messageId) {
        return new Promise(async (resolve, reject) => {
            try {
                // 构建流式请求
                const response = await fetch('http://localhost:5000/api/chat/stream', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream'
                    },
                    body: JSON.stringify({
                        model: 'doubao-seed-1-6-thinking-250715',
                        messages: [
                            {
                                role: 'system',
                                content: '你是一个友好的企业客服助手，请简洁地回答用户问题。'
                            },
                            {
                                role: 'user',
                                content: message
                            }
                        ],
                        stream: true,
                        max_tokens: 2000,
                        temperature: 0.7
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`API调用失败: ${response.status}`);
                }
                
                // 处理流式响应
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let fullResponse = '';
                
                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) break;
                    
                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');
                    
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);
                            
                            if (data === '[DONE]') {
                                this.finishStreamingMessage(messageId, fullResponse);
                                resolve(fullResponse);
                                return;
                            }
                            
                            try {
                                const parsed = JSON.parse(data);
                                const content = parsed.choices?.[0]?.delta?.content || '';
                                
                                if (content) {
                                    fullResponse += content;
                                    this.appendToStreamingMessage(messageId, content);
                                }
                            } catch (e) {
                                // 忽略解析错误
                            }
                        }
                    }
                }
                
            } catch (error) {
                reject(error);
            }
        });
    }
    
    /**
     * 模拟流式响应
     */
    async simulateStreamingResponse(message, messageId) {
        return new Promise((resolve) => {
            // 生成模拟回复
            const responses = [
                `感谢您的问题："${message}"。`,
                '我正在为您分析这个问题。',
                '根据我的理解，这个问题涉及到几个方面：',
                '1. 首先需要确认具体的需求',
                '2. 然后分析可能的解决方案',
                '3. 最后提供详细的操作步骤',
                '希望这个回复对您有帮助！如果还有其他问题，请随时告诉我。'
            ];
            
            const fullResponse = responses.join('\n\n');
            let currentIndex = 0;
            
            const streamInterval = setInterval(() => {
                if (currentIndex >= fullResponse.length) {
                    clearInterval(streamInterval);
                    this.finishStreamingMessage(messageId, fullResponse);
                    resolve(fullResponse);
                    return;
                }
                
                const chunkEnd = Math.min(
                    currentIndex + this.streamConfig.chunkSize,
                    fullResponse.length
                );
                
                const chunk = fullResponse.slice(currentIndex, chunkEnd);
                this.appendToStreamingMessage(messageId, chunk);
                
                currentIndex = chunkEnd;
                
            }, this.streamConfig.typingSpeed);
        });
    }
    
    /**
     * 创建流式消息
     */
    createStreamingMessage() {
        const messageId = ++this.chatInterface.messageId;
        
        // 创建消息元素
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message-bubble ai streaming';
        messageDiv.dataset.messageId = messageId;
        messageDiv.id = `streaming-message-${messageId}`;
        
        messageDiv.innerHTML = `
            <div class="message-content">
                <div class="streaming-content" id="streaming-content-${messageId}"></div>
                <div class="streaming-cursor">|</div>
            </div>
            <div class="message-meta">
                <span class="message-time">${this.chatInterface.formatTime(new Date())}</span>
                <span class="streaming-indicator">正在输入...</span>
            </div>
        `;
        
        this.chatInterface.messagesContainer.appendChild(messageDiv);
        this.chatInterface.scrollToBottom();
        
        this.currentStreamingMessage = {
            id: messageId,
            element: messageDiv,
            content: '',
            isComplete: false
        };
        
        return messageId;
    }
    
    /**
     * 向流式消息追加内容
     */
    appendToStreamingMessage(messageId, content) {
        if (!this.currentStreamingMessage || this.currentStreamingMessage.id !== messageId) {
            return;
        }
        
        this.currentStreamingMessage.content += content;
        
        const contentElement = document.getElementById(`streaming-content-${messageId}`);
        if (contentElement) {
            // 实时Markdown渲染
            if (this.streamConfig.enableMarkdownRealtime) {
                contentElement.innerHTML = this.renderMarkdown(this.currentStreamingMessage.content);
            } else {
                contentElement.textContent = this.currentStreamingMessage.content;
            }
            
            // 自动滚动
            this.chatInterface.scrollToBottom();
            
            // 播放打字音效
            if (this.streamConfig.enableSoundEffects) {
                this.playTypingSound();
            }
        }
    }
    
    /**
     * 完成流式消息
     */
    finishStreamingMessage(messageId, fullContent) {
        if (!this.currentStreamingMessage || this.currentStreamingMessage.id !== messageId) {
            return;
        }
        
        const messageElement = document.getElementById(`streaming-message-${messageId}`);
        if (messageElement) {
            // 移除流式样式
            messageElement.classList.remove('streaming');
            
            // 移除光标
            const cursor = messageElement.querySelector('.streaming-cursor');
            if (cursor) cursor.remove();
            
            // 更新状态指示器
            const indicator = messageElement.querySelector('.streaming-indicator');
            if (indicator) indicator.remove();
            
            // 最终内容渲染
            const contentElement = messageElement.querySelector('.streaming-content');
            if (contentElement) {
                contentElement.innerHTML = this.renderMarkdown(fullContent);
            }
            
            // 添加消息操作按钮
            if (this.chatInterface.options.enableMessageActions) {
                const actionsHtml = this.chatInterface.createMessageActions({
                    type: 'ai',
                    content: fullContent
                });
                messageElement.insertAdjacentHTML('beforeend', actionsHtml);
            }
        }
        
        // 添加到消息历史
        this.chatInterface.messages.push({
            id: messageId,
            type: 'ai',
            content: fullContent,
            timestamp: new Date()
        });
        
        this.currentStreamingMessage = null;
        this.isStreaming = false;
        
        // 触发消息完成事件
        this.chatInterface.emit('streamingMessageComplete', {
            messageId,
            content: fullContent
        });
    }
    
    /**
     * 渲染Markdown
     */
    renderMarkdown(text) {
        return text
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>')
            .replace(/^/, '<p>')
            .replace(/$/, '</p>');
    }
    
    /**
     * 设置打字效果
     */
    setupTypingEffects() {
        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            .streaming-cursor {
                display: inline-block;
                animation: blink 1s infinite;
                color: var(--primary-color);
                font-weight: bold;
            }
            
            @keyframes blink {
                0%, 50% { opacity: 1; }
                51%, 100% { opacity: 0; }
            }
            
            .message-bubble.streaming {
                border-left: 3px solid var(--primary-color);
            }
            
            .streaming-indicator {
                color: var(--primary-color);
                font-size: 0.75rem;
                font-style: italic;
            }
            
            .streaming-content {
                min-height: 1.2em;
            }
        `;
        document.head.appendChild(style);
    }
    
    /**
     * 设置音效
     */
    setupSoundEffects() {
        if (!this.streamConfig.enableSoundEffects) return;
        
        // 创建音频上下文
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            this.lastSoundTime = 0;
        } catch (e) {
            console.warn('音频上下文创建失败，禁用音效');
            this.streamConfig.enableSoundEffects = false;
        }
    }
    
    /**
     * 播放打字音效
     */
    playTypingSound() {
        if (!this.audioContext || !this.streamConfig.enableSoundEffects) return;
        
        const now = Date.now();
        if (now - this.lastSoundTime < 100) return; // 限制音效频率
        
        this.lastSoundTime = now;
        
        // 生成简单的打字音效
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        oscillator.frequency.setValueAtTime(800 + Math.random() * 200, this.audioContext.currentTime);
        oscillator.type = 'square';
        
        gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.1);
        
        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.1);
    }
    
    /**
     * 停止当前流式消息
     */
    stopStreaming() {
        if (this.currentStreamingMessage && !this.currentStreamingMessage.isComplete) {
            this.finishStreamingMessage(
                this.currentStreamingMessage.id,
                this.currentStreamingMessage.content
            );
        }
    }
    
    /**
     * 更新配置
     */
    updateConfig(newConfig) {
        this.streamConfig = { ...this.streamConfig, ...newConfig };
    }
}

// 导出类
window.StreamingMessageHandler = StreamingMessageHandler;
