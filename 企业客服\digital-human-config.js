/**
 * 数字人配置文件
 * 请根据实际情况修改以下配置
 */

window.DIGITAL_HUMAN_CONFIG = {
    // 2D数字人图片配置
    image2D: {
        // 主图片 - 使用用户提供的红色西装女性客服图片
        primaryPath: '../数字人创作文件/customer-service-lady.jpg',
        
        // 备用图片路径列表
        fallbackPaths: [
            './digital-human.jpg',
            './digital-human.png',
            '../assets/digital-human.jpg'
        ],
        
        // 图片样式配置
        style: {
            width: '300px',
            height: '400px',
            objectFit: 'cover',  // 'cover' | 'contain' | 'fill'
            borderRadius: '12px'
        }
    },
    
    // 3D模型配置
    models3D: [
        {
            name: 'GLB模型1',
            path: './look1_nayong_glb_test.glb',
            enabled: true
        },
        {
            name: 'GLB模型2', 
            path: './look1_nayong_glb_test (1).glb',
            enabled: true
        },
        {
            name: '解压模型',
            path: './look1-nayong-glb-test/source/look1.glb',
            enabled: true
        }
    ],
    
    // 界面配置
    ui: {
        fullscreen: false,  // 不全屏，使用三栏布局
        showCloseButton: true,  // 显示关闭按钮
        animationsEnabled: true,  // 启用动画效果
        theme: 'gradient',  // 'gradient' | 'solid' | 'transparent'
        layout: 'three-column'  // 三栏布局
    },
    
    // WebSocket配置
    websocket: {
        url: 'ws://localhost:8080/ws',
        autoReconnect: true,
        reconnectInterval: 3000,
        maxReconnectAttempts: 5
    },
    
    // 火山引擎API配置
    doubaoAPI: {
        baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
        // 注意：不要在前端代码中存放真实的API Key。前端应通过后端代理调用第三方API。
        // 请在后端(例如 proxy server) 设置 ARK_API_KEY 环境变量并通过受控接口访问。
        apiKey: 'YOUR_API_KEY_HERE',
        modelId: 'doubao-seed-1-6-thinking-250715',
        enableCORS: false  // 服务器端处理CORS
    }
};

console.log('📋 数字人配置已加载');