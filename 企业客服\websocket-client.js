/**
 * WebSocket客户端 - OA系统集成
 * Linus式设计：简单协议，可靠连接，零破坏性
 * 
 * 功能：
 * 1. 扩展现有OA WebSocket协议
 * 2. 处理AI聊天消息  
 * 3. 触发3D动画和表情
 * 4. 保持向后兼容性
 */

class OAWebSocketClient {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = 3000;
        
        // 消息队列 - 连接失败时缓存
        this.messageQueue = [];
        
        // 事件回调
        this.callbacks = {
            onMessage: null,
            onConnected: null,
            onDisconnected: null,
            onError: null
        };
    }
    
    /**
     * 初始化WebSocket连接
     * 注意：需要根据实际OA系统的WebSocket地址修改
     */
    async initWebSocket() {
        try {
            // 根据页面协议自动选择 ws/wss，并优先使用配置中的 URL
            let configured = window.DIGITAL_HUMAN_CONFIG && window.DIGITAL_HUMAN_CONFIG.websocket && window.DIGITAL_HUMAN_CONFIG.websocket.url;
            let wsUrl = configured || 'ws://localhost:8080/ws';

            // 如果页面在 https 下，优先使用 wss 协议
            try {
                const pageIsSecure = window.location.protocol === 'https:';
                if (pageIsSecure) {
                    wsUrl = wsUrl.replace(/^ws:/i, 'wss:');
                }
            } catch (e) {
                // ignore
            }

            this.ws = new WebSocket(wsUrl);
            this.setupEventHandlers();
            
        } catch (error) {
            console.error('WebSocket连接失败:', error);
            this.handleError(error);
        }
    }
    
    /**
     * 设置WebSocket事件处理器
     */
    setupEventHandlers() {
        this.ws.onopen = () => {
            console.log('WebSocket连接成功');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            
            // 处理排队的消息
            this.flushMessageQueue();
            
            if (this.callbacks.onConnected) {
                this.callbacks.onConnected();
            }
            
            digitalHuman.updateStatus('服务器已连接');
        };
        
        this.ws.onmessage = (event) => {
            try {
                const message = JSON.parse(event.data);
                this.handleMessage(message);
            } catch (error) {
                console.error('消息解析失败:', event.data, error);
            }
        };
        
        this.ws.onclose = () => {
            console.log('WebSocket连接关闭');
            this.isConnected = false;
            
            if (this.callbacks.onDisconnected) {
                this.callbacks.onDisconnected();
            }
            
            digitalHuman.updateStatus('连接断开，尝试重连...');
            this.attemptReconnect();
        };
        
        this.ws.onerror = (error) => {
            console.error('WebSocket错误:', error);
            this.handleError(error);
        };
    }
    
    /**
     * 处理接收到的消息
     * 兼容现有OA协议 + 新增AI聊天协议
     */
    handleMessage(message) {
        console.log('收到消息:', message);
        
        switch (message.type) {
            case 'ai_chat_response':
                this.handleAIChatResponse(message.data);
                break;
                
            case 'system_status':
                this.handleSystemStatus(message.data);
                break;
                
            case 'user_auth':
                this.handleUserAuth(message.data);
                break;
                
            // 保持OA原有消息类型的兼容性
            case 'oa_chat_message':
            case 'oa_notification':
            case 'oa_status_update':
                // 传递给OA系统处理，不干预
                if (window.OASystem && window.OASystem.handleMessage) {
                    window.OASystem.handleMessage(message);
                }
                break;
                
            default:
                console.warn('未知消息类型:', message.type);
        }
        
        // 通用回调
        if (this.callbacks.onMessage) {
            this.callbacks.onMessage(message);
        }
    }
    
    /**
     * 处理AI聊天响应
     * 数据格式：{ response, animation, expression, audio_url? }
     */
    handleAIChatResponse(data) {
        console.log('AI响应:', data);
        
        // 显示AI回复文本
        if (data.response) {
            this.addMessageToChat('ai', data.response);
        }
        
        // 触发表情变化
        if (data.expression && digitalHuman.avatar) {
            digitalHuman.setExpression(data.expression);
        }
        
        // 触发动画
        if (data.animation && digitalHuman.avatar) {
            digitalHuman.testAnimation(data.animation);
        }
        
        // 播放语音（如果有）
        if (data.audio_url) {
            this.playAudio(data.audio_url);
        }
    }
    
    /**
     * 处理系统状态消息
     */
    handleSystemStatus(data) {
        if (data.status) {
            digitalHuman.updateStatus(data.status);
        }
    }
    
    /**
     * 处理用户认证（使用OA系统的认证）
     */
    handleUserAuth(data) {
        if (data.success) {
            console.log('用户认证成功:', data.user);
            digitalHuman.updateStatus('认证成功');
        } else {
            console.error('用户认证失败:', data.error);
            digitalHuman.updateStatus('认证失败');
        }
    }
    
    /**
     * 发送AI聊天请求
     * 格式：{ type: 'ai_chat_request', data: { user_id, message, session_id } }
     */
    sendAIChatRequest(message) {
        const requestData = {
            type: 'ai_chat_request',
            data: {
                user_id: this.getCurrentUserId(), // 从OA系统获取
                message: message,
                session_id: this.getCurrentSessionId(), // 从OA系统获取
                timestamp: Date.now()
            }
        };
        
        this.send(requestData);
    }
    
    /**
     * 发送消息（带队列支持）
     */
    send(message) {
        if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        } else {
            // 连接断开时加入队列
            this.messageQueue.push(message);
            console.warn('消息已加入队列，等待连接恢复');
        }
    }
    
    /**
     * 处理消息队列
     */
    flushMessageQueue() {
        while (this.messageQueue.length > 0) {
            const message = this.messageQueue.shift();
            this.send(message);
        }
    }
    
    /**
     * 重连机制
     */
    attemptReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('重连次数超限，停止重连');
            digitalHuman.updateStatus('连接失败');
            return;
        }
        
        this.reconnectAttempts++;
        
        setTimeout(() => {
            console.log(`第${this.reconnectAttempts}次重连尝试...`);
            this.initWebSocket();
        }, this.reconnectInterval);
    }
    
    /**
     * 错误处理
     */
    handleError(error) {
        console.error('WebSocket错误:', error);
        
        if (this.callbacks.onError) {
            this.callbacks.onError(error);
        }
        
        digitalHuman.updateStatus('连接错误');
    }
    
    /**
     * 从OA系统获取当前用户ID
     * TODO: 根据实际OA系统API调整
     */
    getCurrentUserId() {
        // 假设OA系统在全局对象中暴露用户信息
        if (window.OASystem && window.OASystem.currentUser) {
            return window.OASystem.currentUser.id;
        }
        return 'anonymous_user'; // 默认值
    }
    
    /**
     * 从OA系统获取当前会话ID
     * TODO: 根据实际OA系统API调整
     */
    getCurrentSessionId() {
        if (window.OASystem && window.OASystem.currentSession) {
            return window.OASystem.currentSession.id;
        }
        return 'default_session_' + Date.now();
    }
    
    /**
     * 在聊天界面添加消息
     */
    addMessageToChat(sender, text) {
        const messagesEl = document.getElementById('messages');
        const messageEl = document.createElement('div');
        messageEl.className = `message ${sender}`;
        messageEl.textContent = text;
        messagesEl.appendChild(messageEl);
        messagesEl.scrollTop = messagesEl.scrollHeight;
    }
    
    /**
     * 播放音频
     */
    playAudio(audioUrl) {
        try {
            const audio = new Audio(audioUrl);
            audio.play().catch(error => {
                console.error('音频播放失败:', error);
            });
        } catch (error) {
            console.error('音频创建失败:', error);
        }
    }
    
    /**
     * 关闭连接
     */
    close() {
        if (this.ws) {
            this.ws.close();
        }
    }
}

// 扩展DigitalHuman类添加WebSocket功能
DigitalHuman.prototype.initWebSocket = function() {
    this.wsClient = new OAWebSocketClient();
    
    // 设置回调
    this.wsClient.callbacks.onConnected = () => {
        this.isConnected = true;
        this.updateStatus('系统就绪');
    };
    
    this.wsClient.callbacks.onDisconnected = () => {
        this.isConnected = false;
    };
    
    this.wsClient.callbacks.onError = (error) => {
        this.updateStatus('连接错误');
    };
    
    // 启动连接
    this.wsClient.initWebSocket();
};

// 发送消息方法 - 直接调用豆包API（无WebSocket依赖）
DigitalHuman.prototype.sendMessage = async function() {
    const input = document.getElementById('message-input');
    const message = input.value.trim();
    
    if (!message) return;
    
    // 显示用户消息
    this.addMessageToChat('user', message);
    
    // 清空输入框
    input.value = '';
    
    // 思考状态
    this.setExpression('thinking');
    
    try {
        // 直接调用豆包API
        if (this.doubaoAPI) {
            console.log('📤 发送消息:', message);
            const aiReply = await this.doubaoAPI.sendMessage(message);
            this.addMessageToChat('ai', aiReply);
            
            // 成功表情
            this.setExpression('happy');
            setTimeout(() => this.setExpression('neutral'), 2000);
        } else {
            throw new Error('豆包API未初始化');
        }
    } catch (error) {
        console.error('AI回复失败:', error);
        this.addMessageToChat('ai', '抱歉，我现在无法回复您的问题。请检查豆包API服务器是否启动。');
        this.setExpression('concern');
        setTimeout(() => this.setExpression('neutral'), 2000);
    }
};

// 添加消息到聊天界面 - 独立函数
DigitalHuman.prototype.addMessageToChat = function(sender, message) {
    const messagesContainer = document.getElementById('messages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}`;
    messageDiv.textContent = message;
    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
};