/**
 * 豆包API集成示例 - C后端实现
 * Linus式C代码：简洁、高效、可靠
 * 
 * 编译命令：
 * gcc -o doubao_server doubao_server.c -lcurl -ljson-c -lwebsockets
 * 
 * 依赖库：
 * - libcurl (HTTP客户端)
 * - json-c (JSON解析)
 * - libwebsockets (WebSocket服务器)
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <curl/curl.h>
#include <json-c/json.h>
#include <libwebsockets.h>

// 配置常量
#define MAX_MESSAGE_SIZE 4096
#define MAX_CLIENTS 100
#define WEBSOCKET_PORT 8080

// 豆包API配置
#define DOUBAO_API_URL "https://ark.cn-beijing.volces.com/api/v3/chat/completions"
#define DOUBAO_API_KEY "your_doubao_api_key_here"  // 替换为实际API密钥
#define DOUBAO_MODEL "ep-20241201123456-abcde"     // 替换为实际模型ID

// HTTP响应结构
struct http_response {
    char *data;
    size_t size;
};

// 客户端会话结构
struct client_session {
    struct lws *wsi;
    char user_id[64];
    char session_id[128];
    int is_active;
};

// 全局变量
static struct client_session clients[MAX_CLIENTS];
static int client_count = 0;

/**
 * HTTP响应回调 - libcurl
 */
static size_t write_response_callback(void *contents, size_t size, size_t nmemb, struct http_response *response) {
    size_t realsize = size * nmemb;
    
    char *ptr = realloc(response->data, response->size + realsize + 1);
    if (!ptr) {
        printf("内存分配失败\n");
        return 0;
    }
    
    response->data = ptr;
    memcpy(&(response->data[response->size]), contents, realsize);
    response->size += realsize;
    response->data[response->size] = 0;
    
    return realsize;
}

/**
 * 调用豆包API
 * 输入：用户消息
 * 输出：AI回复 + 动画指令
 */
char* call_doubao_api(const char* user_message, const char* user_id) {
    CURL *curl;
    CURLcode res;
    struct http_response response = {0};
    
    curl = curl_easy_init();
    if (!curl) {
        printf("CURL初始化失败\n");
        return NULL;
    }
    
    // 构建请求JSON
    json_object *json_request = json_object_new_object();
    json_object *model = json_object_new_string(DOUBAO_MODEL);
    json_object *messages = json_object_new_array();
    
    // 用户消息
    json_object *user_msg = json_object_new_object();
    json_object *role = json_object_new_string("user");
    json_object *content = json_object_new_string(user_message);
    json_object_object_add(user_msg, "role", role);
    json_object_object_add(user_msg, "content", content);
    json_array_add(messages, user_msg);
    
    json_object_object_add(json_request, "model", model);
    json_object_object_add(json_request, "messages", messages);
    
    // 添加企业客服系统提示
    json_object *system_msg = json_object_new_object();
    json_object *system_role = json_object_new_string("system");
    json_object *system_content = json_object_new_string(
        "你是一个专业的企业客服，需要友好、准确地回答用户问题。"
        "根据回复内容，在最后添加合适的动画指令：greeting(打招呼), pointing(指向), thinking(思考), explaining(解释)。"
        "格式：[回复内容]|[动画指令]|[表情:happy/neutral/thinking/concern]"
    );
    json_object_object_add(system_msg, "role", system_role);
    json_object_object_add(system_msg, "content", system_content);
    json_array_add(messages, system_msg);
    
    const char *json_string = json_object_to_json_string(json_request);
    
    // HTTP请求头
    struct curl_slist *headers = NULL;
    char auth_header[256];
    snprintf(auth_header, sizeof(auth_header), "Authorization: Bearer %s", DOUBAO_API_KEY);
    
    headers = curl_slist_append(headers, "Content-Type: application/json");
    headers = curl_slist_append(headers, auth_header);
    
    // 设置CURL选项
    curl_easy_setopt(curl, CURLOPT_URL, DOUBAO_API_URL);
    curl_easy_setopt(curl, CURLOPT_POSTFIELDS, json_string);
    curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, write_response_callback);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
    curl_easy_setopt(curl, CURLOPT_TIMEOUT, 30L); // 30秒超时
    
    // 执行请求
    res = curl_easy_perform(curl);
    
    // 清理
    curl_slist_free_all(headers);
    curl_easy_cleanup(curl);
    json_object_put(json_request);
    
    if (res != CURLE_OK) {
        printf("CURL请求失败: %s\n", curl_easy_strerror(res));
        if (response.data) free(response.data);
        return NULL;
    }
    
    return response.data;
}

/**
 * 解析豆包API响应，提取回复和动画指令
 */
char* parse_doubao_response(const char* api_response) {
    json_object *json_response = json_tokener_parse(api_response);
    if (!json_response) {
        printf("JSON解析失败\n");
        return NULL;
    }
    
    // 提取AI回复
    json_object *choices, *first_choice, *message, *content;
    if (!json_object_object_get_ex(json_response, "choices", &choices) ||
        json_object_get_type(choices) != json_type_array ||
        json_array_length(choices) == 0) {
        printf("API响应格式错误\n");
        json_object_put(json_response);
        return NULL;
    }
    
    first_choice = json_array_get_idx(choices, 0);
    if (!json_object_object_get_ex(first_choice, "message", &message) ||
        !json_object_object_get_ex(message, "content", &content)) {
        printf("无法提取消息内容\n");
        json_object_put(json_response);
        return NULL;
    }
    
    const char *ai_response = json_object_get_string(content);
    
    // 解析格式：[回复内容]|[动画指令]|[表情]
    char *response_copy = strdup(ai_response);
    char *text_part = strtok(response_copy, "|");
    char *animation_part = strtok(NULL, "|");
    char *expression_part = strtok(NULL, "|");
    
    // 构建WebSocket响应JSON
    json_object *ws_response = json_object_new_object();
    json_object *type = json_object_new_string("ai_chat_response");
    json_object *data = json_object_new_object();
    
    json_object_object_add(data, "response", json_object_new_string(text_part ? text_part : ai_response));
    json_object_object_add(data, "animation", json_object_new_string(animation_part ? animation_part : "neutral"));
    json_object_object_add(data, "expression", json_object_new_string(expression_part ? expression_part : "neutral"));
    json_object_object_add(data, "timestamp", json_object_new_int64(time(NULL)));
    
    json_object_object_add(ws_response, "type", type);
    json_object_object_add(ws_response, "data", data);
    
    char *result = strdup(json_object_to_json_string(ws_response));
    
    // 清理
    free(response_copy);
    json_object_put(json_response);
    json_object_put(ws_response);
    
    return result;
}

/**
 * WebSocket回调函数
 */
static int websocket_callback(struct lws *wsi, enum lws_callback_reasons reason,
                             void *user, void *in, size_t len) {
    switch (reason) {
        case LWS_CALLBACK_ESTABLISHED: {
            printf("WebSocket连接建立\n");
            
            // 找到空闲的客户端槽位
            for (int i = 0; i < MAX_CLIENTS; i++) {
                if (!clients[i].is_active) {
                    clients[i].wsi = wsi;
                    clients[i].is_active = 1;
                    snprintf(clients[i].user_id, sizeof(clients[i].user_id), "user_%d", i);
                    snprintf(clients[i].session_id, sizeof(clients[i].session_id), "session_%ld", time(NULL));
                    client_count++;
                    break;
                }
            }
            break;
        }
        
        case LWS_CALLBACK_RECEIVE: {
            if (len == 0) break;
            
            char message[MAX_MESSAGE_SIZE];
            memcpy(message, in, len);
            message[len] = '\0';
            
            printf("收到消息: %s\n", message);
            
            // 解析WebSocket消息
            json_object *json_msg = json_tokener_parse(message);
            if (!json_msg) {
                printf("JSON解析失败\n");
                break;
            }
            
            json_object *type_obj, *data_obj;
            if (!json_object_object_get_ex(json_msg, "type", &type_obj)) {
                json_object_put(json_msg);
                break;
            }
            
            const char *msg_type = json_object_get_string(type_obj);
            
            if (strcmp(msg_type, "ai_chat_request") == 0) {
                // 处理AI聊天请求
                if (json_object_object_get_ex(json_msg, "data", &data_obj)) {
                    json_object *user_msg_obj, *user_id_obj;
                    
                    if (json_object_object_get_ex(data_obj, "message", &user_msg_obj) &&
                        json_object_object_get_ex(data_obj, "user_id", &user_id_obj)) {
                        
                        const char *user_message = json_object_get_string(user_msg_obj);
                        const char *user_id = json_object_get_string(user_id_obj);
                        
                        printf("处理用户消息: %s (用户: %s)\n", user_message, user_id);
                        
                        // 调用豆包API
                        char *api_response = call_doubao_api(user_message, user_id);
                        if (api_response) {
                            char *ws_response = parse_doubao_response(api_response);
                            if (ws_response) {
                                // 发送响应到客户端
                                size_t response_len = strlen(ws_response);
                                unsigned char *buf = malloc(LWS_PRE + response_len);
                                memcpy(&buf[LWS_PRE], ws_response, response_len);
                                
                                lws_write(wsi, &buf[LWS_PRE], response_len, LWS_WRITE_TEXT);
                                
                                free(buf);
                                free(ws_response);
                            }
                            free(api_response);
                        }
                    }
                }
            }
            
            json_object_put(json_msg);
            break;
        }
        
        case LWS_CALLBACK_CLOSED: {
            printf("WebSocket连接关闭\n");
            
            // 清理客户端信息
            for (int i = 0; i < MAX_CLIENTS; i++) {
                if (clients[i].wsi == wsi) {
                    clients[i].is_active = 0;
                    clients[i].wsi = NULL;
                    client_count--;
                    break;
                }
            }
            break;
        }
        
        default:
            break;
    }
    
    return 0;
}

// WebSocket协议定义
static struct lws_protocols protocols[] = {
    {
        "digital-human-protocol",
        websocket_callback,
        0,
        MAX_MESSAGE_SIZE,
    },
    { NULL, NULL, 0, 0 } // 结束标记
};

/**
 * 主函数
 */
int main(void) {
    struct lws_context_creation_info info;
    struct lws_context *context;
    
    printf("启动3D数字人WebSocket服务器...\n");
    
    // 初始化libcurl
    curl_global_init(CURL_GLOBAL_DEFAULT);
    
    // 清零配置
    memset(&info, 0, sizeof(info));
    
    // 配置WebSocket服务器
    info.port = WEBSOCKET_PORT;
    info.protocols = protocols;
    info.gid = -1;
    info.uid = -1;
    info.options = LWS_SERVER_OPTION_HTTP_HEADERS_SECURITY_BEST_PRACTICES_ENFORCE;
    
    // 创建WebSocket上下文
    context = lws_create_context(&info);
    if (!context) {
        printf("WebSocket上下文创建失败\n");
        return -1;
    }
    
    printf("服务器启动成功，监听端口 %d\n", WEBSOCKET_PORT);
    printf("WebSocket地址: ws://localhost:%d\n", WEBSOCKET_PORT);
    
    // 事件循环
    while (1) {
        lws_service(context, 50); // 50ms超时
    }
    
    // 清理
    lws_context_destroy(context);
    curl_global_cleanup();
    
    return 0;
}