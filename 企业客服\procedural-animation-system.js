/**
 * 程序化动画系统
 * Linus式设计：自然流畅，性能优化，真实感强
 * 
 * 核心特性：
 * - 自然呼吸动画
 * - 智能眼球追踪
 * - 微表情系统
 * - 自然手势生成
 * - 程序化身体动作
 */

class ProceduralAnimationSystem {
    constructor(avatar) {
        this.avatar = avatar;
        this.isActive = false;
        
        // 子系统
        this.breathingSystem = new BreathingAnimator(avatar);
        this.eyeTrackingSystem = new EyeTrackingAnimator(avatar);
        this.microExpressionSystem = new MicroExpressionAnimator(avatar);
        this.gestureSystem = new NaturalGestureAnimator(avatar);
        this.idleAnimationSystem = new IdleAnimationSystem(avatar);
        
        // 动画状态
        this.animationState = {
            isBreathing: true,
            isBlinking: true,
            isTracking: false,
            currentGesture: null,
            idleMode: true
        };
        
        // 性能配置
        this.performanceConfig = {
            targetFPS: 60,
            updateInterval: 16.67, // ms
            enableLOD: true,
            qualityLevel: 'high'
        };
        
        console.log('🤖 程序化动画系统初始化');
    }
    
    /**
     * 启动动画系统
     */
    start() {
        if (this.isActive) return;
        
        this.isActive = true;
        
        // 启动各个子系统
        this.breathingSystem.start();
        this.eyeTrackingSystem.start();
        this.microExpressionSystem.start();
        this.gestureSystem.start();
        this.idleAnimationSystem.start();
        
        // 启动主更新循环
        this.startUpdateLoop();
        
        console.log('🎬 程序化动画系统已启动');
    }
    
    /**
     * 停止动画系统
     */
    stop() {
        this.isActive = false;
        
        this.breathingSystem.stop();
        this.eyeTrackingSystem.stop();
        this.microExpressionSystem.stop();
        this.gestureSystem.stop();
        this.idleAnimationSystem.stop();
        
        console.log('⏹️ 程序化动画系统已停止');
    }
    
    /**
     * 启动更新循环
     */
    startUpdateLoop() {
        const lastTime = { value: Date.now() };
        
        const updateLoop = () => {
            if (!this.isActive) return;
            
            const currentTime = Date.now();
            const deltaTime = (currentTime - lastTime.value) / 1000;
            lastTime.value = currentTime;
            
            this.update(deltaTime);
            
            setTimeout(() => {
                requestAnimationFrame(updateLoop);
            }, this.performanceConfig.updateInterval);
        };
        
        updateLoop();
    }
    
    /**
     * 主更新方法
     */
    update(deltaTime) {
        if (!this.avatar) return;
        
        // 更新各个子系统
        if (this.animationState.isBreathing) {
            this.breathingSystem.update(deltaTime);
        }
        
        if (this.animationState.isBlinking) {
            this.eyeTrackingSystem.update(deltaTime);
        }
        
        this.microExpressionSystem.update(deltaTime);
        
        if (this.animationState.idleMode) {
            this.idleAnimationSystem.update(deltaTime);
        }
        
        this.gestureSystem.update(deltaTime);
    }
    
    /**
     * 设置眼球追踪目标
     */
    setEyeTrackingTarget(target) {
        this.eyeTrackingSystem.setTarget(target);
        this.animationState.isTracking = true;
    }
    
    /**
     * 停止眼球追踪
     */
    stopEyeTracking() {
        this.eyeTrackingSystem.clearTarget();
        this.animationState.isTracking = false;
    }
    
    /**
     * 播放手势动画
     */
    playGesture(gestureType, intensity = 1.0) {
        this.gestureSystem.playGesture(gestureType, intensity);
        this.animationState.currentGesture = gestureType;
    }
    
    /**
     * 设置呼吸强度
     */
    setBreathingIntensity(intensity) {
        this.breathingSystem.setIntensity(intensity);
    }
    
    /**
     * 启用/禁用微表情
     */
    setMicroExpressionsEnabled(enabled) {
        this.microExpressionSystem.setEnabled(enabled);
    }
}

/**
 * 自然呼吸动画器
 */
class BreathingAnimator {
    constructor(avatar) {
        this.avatar = avatar;
        this.isActive = false;
        
        this.config = {
            intensity: 0.02,
            frequency: 0.25,  // 每秒0.25次，即4秒一个周期
            chestBone: null,
            shoulderBones: [],
            ribBones: []
        };
        
        this.state = {
            phase: 0,
            lastUpdate: 0
        };
        
        this.findBreathingBones();
    }
    
    findBreathingBones() {
        if (!this.avatar) return;
        
        this.avatar.traverse((child) => {
            if (child.type === 'Bone') {
                const name = child.name.toLowerCase();
                
                // 查找胸部骨骼
                if (name.includes('chest') || name.includes('spine2') || name.includes('torso')) {
                    this.config.chestBone = child;
                }
                
                // 查找肩膀骨骼
                if (name.includes('shoulder') || name.includes('clavicle')) {
                    this.config.shoulderBones.push(child);
                }
                
                // 查找肋骨区域
                if (name.includes('rib') || name.includes('spine1')) {
                    this.config.ribBones.push(child);
                }
            }
        });
        
        console.log('🫁 找到呼吸骨骼:', {
            chest: !!this.config.chestBone,
            shoulders: this.config.shoulderBones.length,
            ribs: this.config.ribBones.length
        });
    }
    
    start() {
        this.isActive = true;
        this.state.lastUpdate = Date.now();
    }
    
    stop() {
        this.isActive = false;
        this.resetBreathingPose();
    }
    
    update(deltaTime) {
        if (!this.isActive) return;
        
        this.state.phase += deltaTime * this.config.frequency;
        
        // 使用正弦波生成自然呼吸模式
        const breathCycle = Math.sin(this.state.phase * Math.PI * 2);
        const breathIntensity = this.config.intensity * breathCycle;
        
        this.applyBreathingAnimation(breathIntensity);
    }
    
    applyBreathingAnimation(intensity) {
        // 胸部扩张
        if (this.config.chestBone) {
            this.config.chestBone.scale.y = 1 + intensity;
            this.config.chestBone.scale.z = 1 + intensity * 0.5;
        }
        
        // 肩膀轻微上下移动
        this.config.shoulderBones.forEach(bone => {
            bone.position.y += intensity * 0.01;
        });
        
        // 肋骨区域扩张
        this.config.ribBones.forEach(bone => {
            bone.scale.x = 1 + intensity * 0.3;
            bone.scale.z = 1 + intensity * 0.3;
        });
    }
    
    resetBreathingPose() {
        if (this.config.chestBone) {
            this.config.chestBone.scale.set(1, 1, 1);
        }
        
        this.config.shoulderBones.forEach(bone => {
            bone.position.y = 0;
        });
        
        this.config.ribBones.forEach(bone => {
            bone.scale.set(1, 1, 1);
        });
    }
    
    setIntensity(intensity) {
        this.config.intensity = Math.max(0, Math.min(0.1, intensity));
    }
}

/**
 * 眼球追踪动画器
 */
class EyeTrackingAnimator {
    constructor(avatar) {
        this.avatar = avatar;
        this.isActive = false;
        
        this.config = {
            smoothing: 0.1,
            maxRotation: Math.PI / 6, // 30度最大转动
            blinkFrequency: 3.0,      // 平均3秒眨眼一次
            blinkDuration: 0.15       // 眨眼持续0.15秒
        };
        
        this.state = {
            target: null,
            currentTarget: new THREE.Vector3(),
            eyeBones: [],
            eyelidBones: [],
            lastBlink: 0,
            isBlinking: false,
            blinkPhase: 0
        };
        
        this.findEyeBones();
    }
    
    findEyeBones() {
        if (!this.avatar) return;
        
        this.avatar.traverse((child) => {
            if (child.type === 'Bone') {
                const name = child.name.toLowerCase();
                
                if (name.includes('eye') && !name.includes('lid')) {
                    this.state.eyeBones.push(child);
                }
                
                if (name.includes('eyelid') || (name.includes('eye') && name.includes('lid'))) {
                    this.state.eyelidBones.push(child);
                }
            }
        });
        
        console.log('👁️ 找到眼部骨骼:', {
            eyes: this.state.eyeBones.length,
            eyelids: this.state.eyelidBones.length
        });
    }
    
    start() {
        this.isActive = true;
        this.state.lastBlink = Date.now();
    }
    
    stop() {
        this.isActive = false;
        this.resetEyePose();
    }
    
    update(deltaTime) {
        if (!this.isActive) return;
        
        // 更新眼球追踪
        this.updateEyeTracking(deltaTime);
        
        // 更新眨眼动画
        this.updateBlinking(deltaTime);
    }
    
    updateEyeTracking(deltaTime) {
        if (this.state.target) {
            // 计算目标方向
            const eyeDirection = this.calculateEyeDirection(this.state.target);
            
            // 平滑插值到目标方向
            this.state.currentTarget.lerp(eyeDirection, this.config.smoothing);
            
            // 应用到眼球骨骼
            this.setEyeRotation(this.state.currentTarget.x, this.state.currentTarget.y);
        }
    }
    
    updateBlinking(deltaTime) {
        const now = Date.now();
        
        // 检查是否需要眨眼
        if (!this.state.isBlinking && 
            (now - this.state.lastBlink) > (this.config.blinkFrequency * 1000 + Math.random() * 2000)) {
            this.startBlink();
        }
        
        // 更新眨眼动画
        if (this.state.isBlinking) {
            this.state.blinkPhase += deltaTime / this.config.blinkDuration;
            
            if (this.state.blinkPhase >= 1.0) {
                this.endBlink();
            } else {
                this.applyBlinkAnimation(this.state.blinkPhase);
            }
        }
    }
    
    calculateEyeDirection(target) {
        // 简化的眼球方向计算
        const avatarPosition = this.avatar.position;
        const direction = new THREE.Vector3().subVectors(target, avatarPosition).normalize();
        
        // 转换为眼球旋转角度
        const eyeDirection = new THREE.Vector3(
            Math.atan2(direction.x, direction.z),
            Math.atan2(direction.y, Math.sqrt(direction.x * direction.x + direction.z * direction.z)),
            0
        );
        
        // 限制旋转范围
        eyeDirection.x = Math.max(-this.config.maxRotation, Math.min(this.config.maxRotation, eyeDirection.x));
        eyeDirection.y = Math.max(-this.config.maxRotation, Math.min(this.config.maxRotation, eyeDirection.y));
        
        return eyeDirection;
    }
    
    setEyeRotation(x, y) {
        this.state.eyeBones.forEach(bone => {
            bone.rotation.x = y;
            bone.rotation.y = x;
        });
    }
    
    startBlink() {
        this.state.isBlinking = true;
        this.state.blinkPhase = 0;
        this.state.lastBlink = Date.now();
    }
    
    endBlink() {
        this.state.isBlinking = false;
        this.state.blinkPhase = 0;
        this.resetEyelids();
    }
    
    applyBlinkAnimation(phase) {
        // 使用sin函数创建自然的眨眼动画
        const blinkAmount = Math.sin(phase * Math.PI);
        
        this.state.eyelidBones.forEach(bone => {
            bone.rotation.x = blinkAmount * Math.PI / 4; // 45度最大闭合
        });
        
        // 如果有BlendShape支持，也可以应用到morphTargets
        if (window.enterpriseDigitalHuman && window.enterpriseDigitalHuman.morphTargets) {
            const morphTargets = window.enterpriseDigitalHuman.morphTargets;
            Object.values(morphTargets).forEach(mesh => {
                if (mesh.morphTargetInfluences && mesh.morphTargetDictionary) {
                    const blinkIndex = mesh.morphTargetDictionary['blink'] || 
                                     mesh.morphTargetDictionary['eyeBlink'] ||
                                     mesh.morphTargetDictionary['eye_blink'];
                    
                    if (blinkIndex !== undefined) {
                        mesh.morphTargetInfluences[blinkIndex] = blinkAmount;
                    }
                }
            });
        }
    }
    
    resetEyelids() {
        this.state.eyelidBones.forEach(bone => {
            bone.rotation.x = 0;
        });
        
        // 重置BlendShape
        if (window.enterpriseDigitalHuman && window.enterpriseDigitalHuman.morphTargets) {
            const morphTargets = window.enterpriseDigitalHuman.morphTargets;
            Object.values(morphTargets).forEach(mesh => {
                if (mesh.morphTargetInfluences && mesh.morphTargetDictionary) {
                    const blinkIndex = mesh.morphTargetDictionary['blink'] || 
                                     mesh.morphTargetDictionary['eyeBlink'] ||
                                     mesh.morphTargetDictionary['eye_blink'];
                    
                    if (blinkIndex !== undefined) {
                        mesh.morphTargetInfluences[blinkIndex] = 0;
                    }
                }
            });
        }
    }
    
    resetEyePose() {
        this.setEyeRotation(0, 0);
        this.resetEyelids();
    }
    
    setTarget(target) {
        this.state.target = target;
    }
    
    clearTarget() {
        this.state.target = null;
        this.state.currentTarget.set(0, 0, 0);
    }
}

/**
 * 微表情动画器
 */
class MicroExpressionAnimator {
    constructor(avatar) {
        this.avatar = avatar;
        this.isActive = false;
        this.enabled = true;
        
        this.config = {
            frequency: 0.1,        // 10%几率每秒触发
            intensity: 0.3,        // 微表情强度
            duration: 500,         // 持续时间ms
            cooldown: 2000         // 冷却时间ms
        };
        
        this.state = {
            lastTrigger: 0,
            activeMicroExpression: null
        };
        
        // 微表情定义
        this.microExpressions = {
            'eyebrow_flash': {
                duration: 300,
                keyframes: [
                    { time: 0, values: { browUp: 0 } },
                    { time: 0.5, values: { browUp: 0.4 } },
                    { time: 1, values: { browUp: 0 } }
                ]
            },
            'subtle_smile': {
                duration: 800,
                keyframes: [
                    { time: 0, values: { smile: 0 } },
                    { time: 0.3, values: { smile: 0.2 } },
                    { time: 0.7, values: { smile: 0.2 } },
                    { time: 1, values: { smile: 0 } }
                ]
            },
            'nostril_flare': {
                duration: 200,
                keyframes: [
                    { time: 0, values: { noseFlare: 0 } },
                    { time: 0.5, values: { noseFlare: 0.3 } },
                    { time: 1, values: { noseFlare: 0 } }
                ]
            },
            'lip_compression': {
                duration: 400,
                keyframes: [
                    { time: 0, values: { lipPress: 0 } },
                    { time: 0.4, values: { lipPress: 0.3 } },
                    { time: 1, values: { lipPress: 0 } }
                ]
            }
        };
    }
    
    start() {
        this.isActive = true;
        this.state.lastTrigger = Date.now();
    }
    
    stop() {
        this.isActive = false;
    }
    
    update(deltaTime) {
        if (!this.isActive || !this.enabled) return;
        
        const now = Date.now();
        
        // 检查是否可以触发新的微表情
        if (!this.state.activeMicroExpression && 
            (now - this.state.lastTrigger) > this.config.cooldown &&
            Math.random() < this.config.frequency * deltaTime) {
            
            this.triggerRandomMicroExpression();
        }
        
        // 更新当前微表情
        if (this.state.activeMicroExpression) {
            this.updateActiveMicroExpression(now);
        }
    }
    
    triggerRandomMicroExpression() {
        const expressions = Object.keys(this.microExpressions);
        const randomExpression = expressions[Math.floor(Math.random() * expressions.length)];
        
        this.playMicroExpression(randomExpression);
    }
    
    playMicroExpression(expressionName) {
        const expression = this.microExpressions[expressionName];
        if (!expression) return;
        
        this.state.activeMicroExpression = {
            name: expressionName,
            config: expression,
            startTime: Date.now(),
            currentFrame: 0
        };
        
        this.state.lastTrigger = Date.now();
        
        console.log(`✨ 微表情: ${expressionName}`);
    }
    
    updateActiveMicroExpression(currentTime) {
        const active = this.state.activeMicroExpression;
        if (!active) return;
        
        const elapsed = currentTime - active.startTime;
        const progress = elapsed / active.config.duration;
        
        if (progress >= 1.0) {
            // 微表情结束
            this.state.activeMicroExpression = null;
            return;
        }
        
        // 插值计算当前表情值
        const currentValues = this.interpolateKeyframes(active.config.keyframes, progress);
        this.applyMicroExpressionValues(currentValues);
    }
    
    interpolateKeyframes(keyframes, progress) {
        // 找到当前进度对应的关键帧
        let currentFrame = 0;
        for (let i = 0; i < keyframes.length - 1; i++) {
            if (progress >= keyframes[i].time && progress <= keyframes[i + 1].time) {
                currentFrame = i;
                break;
            }
        }
        
        const frame1 = keyframes[currentFrame];
        const frame2 = keyframes[currentFrame + 1] || frame1;
        
        const frameProgress = (progress - frame1.time) / (frame2.time - frame1.time);
        const clampedProgress = Math.max(0, Math.min(1, frameProgress));
        
        // 插值计算
        const result = {};
        Object.keys(frame1.values).forEach(key => {
            const value1 = frame1.values[key];
            const value2 = frame2.values[key] || value1;
            result[key] = value1 + (value2 - value1) * clampedProgress;
        });
        
        return result;
    }
    
    applyMicroExpressionValues(values) {
        // 应用到BlendShape或骨骼系统
        if (window.enterpriseDigitalHuman && window.enterpriseDigitalHuman.morphTargets) {
            const morphTargets = window.enterpriseDigitalHuman.morphTargets;
            
            Object.values(morphTargets).forEach(mesh => {
                if (mesh.morphTargetInfluences && mesh.morphTargetDictionary) {
                    // 映射微表情值到BlendShape
                    this.mapValuesToBlendShapes(mesh, values);
                }
            });
        }
    }
    
    mapValuesToBlendShapes(mesh, values) {
        const mapping = {
            'browUp': ['browInnerUp', 'browOuterUp', 'brow_inner_up'],
            'smile': ['mouthSmile', 'mouth_smile', 'smile'],
            'noseFlare': ['noseSneer', 'nose_sneer', 'nostrilFlare'],
            'lipPress': ['mouthPress', 'mouth_press', 'lipPress']
        };
        
        Object.keys(values).forEach(valueKey => {
            const candidates = mapping[valueKey];
            if (!candidates) return;
            
            for (let candidate of candidates) {
                if (candidate in mesh.morphTargetDictionary) {
                    const index = mesh.morphTargetDictionary[candidate];
                    mesh.morphTargetInfluences[index] = values[valueKey] * this.config.intensity;
                    break;
                }
            }
        });
    }
    
    setEnabled(enabled) {
        this.enabled = enabled;
        console.log(`✨ 微表情系统: ${enabled ? '启用' : '禁用'}`);
    }
    
    setIntensity(intensity) {
        this.config.intensity = Math.max(0, Math.min(1, intensity));
    }
    
    setFrequency(frequency) {
        this.config.frequency = Math.max(0, Math.min(1, frequency));
    }
}

/**
 * 自然手势动画器
 */
class NaturalGestureAnimator {
    constructor(avatar) {
        this.avatar = avatar;
        this.isActive = false;
        
        this.config = {
            armBones: [],
            handBones: [],
            fingerBones: []
        };
        
        this.state = {
            currentGesture: null,
            gestureProgress: 0
        };
        
        this.findGestureBones();
        this.defineGestures();
    }
    
    findGestureBones() {
        if (!this.avatar) return;
        
        this.avatar.traverse((child) => {
            if (child.type === 'Bone') {
                const name = child.name.toLowerCase();
                
                if (name.includes('arm') || name.includes('shoulder')) {
                    this.config.armBones.push(child);
                }
                
                if (name.includes('hand') || name.includes('wrist')) {
                    this.config.handBones.push(child);
                }
                
                if (name.includes('finger') || name.includes('thumb')) {
                    this.config.fingerBones.push(child);
                }
            }
        });
        
        console.log('🤲 找到手势骨骼:', {
            arms: this.config.armBones.length,
            hands: this.config.handBones.length,
            fingers: this.config.fingerBones.length
        });
    }
    
    defineGestures() {
        this.gestures = {
            'greeting_wave': {
                duration: 2000,
                keyframes: [
                    { time: 0, arm: { x: 0, y: 0, z: 0 }, hand: { x: 0, y: 0, z: 0 } },
                    { time: 0.2, arm: { x: 0, y: 1.2, z: 0.5 }, hand: { x: 0, y: 0, z: 0 } },
                    { time: 0.4, arm: { x: 0, y: 1.2, z: 0.5 }, hand: { x: 0, y: 0, z: 0.5 } },
                    { time: 0.6, arm: { x: 0, y: 1.2, z: 0.5 }, hand: { x: 0, y: 0, z: -0.5 } },
                    { time: 0.8, arm: { x: 0, y: 1.2, z: 0.5 }, hand: { x: 0, y: 0, z: 0.5 } },
                    { time: 1.0, arm: { x: 0, y: 0, z: 0 }, hand: { x: 0, y: 0, z: 0 } }
                ]
            },
            'pointing': {
                duration: 1500,
                keyframes: [
                    { time: 0, arm: { x: 0, y: 0, z: 0 }, hand: { x: 0, y: 0, z: 0 } },
                    { time: 0.3, arm: { x: 0, y: 0.8, z: 0.8 }, hand: { x: 0, y: 0, z: 0 } },
                    { time: 0.7, arm: { x: 0, y: 0.8, z: 0.8 }, hand: { x: 0, y: 0, z: 0 } },
                    { time: 1.0, arm: { x: 0, y: 0, z: 0 }, hand: { x: 0, y: 0, z: 0 } }
                ]
            },
            'explaining': {
                duration: 3000,
                keyframes: [
                    { time: 0, arm: { x: 0, y: 0, z: 0 }, hand: { x: 0, y: 0, z: 0 } },
                    { time: 0.2, arm: { x: 0, y: 0.6, z: 0.3 }, hand: { x: 0, y: 0, z: 0 } },
                    { time: 0.5, arm: { x: 0.2, y: 0.8, z: 0.5 }, hand: { x: 0.1, y: 0, z: 0 } },
                    { time: 0.8, arm: { x: -0.2, y: 0.6, z: 0.3 }, hand: { x: -0.1, y: 0, z: 0 } },
                    { time: 1.0, arm: { x: 0, y: 0, z: 0 }, hand: { x: 0, y: 0, z: 0 } }
                ]
            }
        };
    }
    
    start() {
        this.isActive = true;
    }
    
    stop() {
        this.isActive = false;
        this.resetGesturePose();
    }
    
    update(deltaTime) {
        if (!this.isActive || !this.state.currentGesture) return;
        
        this.state.gestureProgress += deltaTime / (this.state.currentGesture.duration / 1000);
        
        if (this.state.gestureProgress >= 1.0) {
            this.endCurrentGesture();
        } else {
            this.updateGestureAnimation();
        }
    }
    
    playGesture(gestureType, intensity = 1.0) {
        const gesture = this.gestures[gestureType];
        if (!gesture) {
            console.warn('❌ 未知手势:', gestureType);
            return;
        }
        
        this.state.currentGesture = {
            ...gesture,
            intensity: intensity
        };
        this.state.gestureProgress = 0;
        
        console.log(`🤲 播放手势: ${gestureType}`);
    }
    
    updateGestureAnimation() {
        const gesture = this.state.currentGesture;
        if (!gesture) return;
        
        // 插值计算当前姿势
        const currentPose = this.interpolateGestureKeyframes(gesture.keyframes, this.state.gestureProgress);
        this.applyGesturePose(currentPose, gesture.intensity);
    }
    
    interpolateGestureKeyframes(keyframes, progress) {
        // 找到当前关键帧
        let frameIndex = 0;
        for (let i = 0; i < keyframes.length - 1; i++) {
            if (progress >= keyframes[i].time && progress <= keyframes[i + 1].time) {
                frameIndex = i;
                break;
            }
        }
        
        const frame1 = keyframes[frameIndex];
        const frame2 = keyframes[frameIndex + 1] || frame1;
        
        const frameProgress = (progress - frame1.time) / (frame2.time - frame1.time);
        const t = Math.max(0, Math.min(1, frameProgress));
        
        // 插值
        return {
            arm: {
                x: frame1.arm.x + (frame2.arm.x - frame1.arm.x) * t,
                y: frame1.arm.y + (frame2.arm.y - frame1.arm.y) * t,
                z: frame1.arm.z + (frame2.arm.z - frame1.arm.z) * t
            },
            hand: {
                x: frame1.hand.x + (frame2.hand.x - frame1.hand.x) * t,
                y: frame1.hand.y + (frame2.hand.y - frame1.hand.y) * t,
                z: frame1.hand.z + (frame2.hand.z - frame1.hand.z) * t
            }
        };
    }
    
    applyGesturePose(pose, intensity) {
        // 应用到手臂骨骼
        this.config.armBones.forEach(bone => {
            if (bone.name.toLowerCase().includes('right')) {
                bone.rotation.x = pose.arm.x * intensity;
                bone.rotation.y = pose.arm.y * intensity;
                bone.rotation.z = pose.arm.z * intensity;
            }
        });
        
        // 应用到手部骨骼
        this.config.handBones.forEach(bone => {
            bone.rotation.x = pose.hand.x * intensity;
            bone.rotation.y = pose.hand.y * intensity;
            bone.rotation.z = pose.hand.z * intensity;
        });
    }
    
    endCurrentGesture() {
        this.state.currentGesture = null;
        this.state.gestureProgress = 0;
        
        // 平滑回到休息姿势
        this.resetGesturePose();
    }
    
    resetGesturePose() {
        this.config.armBones.forEach(bone => {
            bone.rotation.set(0, 0, 0);
        });
        
        this.config.handBones.forEach(bone => {
            bone.rotation.set(0, 0, 0);
        });
    }
}

/**
 * 待机动画系统
 */
class IdleAnimationSystem {
    constructor(avatar) {
        this.avatar = avatar;
        this.isActive = false;
        
        this.config = {
            headSway: {
                intensity: 0.01,
                frequency: 0.1
            },
            shoulderShift: {
                intensity: 0.005,
                frequency: 0.08
            },
            weightShift: {
                intensity: 0.003,
                frequency: 0.05
            }
        };
        
        this.state = {
            time: 0,
            headBone: null,
            shoulderBones: [],
            hipBone: null
        };
        
        this.findIdleBones();
    }
    
    findIdleBones() {
        if (!this.avatar) return;
        
        this.avatar.traverse((child) => {
            if (child.type === 'Bone') {
                const name = child.name.toLowerCase();
                
                if (name.includes('head') || name.includes('neck')) {
                    this.state.headBone = child;
                }
                
                if (name.includes('shoulder') || name.includes('clavicle')) {
                    this.state.shoulderBones.push(child);
                }
                
                if (name.includes('hip') || name.includes('pelvis')) {
                    this.state.hipBone = child;
                }
            }
        });
        
        console.log('😴 找到待机动画骨骼:', {
            head: !!this.state.headBone,
            shoulders: this.state.shoulderBones.length,
            hip: !!this.state.hipBone
        });
    }
    
    start() {
        this.isActive = true;
        this.state.time = 0;
    }
    
    stop() {
        this.isActive = false;
        this.resetIdlePose();
    }
    
    update(deltaTime) {
        if (!this.isActive) return;
        
        this.state.time += deltaTime;
        
        // 头部轻微摆动
        if (this.state.headBone) {
            const headSway = Math.sin(this.state.time * this.config.headSway.frequency) * this.config.headSway.intensity;
            this.state.headBone.rotation.y = headSway;
        }
        
        // 肩膀轻微起伏
        this.state.shoulderBones.forEach((bone, index) => {
            const phase = this.state.time * this.config.shoulderShift.frequency + index * Math.PI;
            const shift = Math.sin(phase) * this.config.shoulderShift.intensity;
            bone.position.y = shift;
        });
        
        // 重心轻微转移
        if (this.state.hipBone) {
            const weightShift = Math.sin(this.state.time * this.config.weightShift.frequency) * this.config.weightShift.intensity;
            this.state.hipBone.rotation.z = weightShift;
        }
    }
    
    resetIdlePose() {
        if (this.state.headBone) {
            this.state.headBone.rotation.y = 0;
        }
        
        this.state.shoulderBones.forEach(bone => {
            bone.position.y = 0;
        });
        
        if (this.state.hipBone) {
            this.state.hipBone.rotation.z = 0;
        }
    }
}

// 全局导出
window.ProceduralAnimationSystem = ProceduralAnimationSystem;
window.BreathingAnimator = BreathingAnimator;
window.EyeTrackingAnimator = EyeTrackingAnimator;
window.MicroExpressionAnimator = MicroExpressionAnimator;
window.NaturalGestureAnimator = NaturalGestureAnimator;
window.IdleAnimationSystem = IdleAnimationSystem;

console.log('🤖 程序化动画系统模块加载完成');