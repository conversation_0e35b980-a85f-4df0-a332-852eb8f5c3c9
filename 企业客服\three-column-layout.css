/* 
 * 三栏布局数字人界面设计
 * 左栏：功能面板，中栏：3D渲染，右栏：AI聊天
 */

/* CSS Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 全局变量 */
:root {
    --primary-bg: #f5f5f5;
    --secondary-bg: #ffffff;
    --accent-bg: #f0f2f5;
    --border-color: #e0e0e0;
    
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-accent: #1976d2;
    
    --left-width: 240px;
    --right-width: 360px;
    --header-height: 60px;
    
    --shadow-light: 0 2px 8px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 16px rgba(0,0,0,0.15);
    --transition: 0.3s ease;
}

/* 主容器 - 三栏布局 */
#digital-human-widget {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--primary-bg);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
}

/* 顶部标题栏 */
.widget-header {
    height: var(--header-height);
    background: var(--secondary-bg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    box-shadow: var(--shadow-light);
    z-index: 100;
}

.widget-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 12px;
}

.toolbar {
    display: flex;
    gap: 8px;
}

.toolbar-btn {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    background: var(--secondary-bg);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    font-size: 18px;
}

.toolbar-btn:hover {
    background: var(--accent-bg);
    border-color: var(--text-accent);
    color: var(--text-accent);
}

/* 主内容区 - 三栏 */
.main-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

/* 左侧功能栏 */
.left-panel {
    width: var(--left-width);
    background: var(--secondary-bg);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

.panel-section {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.section-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.control-btn {
    padding: 10px 16px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--accent-bg);
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition);
    text-align: left;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.control-btn:hover {
    background: var(--text-accent);
    color: white;
    border-color: var(--text-accent);
}

/* 中间3D渲染区 */
.center-panel {
    flex: 1;
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}

#avatar-canvas {
    width: 100%;
    height: 100%;
    display: block;
}

#digital-human-2d-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background: inherit;
}

/* 修复 AI客服助手文字重叠问题 */
.digital-human-info {
    position: absolute;
    top: 20px;
    left: 20px;
    color: white;
    background: rgba(0, 0, 0, 0.8);
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    z-index: 100;
    max-width: 300px;
    backdrop-filter: blur(10px);
}

.digital-human-info div:first-child {
    margin-bottom: 4px;
}

.digital-human-info div:nth-child(2) {
    font-size: 14px;
    opacity: 0.9;
}

.digital-human-info div:last-child {
    font-size: 12px;
    opacity: 0.7;
    margin-top: 8px;
    line-height: 1.4;
}

#digital-human-2d-image {
    max-width: 400px;
    max-height: 500px;
    object-fit: cover;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    background: white;
}

.status-indicator {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(255,255,255,0.9);
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 14px;
    color: var(--text-primary);
    box-shadow: var(--shadow-medium);
}

/* 右侧聊天栏 */
.right-panel {
    width: var(--right-width);
    background: var(--secondary-bg);
    border-left: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
}

.chat-section {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.chat-header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
    background: var(--accent-bg);
}

.chat-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.message {
    max-width: 80%;
    padding: 10px 14px;
    border-radius: 12px;
    font-size: 14px;
    line-height: 1.5;
}

.message.user {
    align-self: flex-end;
    background: var(--text-accent);
    color: white;
}

.message.assistant {
    align-self: flex-start;
    background: var(--accent-bg);
    color: var(--text-primary);
}

.chat-input {
    padding: 16px;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 8px;
}

#message-input {
    flex: 1;
    padding: 10px 14px;
    border: 1px solid var(--border-color);
    border-radius: 24px;
    font-size: 14px;
    outline: none;
    transition: var(--transition);
}

#message-input:focus {
    border-color: var(--text-accent);
}

#send-btn {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    border: none;
    background: var(--text-accent);
    color: white;
    cursor: pointer;
    transition: var(--transition);
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#send-btn:hover {
    transform: scale(1.1);
    background: #1565c0;
}

/* 模型选择面板 */
.model-selection-panel {
    position: absolute;
    top: var(--header-height);
    left: var(--left-width);
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    box-shadow: var(--shadow-medium);
    display: none;
    z-index: 200;
    max-width: 400px;
}

.model-selection-panel.active {
    display: block;
}

.panel-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-primary);
}

#model-options-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.model-option {
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
}

.model-option:hover {
    background: var(--accent-bg);
    border-color: var(--text-accent);
}

.model-option.selected {
    background: var(--text-accent);
    color: white;
    border-color: var(--text-accent);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    :root {
        --left-width: 200px;
        --right-width: 320px;
    }
}

@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }
    
    .left-panel, .right-panel {
        width: 100%;
        height: auto;
    }
    
    .center-panel {
        min-height: 400px;
    }
}