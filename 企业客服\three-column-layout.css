/* 
 * 三栏布局数字人界面设计
 * 左栏：功能面板，中栏：3D渲染，右栏：AI聊天
 */

/* CSS Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    backdrop-filter: blur(10px);
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 全局动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.02);
    }
}

/* 全局变量 - 玻璃拟态风格 */
:root {
    --primary-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-bg: rgba(255, 255, 255, 0.1);
    --accent-bg: rgba(255, 255, 255, 0.05);
    --border-color: rgba(255, 255, 255, 0.2);
    
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.8);
    --text-accent: #64b5f6;
    
    --left-width: 240px;
    --right-width: 360px;
    --header-height: 60px;
    
    --shadow-light: 0 8px 32px rgba(0,0,0,0.1);
    --shadow-medium: 0 16px 64px rgba(0,0,0,0.2);
    --glass-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
    --transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 主容器 - 三栏布局 */
#digital-human-widget {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--primary-bg);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    overflow: hidden;
}

/* 顶部标题栏 */
.widget-header {
    height: var(--header-height);
    background: var(--secondary-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    border-top: none;
    border-left: none;
    border-right: none;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    box-shadow: var(--glass-shadow);
    z-index: 100;
}

.widget-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 12px;
}

.toolbar {
    display: flex;
    gap: 8px;
}

.toolbar-btn {
    width: 36px;
    height: 36px;
    border-radius: 12px;
    border: 1px solid var(--border-color);
    background: var(--accent-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition);
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toolbar-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: var(--text-accent);
    color: var(--text-accent);
    transform: translateY(-2px);
    box-shadow: var(--glass-shadow);
}

/* 主内容区 - 三栏 */
.main-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

/* 左侧功能栏 */
.left-panel {
    width: var(--left-width);
    background: var(--secondary-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    box-shadow: var(--glass-shadow);
}

.panel-section {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    animation: fadeInUp 0.6s ease-out;
}

.section-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.control-btn {
    padding: 12px 16px;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    background: var(--accent-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition);
    text-align: left;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    font-weight: 500;
}

.control-btn:hover {
    background: rgba(100, 181, 246, 0.2);
    color: var(--text-accent);
    border-color: var(--text-accent);
    transform: translateX(4px);
    box-shadow: var(--glass-shadow);
}

/* 中间3D渲染区 */
.center-panel {
    flex: 1;
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}

#avatar-canvas {
    width: 100%;
    height: 100%;
    display: block;
}

#digital-human-2d-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background: inherit;
}

/* 修复 AI客服助手文字重叠问题 */
.digital-human-info {
    position: absolute;
    top: 20px;
    left: 20px;
    color: white;
    background: rgba(0, 0, 0, 0.8);
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    z-index: 100;
    max-width: 300px;
    backdrop-filter: blur(10px);
}

.digital-human-info div:first-child {
    margin-bottom: 4px;
}

.digital-human-info div:nth-child(2) {
    font-size: 14px;
    opacity: 0.9;
}

.digital-human-info div:last-child {
    font-size: 12px;
    opacity: 0.7;
    margin-top: 8px;
    line-height: 1.4;
}

#digital-human-2d-image {
    max-width: 400px;
    max-height: 500px;
    object-fit: cover;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    background: white;
}

.status-indicator {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--border-color);
    padding: 10px 24px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    box-shadow: var(--glass-shadow);
    animation: pulse 2s infinite;
}

/* 右侧聊天栏 */
.right-panel {
    width: var(--right-width);
    background: var(--secondary-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-left: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    box-shadow: var(--glass-shadow);
}

.chat-section {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.chat-header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
    background: var(--accent-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.chat-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.message {
    max-width: 80%;
    padding: 10px 14px;
    border-radius: 12px;
    font-size: 14px;
    line-height: 1.5;
}

.message.user {
    align-self: flex-end;
    background: linear-gradient(135deg, var(--text-accent), #42a5f5);
    color: white;
    box-shadow: var(--glass-shadow);
}

.message.assistant {
    align-self: flex-start;
    background: var(--accent-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.chat-input {
    padding: 16px;
    border-top: 1px solid var(--border-color);
    background: var(--accent-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    display: flex;
    gap: 8px;
}

#message-input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid var(--border-color);
    border-radius: 24px;
    background: var(--accent-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: var(--text-primary);
    font-size: 14px;
    outline: none;
    transition: var(--transition);
}

#message-input:focus {
    border-color: var(--text-accent);
    box-shadow: 0 0 0 3px rgba(100, 181, 246, 0.2);
}

#message-input::placeholder {
    color: var(--text-secondary);
}

#send-btn {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    border: none;
    background: linear-gradient(135deg, var(--text-accent), #42a5f5);
    color: white;
    cursor: pointer;
    transition: var(--transition);
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--glass-shadow);
}

#send-btn:hover {
    transform: scale(1.1) translateY(-2px);
    box-shadow: 0 12px 40px rgba(100, 181, 246, 0.4);
}

/* 模型选择面板 */
.model-selection-panel {
    position: absolute;
    top: var(--header-height);
    left: var(--left-width);
    background: var(--secondary-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    box-shadow: var(--shadow-medium);
    display: none;
    z-index: 200;
    max-width: 400px;
}

.model-selection-panel.active {
    display: block;
}

.panel-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-primary);
}

#model-options-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.model-option {
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
}

.model-option:hover {
    background: var(--accent-bg);
    border-color: var(--text-accent);
}

.model-option.selected {
    background: var(--text-accent);
    color: white;
    border-color: var(--text-accent);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    :root {
        --left-width: 200px;
        --right-width: 320px;
    }
}

@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }
    
    .left-panel, .right-panel {
        width: 100%;
        height: auto;
    }
    
    .center-panel {
        min-height: 400px;
    }
}