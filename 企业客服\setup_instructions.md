# 火山引擎豆包API配置指南

## 1. 后端服务器配置

由于浏览器的CORS限制，您需要设置一个后端服务器来代理API请求。

### 方案一：使用Python Flask服务器（推荐）

1. **安装依赖**
```bash
pip install --upgrade "volcengine-python-sdk[ark]"
pip install flask flask-cors
```

2. **设置环境变量**
```bash
# Windows
set ARK_API_KEY=your_actual_api_key_here

# Linux/Mac
export ARK_API_KEY=your_actual_api_key_here
```

3. **创建并运行服务器**
运行下面创建的 `doubao_server.py` 文件：
```bash
python doubao_server.py
```

### 方案二：使用C语言服务器（已有）

如果您的C语言后端服务器已经在运行，需要：
1. 添加HTTP客户端库（如libcurl）
2. 实现API代理功能
3. 处理CORS headers

## 2. 前端配置

1. **更新API Key**
编辑 `digital-human-config.js` 文件，将 `YOUR_API_KEY_HERE` 替换为实际的API Key。

2. **确认服务器地址**
确保 WebSocket 和 API 代理服务器地址正确：
- WebSocket: `ws://localhost:8080/ws`
- API代理: `http://localhost:5000/api/chat` (Python服务器)

## 3. 测试步骤

1. 启动后端服务器
2. 在浏览器中打开 `index.html`
3. 检查控制台是否有错误
4. 测试AI对话功能

## 常见问题

### CORS错误
- 确保后端服务器正确设置了CORS headers
- 使用代理服务器而不是直接调用火山引擎API

### API Key无效
- 检查环境变量是否正确设置
- 确认API Key在火山引擎控制台是否有效

### WebSocket连接失败
- 确认WebSocket服务器正在运行
- 检查端口是否被占用