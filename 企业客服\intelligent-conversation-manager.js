/**
 * 智能对话管理系统
 * 实现上下文记忆、情感分析、个性化回复、意图识别等高级功能
 */

class IntelligentConversationManager {
    constructor(chatInterface) {
        this.chatInterface = chatInterface;
        this.conversationHistory = [];
        this.userProfile = {};
        this.contextWindow = 10; // 保持最近10轮对话的上下文
        this.sessionId = this.generateSessionId();
        
        // 智能功能配置
        this.config = {
            enableContextMemory: true,
            enableEmotionAnalysis: true,
            enableIntentRecognition: true,
            enablePersonalization: true,
            enableSmartSuggestions: true,
            enableConversationSummary: true,
            maxContextLength: 4000, // 最大上下文长度（字符）
            emotionUpdateInterval: 3 // 每3条消息更新一次情感状态
        };
        
        // 情感分析器
        this.emotionAnalyzer = new EmotionAnalyzer();
        
        // 意图识别器
        this.intentRecognizer = new IntentRecognizer();
        
        // 个性化引擎
        this.personalizationEngine = new PersonalizationEngine();
        
        // 知识库
        this.knowledgeBase = new ConversationKnowledgeBase();
        
        this.init();
    }
    
    /**
     * 初始化智能对话管理器
     */
    init() {
        this.setupConversationTracking();
        this.setupContextManagement();
        this.setupEmotionTracking();
        this.setupIntentRecognition();
        this.setupPersonalization();
        this.enhanceChatInterface();
        
        console.log('✅ 智能对话管理系统初始化完成');
    }
    
    /**
     * 设置对话跟踪
     */
    setupConversationTracking() {
        // 重写消息发送方法
        const originalSendMessage = this.chatInterface.sendMessage;
        
        this.chatInterface.sendMessage = async () => {
            const message = this.chatInterface.messageInput.value.trim();
            if (!message) return;
            
            // 预处理用户消息
            const processedMessage = await this.preprocessUserMessage(message);
            
            // 调用原始发送方法
            await originalSendMessage.call(this.chatInterface);
            
            // 后处理
            this.postprocessUserMessage(processedMessage);
        };
        
        // 监听AI回复
        this.chatInterface.on('messageAdded', (message) => {
            if (message.type === 'ai') {
                this.processAIResponse(message);
            }
        });
    }
    
    /**
     * 预处理用户消息
     */
    async preprocessUserMessage(message) {
        const processedMessage = {
            content: message,
            timestamp: new Date(),
            emotion: null,
            intent: null,
            entities: [],
            context: this.getCurrentContext()
        };
        
        // 情感分析
        if (this.config.enableEmotionAnalysis) {
            processedMessage.emotion = await this.emotionAnalyzer.analyze(message);
        }
        
        // 意图识别
        if (this.config.enableIntentRecognition) {
            processedMessage.intent = await this.intentRecognizer.recognize(message);
        }
        
        // 实体提取
        processedMessage.entities = this.extractEntities(message);
        
        // 添加到对话历史
        this.addToConversationHistory('user', processedMessage);
        
        return processedMessage;
    }
    
    /**
     * 后处理用户消息
     */
    postprocessUserMessage(processedMessage) {
        // 更新用户画像
        this.updateUserProfile(processedMessage);
        
        // 生成智能建议
        if (this.config.enableSmartSuggestions) {
            this.generateSmartSuggestions(processedMessage);
        }
        
        // 更新上下文
        this.updateContext(processedMessage);
    }
    
    /**
     * 处理AI回复
     */
    async processAIResponse(message) {
        const processedResponse = {
            content: message.content,
            timestamp: message.timestamp,
            relevanceScore: this.calculateRelevanceScore(message.content),
            helpfulness: null,
            followUpSuggestions: []
        };
        
        // 生成后续建议
        processedResponse.followUpSuggestions = this.generateFollowUpSuggestions(message.content);
        
        // 添加到对话历史
        this.addToConversationHistory('ai', processedResponse);
        
        // 更新知识库
        this.knowledgeBase.updateFromConversation(this.conversationHistory);
        
        // 显示智能提示
        this.showIntelligentHints(processedResponse);
    }
    
    /**
     * 添加到对话历史
     */
    addToConversationHistory(type, message) {
        this.conversationHistory.push({
            type,
            message,
            sessionId: this.sessionId,
            id: Date.now() + Math.random()
        });
        
        // 限制历史长度
        if (this.conversationHistory.length > this.contextWindow * 2) {
            this.conversationHistory = this.conversationHistory.slice(-this.contextWindow * 2);
        }
    }
    
    /**
     * 获取当前上下文
     */
    getCurrentContext() {
        const recentHistory = this.conversationHistory.slice(-this.contextWindow);
        
        return {
            recentMessages: recentHistory,
            currentTopic: this.extractCurrentTopic(),
            userEmotion: this.getCurrentUserEmotion(),
            conversationStage: this.getConversationStage(),
            keyEntities: this.getKeyEntities()
        };
    }
    
    /**
     * 提取当前话题
     */
    extractCurrentTopic() {
        const recentMessages = this.conversationHistory.slice(-5);
        const topics = [];
        
        recentMessages.forEach(entry => {
            if (entry.message.intent) {
                topics.push(entry.message.intent.topic);
            }
        });
        
        // 返回最频繁的话题
        return this.getMostFrequent(topics) || 'general';
    }
    
    /**
     * 获取当前用户情感
     */
    getCurrentUserEmotion() {
        const recentUserMessages = this.conversationHistory
            .filter(entry => entry.type === 'user')
            .slice(-3);
        
        if (recentUserMessages.length === 0) return 'neutral';
        
        const emotions = recentUserMessages
            .map(entry => entry.message.emotion)
            .filter(emotion => emotion);
        
        return emotions.length > 0 ? emotions[emotions.length - 1] : 'neutral';
    }
    
    /**
     * 获取对话阶段
     */
    getConversationStage() {
        const messageCount = this.conversationHistory.length;
        
        if (messageCount <= 2) return 'greeting';
        if (messageCount <= 6) return 'information_gathering';
        if (messageCount <= 12) return 'problem_solving';
        return 'conclusion';
    }
    
    /**
     * 获取关键实体
     */
    getKeyEntities() {
        const allEntities = [];
        
        this.conversationHistory.forEach(entry => {
            if (entry.message.entities) {
                allEntities.push(...entry.message.entities);
            }
        });
        
        // 统计实体频率
        const entityCounts = {};
        allEntities.forEach(entity => {
            entityCounts[entity.value] = (entityCounts[entity.value] || 0) + 1;
        });
        
        // 返回最频繁的实体
        return Object.entries(entityCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5)
            .map(([entity]) => entity);
    }
    
    /**
     * 提取实体
     */
    extractEntities(text) {
        const entities = [];
        
        // 简单的实体提取（实际项目中可以使用NLP库）
        const patterns = {
            email: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
            phone: /\b\d{3}-?\d{3}-?\d{4}\b/g,
            date: /\b\d{4}-\d{2}-\d{2}\b/g,
            time: /\b\d{1,2}:\d{2}\b/g,
            number: /\b\d+\b/g
        };
        
        Object.entries(patterns).forEach(([type, pattern]) => {
            const matches = text.match(pattern);
            if (matches) {
                matches.forEach(match => {
                    entities.push({
                        type,
                        value: match,
                        start: text.indexOf(match),
                        end: text.indexOf(match) + match.length
                    });
                });
            }
        });
        
        return entities;
    }
    
    /**
     * 更新用户画像
     */
    updateUserProfile(message) {
        if (!this.config.enablePersonalization) return;
        
        // 更新情感倾向
        if (message.emotion) {
            this.userProfile.emotionHistory = this.userProfile.emotionHistory || [];
            this.userProfile.emotionHistory.push({
                emotion: message.emotion,
                timestamp: message.timestamp
            });
            
            // 保持最近20条情感记录
            if (this.userProfile.emotionHistory.length > 20) {
                this.userProfile.emotionHistory = this.userProfile.emotionHistory.slice(-20);
            }
        }
        
        // 更新兴趣话题
        if (message.intent && message.intent.topic) {
            this.userProfile.interests = this.userProfile.interests || {};
            this.userProfile.interests[message.intent.topic] = 
                (this.userProfile.interests[message.intent.topic] || 0) + 1;
        }
        
        // 更新交互偏好
        this.userProfile.interactionStyle = this.analyzeInteractionStyle(message);
        
        // 更新最后活跃时间
        this.userProfile.lastActive = message.timestamp;
    }
    
    /**
     * 分析交互风格
     */
    analyzeInteractionStyle(message) {
        const style = this.userProfile.interactionStyle || {
            formality: 'neutral',
            verbosity: 'medium',
            directness: 'medium'
        };
        
        // 分析正式程度
        const formalWords = ['您', '请', '谢谢', '不好意思'];
        const informalWords = ['你', '咋', '啥', '哈哈'];
        
        const formalCount = formalWords.filter(word => message.content.includes(word)).length;
        const informalCount = informalWords.filter(word => message.content.includes(word)).length;
        
        if (formalCount > informalCount) {
            style.formality = 'formal';
        } else if (informalCount > formalCount) {
            style.formality = 'informal';
        }
        
        // 分析详细程度
        if (message.content.length > 100) {
            style.verbosity = 'high';
        } else if (message.content.length < 20) {
            style.verbosity = 'low';
        }
        
        return style;
    }
    
    /**
     * 生成智能建议
     */
    generateSmartSuggestions(message) {
        const suggestions = [];
        
        // 基于意图的建议
        if (message.intent) {
            suggestions.push(...this.getIntentBasedSuggestions(message.intent));
        }
        
        // 基于情感的建议
        if (message.emotion && message.emotion !== 'neutral') {
            suggestions.push(...this.getEmotionBasedSuggestions(message.emotion));
        }
        
        // 基于上下文的建议
        suggestions.push(...this.getContextBasedSuggestions());
        
        // 更新快捷回复
        if (suggestions.length > 0) {
            this.chatInterface.updateQuickReplies(null, suggestions.slice(0, 5));
        }
    }
    
    /**
     * 获取基于意图的建议
     */
    getIntentBasedSuggestions(intent) {
        const intentSuggestions = {
            'question': ['我需要更多信息', '请详细说明', '有其他解决方案吗？'],
            'complaint': ['我理解您的困扰', '请提供更多细节', '我们会尽快解决'],
            'request': ['好的，我来帮您', '需要什么帮助？', '请告诉我具体需求'],
            'greeting': ['你好', '很高兴为您服务', '有什么可以帮您的？']
        };
        
        return intentSuggestions[intent.category] || [];
    }
    
    /**
     * 获取基于情感的建议
     */
    getEmotionBasedSuggestions(emotion) {
        const emotionSuggestions = {
            'happy': ['太好了！', '很高兴听到这个', '继续保持'],
            'sad': ['我理解您的感受', '让我来帮助您', '会好起来的'],
            'angry': ['我理解您的不满', '让我们解决这个问题', '请给我们机会改进'],
            'confused': ['让我详细解释', '我来帮您理清思路', '有什么不明白的？']
        };
        
        return emotionSuggestions[emotion] || [];
    }
    
    /**
     * 获取基于上下文的建议
     */
    getContextBasedSuggestions() {
        const stage = this.getConversationStage();
        
        const stageSuggestions = {
            'greeting': ['你好', '需要帮助吗？', '欢迎咨询'],
            'information_gathering': ['请提供更多信息', '还有其他问题吗？', '我需要了解更多'],
            'problem_solving': ['让我们试试这个方案', '还有其他选择', '这样可以吗？'],
            'conclusion': ['还有其他问题吗？', '感谢您的咨询', '祝您生活愉快']
        };
        
        return stageSuggestions[stage] || [];
    }
    
    /**
     * 生成后续建议
     */
    generateFollowUpSuggestions(aiResponse) {
        const suggestions = [];
        
        // 基于AI回复内容生成建议
        if (aiResponse.includes('步骤') || aiResponse.includes('方法')) {
            suggestions.push('请演示一下', '有更简单的方法吗？', '我按步骤试试');
        }
        
        if (aiResponse.includes('问题') || aiResponse.includes('疑问')) {
            suggestions.push('没有其他问题了', '还有一个问题', '我明白了');
        }
        
        if (aiResponse.includes('帮助') || aiResponse.includes('协助')) {
            suggestions.push('谢谢您的帮助', '需要更多帮助', '问题解决了');
        }
        
        return suggestions;
    }
    
    /**
     * 显示智能提示
     */
    showIntelligentHints(response) {
        // 如果相关性分数较低，显示提示
        if (response.relevanceScore < 0.7) {
            this.showHint('AI回复可能不够准确，您可以尝试重新描述问题', 'warning');
        }
        
        // 如果有后续建议，显示
        if (response.followUpSuggestions.length > 0) {
            this.showFollowUpSuggestions(response.followUpSuggestions);
        }
    }
    
    /**
     * 显示提示
     */
    showHint(message, type = 'info') {
        const hint = document.createElement('div');
        hint.className = `intelligent-hint ${type}`;
        hint.innerHTML = `
            <div class="hint-icon">${type === 'warning' ? '⚠️' : 'ℹ️'}</div>
            <div class="hint-message">${message}</div>
            <button class="hint-close">×</button>
        `;
        
        hint.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'warning' ? 'var(--warning-color)' : 'var(--primary-color)'};
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 8px;
            max-width: 300px;
        `;
        
        document.body.appendChild(hint);
        
        // 自动消失
        setTimeout(() => {
            hint.remove();
        }, 5000);
        
        // 点击关闭
        hint.querySelector('.hint-close').addEventListener('click', () => {
            hint.remove();
        });
    }
    
    /**
     * 计算相关性分数
     */
    calculateRelevanceScore(response) {
        // 简单的相关性计算（实际项目中可以使用更复杂的算法）
        const context = this.getCurrentContext();
        const userMessage = context.recentMessages
            .filter(entry => entry.type === 'user')
            .slice(-1)[0];
        
        if (!userMessage) return 1.0;
        
        // 计算关键词重叠度
        const userWords = userMessage.message.content.toLowerCase().split(/\s+/);
        const responseWords = response.toLowerCase().split(/\s+/);
        
        const overlap = userWords.filter(word => responseWords.includes(word)).length;
        const relevanceScore = overlap / Math.max(userWords.length, 1);
        
        return Math.min(relevanceScore + 0.5, 1.0); // 基础分数0.5
    }
    
    /**
     * 工具函数
     */
    getMostFrequent(array) {
        if (array.length === 0) return null;
        
        const counts = {};
        array.forEach(item => {
            counts[item] = (counts[item] || 0) + 1;
        });
        
        return Object.entries(counts)
            .sort(([,a], [,b]) => b - a)[0][0];
    }
    
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    /**
     * 增强聊天界面
     */
    enhanceChatInterface() {
        // 添加智能功能指示器
        this.addIntelligentIndicators();
        
        // 添加对话分析面板
        this.addConversationAnalysisPanel();
    }
    
    /**
     * 添加智能指示器
     */
    addIntelligentIndicators() {
        const indicators = document.createElement('div');
        indicators.className = 'intelligent-indicators';
        indicators.innerHTML = `
            <div class="indicator" id="emotion-indicator" title="情感状态">
                😐 <span>中性</span>
            </div>
            <div class="indicator" id="context-indicator" title="上下文">
                🧠 <span>已记忆</span>
            </div>
            <div class="indicator" id="intent-indicator" title="意图识别">
                🎯 <span>识别中</span>
            </div>
        `;
        
        const header = this.chatInterface.container.querySelector('.modern-chat-header');
        header.appendChild(indicators);
    }
    
    /**
     * 添加对话分析面板
     */
    addConversationAnalysisPanel() {
        // 可以添加一个可折叠的分析面板
        // 显示对话统计、情感趋势、话题分析等
    }
}

// 简化的情感分析器
class EmotionAnalyzer {
    async analyze(text) {
        const emotionKeywords = {
            happy: ['开心', '高兴', '满意', '棒', '好的', '谢谢', '太好了'],
            sad: ['难过', '失望', '糟糕', '不好', '沮丧', '伤心'],
            angry: ['生气', '愤怒', '不满', '投诉', '气死了', '烦'],
            confused: ['不懂', '不明白', '什么意思', '怎么', '为什么', '疑惑'],
            excited: ['激动', '兴奋', '期待', '迫不及待', '太棒了']
        };
        
        const textLower = text.toLowerCase();
        
        for (const [emotion, keywords] of Object.entries(emotionKeywords)) {
            if (keywords.some(keyword => textLower.includes(keyword))) {
                return emotion;
            }
        }
        
        return 'neutral';
    }
}

// 简化的意图识别器
class IntentRecognizer {
    async recognize(text) {
        const intentPatterns = {
            question: /[？?]|什么|怎么|为什么|如何|能否|可以吗/,
            request: /请|帮我|我想|我要|能不能|可以帮/,
            complaint: /投诉|不满|问题|故障|错误|不行/,
            greeting: /你好|您好|hi|hello|早上好|下午好/,
            goodbye: /再见|拜拜|谢谢|结束|没事了/
        };
        
        for (const [intent, pattern] of Object.entries(intentPatterns)) {
            if (pattern.test(text)) {
                return {
                    category: intent,
                    confidence: 0.8,
                    topic: this.extractTopic(text)
                };
            }
        }
        
        return {
            category: 'unknown',
            confidence: 0.1,
            topic: 'general'
        };
    }
    
    extractTopic(text) {
        const topics = {
            '技术': /技术|系统|软件|程序|代码|bug/,
            '产品': /产品|功能|使用|操作|设置/,
            '服务': /服务|客服|支持|帮助|咨询/,
            '账户': /账户|登录|密码|注册|个人/,
            '订单': /订单|购买|支付|退款|发货/
        };
        
        for (const [topic, pattern] of Object.entries(topics)) {
            if (pattern.test(text)) {
                return topic;
            }
        }
        
        return 'general';
    }
}

// 简化的个性化引擎
class PersonalizationEngine {
    generatePersonalizedResponse(baseResponse, userProfile) {
        if (!userProfile.interactionStyle) return baseResponse;
        
        const style = userProfile.interactionStyle;
        
        // 根据正式程度调整
        if (style.formality === 'formal') {
            return baseResponse.replace(/你/g, '您');
        } else if (style.formality === 'informal') {
            return baseResponse.replace(/您/g, '你');
        }
        
        return baseResponse;
    }
}

// 简化的知识库
class ConversationKnowledgeBase {
    constructor() {
        this.knowledge = new Map();
    }
    
    updateFromConversation(history) {
        // 从对话中提取知识点
        // 实际项目中可以使用更复杂的知识提取算法
    }
    
    search(query) {
        // 搜索相关知识
        return [];
    }
}

// 导出类
window.IntelligentConversationManager = IntelligentConversationManager;
