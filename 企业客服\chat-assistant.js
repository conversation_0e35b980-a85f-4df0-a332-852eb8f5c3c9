/**
 * 聊天窗口辅助功能模块
 * Linus式设计：实用功能，模块化架构
 * 
 * 功能列表：
 * 1. 划词工具栏 - 文本选择后的快捷操作
 * 2. 翻译功能 - 多语言翻译支持
 * 3. 图片AI技能 - 图片识别和分析
 * 4. 撰写助手 - 语法检查和写作建议
 * 5. 视频处理 - 总结和字幕生成
 * 6. 播客处理 - 音频内容总结
 */

class ChatAssistant {
    constructor() {
        this.isEnabled = true;
        this.selectedText = '';
        this.currentContext = null;
        
        // 功能模块状态
        this.modules = {
            textSelection: true,
            translation: true,
            imageAI: true,
            writingAssist: true,
            videoSummary: true,
            podcastSummary: true
        };
        
        // API服务配置
        this.services = {
            translation: 'https://api.example.com/translate', // 替换为实际翻译API
            imageAI: 'https://api.example.com/image-analyze',  // 替换为实际图像识别API
            grammarCheck: 'https://api.example.com/grammar'    // 替换为实际语法检查API
        };
        
        this.init();
    }
    
    /**
     * 初始化辅助功能
     */
    init() {
        this.createToolbar();
        this.bindTextSelection();
        this.createFileUploader();
        this.enhanceInputBox();
        this.bindKeyboardShortcuts();
        
        console.log('聊天辅助功能初始化完成');
    }
    
    /**
     * 创建划词工具栏
     */
    createToolbar() {
        const toolbar = document.createElement('div');
        toolbar.id = 'selection-toolbar';
        toolbar.className = 'selection-toolbar hidden';
        toolbar.innerHTML = `
            <div class="toolbar-buttons">
                <button onclick="chatAssistant.translateSelection()" title="翻译">🌐</button>
                <button onclick="chatAssistant.explainSelection()" title="解释">💡</button>
                <button onclick="chatAssistant.summarizeSelection()" title="总结">📝</button>
                <button onclick="chatAssistant.rewriteSelection()" title="改写">✏️</button>
                <button onclick="chatAssistant.copySelection()" title="复制">📋</button>
            </div>
        `;
        
        document.body.appendChild(toolbar);
        
        // 添加工具栏样式
        const style = document.createElement('style');
        style.textContent = `
            .selection-toolbar {
                position: absolute;
                background: #333;
                border-radius: 6px;
                padding: 6px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 10000;
                display: flex;
                gap: 2px;
                transition: opacity 0.2s;
            }
            
            .selection-toolbar.hidden {
                display: none;
            }
            
            .toolbar-buttons {
                display: flex;
                gap: 2px;
            }
            
            .toolbar-buttons button {
                background: rgba(255,255,255,0.1);
                border: none;
                color: white;
                padding: 8px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
                transition: background 0.2s;
            }
            
            .toolbar-buttons button:hover {
                background: rgba(255,255,255,0.2);
            }
            
            .file-upload-area {
                border: 2px dashed #ddd;
                border-radius: 8px;
                padding: 20px;
                text-align: center;
                margin: 10px 0;
                background: #fafafa;
                transition: all 0.3s;
            }
            
            .file-upload-area:hover {
                border-color: #007bff;
                background: #f0f8ff;
            }
            
            .file-upload-area.dragover {
                border-color: #007bff;
                background: #e8f4fd;
            }
            
            .assistant-suggestion {
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 4px;
                padding: 8px 12px;
                margin: 4px 0;
                font-size: 12px;
                color: #856404;
            }
            
            .processing-indicator {
                display: inline-flex;
                align-items: center;
                gap: 8px;
                padding: 4px 8px;
                background: #e3f2fd;
                border-radius: 4px;
                font-size: 12px;
                color: #1976d2;
            }
            
            .spinner {
                width: 12px;
                height: 12px;
                border: 2px solid #ccc;
                border-top: 2px solid #007bff;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    }
    
    /**
     * 绑定文本选择事件
     */
    bindTextSelection() {
        document.addEventListener('mouseup', (event) => {
            setTimeout(() => {
                const selection = window.getSelection();
                const selectedText = selection.toString().trim();
                
                if (selectedText && selectedText.length > 2) {
                    this.selectedText = selectedText;
                    this.showToolbar(event.clientX, event.clientY);
                } else {
                    this.hideToolbar();
                }
            }, 10);
        });
        
        // 点击其他地方隐藏工具栏
        document.addEventListener('mousedown', (event) => {
            const toolbar = document.getElementById('selection-toolbar');
            if (toolbar && !toolbar.contains(event.target)) {
                this.hideToolbar();
            }
        });
    }
    
    /**
     * 显示划词工具栏
     */
    showToolbar(x, y) {
        const toolbar = document.getElementById('selection-toolbar');
        if (toolbar) {
            toolbar.classList.remove('hidden');
            
            // 调整位置避免超出屏幕
            const rect = toolbar.getBoundingClientRect();
            const adjustedX = Math.min(x, window.innerWidth - rect.width - 10);
            const adjustedY = Math.max(y - rect.height - 10, 10);
            
            toolbar.style.left = adjustedX + 'px';
            toolbar.style.top = adjustedY + 'px';
        }
    }
    
    /**
     * 隐藏划词工具栏
     */
    hideToolbar() {
        const toolbar = document.getElementById('selection-toolbar');
        if (toolbar) {
            toolbar.classList.add('hidden');
        }
    }
    
    /**
     * 翻译选中文本
     */
    async translateSelection() {
        if (!this.selectedText) return;
        
        this.showProcessingIndicator('翻译中...');
        
        try {
            // 检测语言并翻译
            const result = await this.callTranslationAPI(this.selectedText);
            
            this.addAssistantMessage(`🌐 翻译结果：${result.translatedText}\n原文：${this.selectedText}`);
            
            // 3D数字人表达
            if (window.digitalHuman && digitalHuman.avatar) {
                digitalHuman.setExpression('explaining');
                digitalHuman.speak('我已经为您翻译了选中的文本。');
            }
            
        } catch (error) {
            this.addAssistantMessage('❌ 翻译失败，请稍后重试。');
            console.error('翻译失败:', error);
        } finally {
            this.hideProcessingIndicator();
            this.hideToolbar();
        }
    }
    
    /**
     * 解释选中文本
     */
    async explainSelection() {
        if (!this.selectedText) return;
        
        this.showProcessingIndicator('分析中...');
        
        try {
            // 通过豆包API解释文本
            const explanation = await this.callDoubaoAPI(`请解释以下内容的含义：${this.selectedText}`);
            
            this.addAssistantMessage(`💡 解释：${explanation}`);
            
            if (window.digitalHuman && digitalHuman.avatar) {
                digitalHuman.setExpression('thinking');
                digitalHuman.speak('让我为您解释这段内容的含义。');
            }
            
        } catch (error) {
            this.addAssistantMessage('❌ 解释失败，请稍后重试。');
        } finally {
            this.hideProcessingIndicator();
            this.hideToolbar();
        }
    }
    
    /**
     * 总结选中文本
     */
    async summarizeSelection() {
        if (!this.selectedText) return;
        
        this.showProcessingIndicator('总结中...');
        
        try {
            const summary = await this.callDoubaoAPI(`请总结以下内容的要点：${this.selectedText}`);
            
            this.addAssistantMessage(`📝 总结：${summary}`);
            
            if (window.digitalHuman && digitalHuman.avatar) {
                digitalHuman.setExpression('explaining');
            }
            
        } catch (error) {
            this.addAssistantMessage('❌ 总结失败，请稍后重试。');
        } finally {
            this.hideProcessingIndicator();
            this.hideToolbar();
        }
    }
    
    /**
     * 改写选中文本
     */
    async rewriteSelection() {
        if (!this.selectedText) return;
        
        this.showProcessingIndicator('改写中...');
        
        try {
            const rewritten = await this.callDoubaoAPI(`请改写以下内容，使其更加清晰易懂：${this.selectedText}`);
            
            this.addAssistantMessage(`✏️ 改写建议：${rewritten}`);
            
        } catch (error) {
            this.addAssistantMessage('❌ 改写失败，请稍后重试。');
        } finally {
            this.hideProcessingIndicator();
            this.hideToolbar();
        }
    }
    
    /**
     * 复制选中文本
     */
    copySelection() {
        if (!this.selectedText) return;
        
        navigator.clipboard.writeText(this.selectedText).then(() => {
            this.addAssistantMessage('📋 文本已复制到剪贴板');
        }).catch(() => {
            this.addAssistantMessage('❌ 复制失败');
        });
        
        this.hideToolbar();
    }
    
    /**
     * 创建文件上传区域
     */
    createFileUploader() {
        const chatInterface = document.getElementById('chat-interface');
        if (!chatInterface) return;
        
        const uploaderHtml = `
            <div class="file-upload-area" id="file-upload-area">
                <div>📎 拖拽文件到此处或点击上传</div>
                <div style="font-size: 12px; color: #666; margin-top: 4px;">
                    支持：图片、视频、音频、文档
                </div>
                <input type="file" id="file-input" style="display: none;" 
                       accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt" multiple>
            </div>
        `;
        
        chatInterface.insertAdjacentHTML('afterbegin', uploaderHtml);
        
        this.bindFileUploadEvents();
    }
    
    /**
     * 绑定文件上传事件
     */
    bindFileUploadEvents() {
        const uploadArea = document.getElementById('file-upload-area');
        const fileInput = document.getElementById('file-input');
        
        if (!uploadArea || !fileInput) return;
        
        // 点击上传
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });
        
        // 文件选择
        fileInput.addEventListener('change', (event) => {
            this.handleFileUpload(event.target.files);
        });
        
        // 拖拽上传
        uploadArea.addEventListener('dragover', (event) => {
            event.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (event) => {
            event.preventDefault();
            uploadArea.classList.remove('dragover');
            this.handleFileUpload(event.dataTransfer.files);
        });
    }
    
    /**
     * 处理文件上传
     */
    async handleFileUpload(files) {
        for (const file of files) {
            console.log('处理文件:', file.name, file.type);
            
            if (file.type.startsWith('image/')) {
                await this.handleImageFile(file);
            } else if (file.type.startsWith('video/')) {
                await this.handleVideoFile(file);
            } else if (file.type.startsWith('audio/')) {
                await this.handleAudioFile(file);
            } else {
                await this.handleDocumentFile(file);
            }
        }
    }
    
    /**
     * 处理图片文件
     */
    async handleImageFile(file) {
        this.showProcessingIndicator('分析图片中...');
        
        try {
            const imageUrl = await this.uploadToServer(file);
            const analysis = await this.analyzeImage(imageUrl);
            
            this.addAssistantMessage(`🖼️ 图片分析结果：${analysis.description}\n\n检测到的内容：${analysis.objects.join(', ')}`);
            
            if (window.digitalHuman && digitalHuman.avatar) {
                digitalHuman.setExpression('happy');
                digitalHuman.speak('我已经分析了您上传的图片，为您识别出了其中的内容。');
            }
            
        } catch (error) {
            this.addAssistantMessage('❌ 图片分析失败，请稍后重试。');
            console.error('图片分析失败:', error);
        } finally {
            this.hideProcessingIndicator();
        }
    }
    
    /**
     * 处理视频文件
     */
    async handleVideoFile(file) {
        this.showProcessingIndicator('处理视频中...');
        
        try {
            const videoUrl = await this.uploadToServer(file);
            const summary = await this.summarizeVideo(videoUrl);
            
            this.addAssistantMessage(`🎬 视频总结：\n${summary.summary}\n\n关键时间点：${summary.keyPoints.join(', ')}`);
            
        } catch (error) {
            this.addAssistantMessage('❌ 视频处理失败，请稍后重试。');
        } finally {
            this.hideProcessingIndicator();
        }
    }
    
    /**
     * 处理音频文件（播客）
     */
    async handleAudioFile(file) {
        this.showProcessingIndicator('处理音频中...');
        
        try {
            const audioUrl = await this.uploadToServer(file);
            const summary = await this.summarizePodcast(audioUrl);
            
            this.addAssistantMessage(`🎧 播客总结：\n${summary.summary}\n\n主要话题：${summary.topics.join(', ')}`);
            
        } catch (error) {
            this.addAssistantMessage('❌ 音频处理失败，请稍后重试。');
        } finally {
            this.hideProcessingIndicator();
        }
    }
    
    /**
     * 增强输入框功能
     */
    enhanceInputBox() {
        const inputBox = document.getElementById('message-input');
        if (!inputBox) return;
        
        // 添加写作助手功能
        let typingTimer;
        inputBox.addEventListener('input', () => {
            clearTimeout(typingTimer);
            typingTimer = setTimeout(() => {
                this.checkGrammar(inputBox.value);
            }, 1000); // 1秒后检查
        });
        
        // 添加智能建议
        inputBox.addEventListener('focus', () => {
            this.showWritingSuggestions();
        });
    }
    
    /**
     * 语法检查
     */
    async checkGrammar(text) {
        if (text.length < 10) return; // 太短不检查
        
        try {
            const suggestions = await this.callGrammarAPI(text);
            if (suggestions && suggestions.length > 0) {
                this.showGrammarSuggestions(suggestions);
            }
        } catch (error) {
            console.error('语法检查失败:', error);
        }
    }
    
    /**
     * 显示语法建议
     */
    showGrammarSuggestions(suggestions) {
        let existingSuggestion = document.getElementById('grammar-suggestion');
        if (existingSuggestion) {
            existingSuggestion.remove();
        }
        
        const suggestion = document.createElement('div');
        suggestion.id = 'grammar-suggestion';
        suggestion.className = 'assistant-suggestion';
        suggestion.innerHTML = `
            <strong>✍️ 写作建议：</strong><br>
            ${suggestions.slice(0, 2).map(s => `• ${s.message}`).join('<br>')}
        `;
        
        const inputArea = document.getElementById('input-area');
        if (inputArea) {
            inputArea.appendChild(suggestion);
            
            // 5秒后自动隐藏
            setTimeout(() => {
                suggestion.remove();
            }, 5000);
        }
    }
    
    /**
     * 显示处理指示器
     */
    showProcessingIndicator(message) {
        let indicator = document.getElementById('processing-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'processing-indicator';
            indicator.className = 'processing-indicator';
            
            const messagesEl = document.getElementById('messages');
            if (messagesEl) {
                messagesEl.appendChild(indicator);
            }
        }
        
        indicator.innerHTML = `<div class="spinner"></div> ${message}`;
        
        // 滚动到底部
        const messagesEl = document.getElementById('messages');
        if (messagesEl) {
            messagesEl.scrollTop = messagesEl.scrollHeight;
        }
    }
    
    /**
     * 隐藏处理指示器
     */
    hideProcessingIndicator() {
        const indicator = document.getElementById('processing-indicator');
        if (indicator) {
            indicator.remove();
        }
    }
    
    /**
     * 添加助手消息到聊天窗口
     */
    addAssistantMessage(message) {
        const messagesEl = document.getElementById('messages');
        if (messagesEl) {
            const messageEl = document.createElement('div');
            messageEl.className = 'message ai';
            messageEl.innerHTML = message.replace(/\n/g, '<br>');
            messagesEl.appendChild(messageEl);
            messagesEl.scrollTop = messagesEl.scrollHeight;
        }
    }
    
    /**
     * 绑定键盘快捷键
     */
    bindKeyboardShortcuts() {
        document.addEventListener('keydown', (event) => {
            // Ctrl+T 翻译
            if (event.ctrlKey && event.key === 't') {
                event.preventDefault();
                this.translateSelection();
            }
            
            // Ctrl+E 解释
            if (event.ctrlKey && event.key === 'e') {
                event.preventDefault();
                this.explainSelection();
            }
            
            // Ctrl+S 总结
            if (event.ctrlKey && event.key === 's') {
                event.preventDefault();
                this.summarizeSelection();
            }
        });
    }
    
    // API调用方法（需要根据实际服务实现）
    async callTranslationAPI(text) {
        // 模拟翻译API调用
        return new Promise(resolve => {
            setTimeout(() => {
                resolve({
                    translatedText: `[翻译结果] ${text}`,
                    sourceLanguage: 'auto',
                    targetLanguage: 'zh'
                });
            }, 1000);
        });
    }
    
    async callDoubaoAPI(prompt) {
        // 使用现有的WebSocket连接
        if (window.digitalHuman && digitalHuman.wsClient) {
            return new Promise((resolve) => {
                const originalCallback = digitalHuman.wsClient.callbacks.onMessage;
                
                digitalHuman.wsClient.callbacks.onMessage = (message) => {
                    if (message.type === 'ai_chat_response') {
                        resolve(message.data.response);
                        digitalHuman.wsClient.callbacks.onMessage = originalCallback;
                    }
                };
                
                digitalHuman.wsClient.sendAIChatRequest(prompt);
            });
        }
        
        // 备用方案
        return `基于"${prompt}"的AI分析结果`;
    }
    
    async callGrammarAPI(text) {
        // 模拟语法检查API
        return new Promise(resolve => {
            setTimeout(() => {
                resolve([
                    { message: '建议使用更正式的表达方式' },
                    { message: '注意标点符号的使用' }
                ]);
            }, 500);
        });
    }
    
    async uploadToServer(file) {
        // 模拟文件上传
        return new Promise(resolve => {
            setTimeout(() => {
                resolve(`https://example.com/uploads/${file.name}`);
            }, 1000);
        });
    }
    
    async analyzeImage(imageUrl) {
        // 模拟图片分析
        return new Promise(resolve => {
            setTimeout(() => {
                resolve({
                    description: '这是一张包含文字和图形的图片',
                    objects: ['文字', '按钮', '界面元素']
                });
            }, 2000);
        });
    }
    
    async summarizeVideo(videoUrl) {
        // 模拟视频总结
        return new Promise(resolve => {
            setTimeout(() => {
                resolve({
                    summary: '这是一个关于技术讲解的视频，主要内容包括...',
                    keyPoints: ['0:30 - 核心概念', '2:15 - 实例演示', '4:00 - 总结']
                });
            }, 3000);
        });
    }
    
    async summarizePodcast(audioUrl) {
        // 模拟播客总结
        return new Promise(resolve => {
            setTimeout(() => {
                resolve({
                    summary: '这是一期关于技术发展的播客，讨论了...',
                    topics: ['人工智能', '机器学习', '未来趋势']
                });
            }, 3000);
        });
    }
}

// 创建全局实例
const chatAssistant = new ChatAssistant();