/**
 * 🏢 Professional GLB Model Loader
 * 专业级GLB模型加载器 - 优先支持GLB格式
 * 
 * Features:
 * - GLB 2.0格式优先支持
 * - 详细的模型信息解析
 * - 高级错误处理和回退机制
 * - 性能监控和优化
 */

class ProfessionalGLBLoader {
    constructor() {
        this.loader = null;
        this.currentModel = null;
        this.modelCache = new Map();
        this.loadingStates = new Map();
        
        // GLB模型配置 - 优先级排序
        this.models = {
            primary: {
                path: 'look1-nayong-glb-test/source/look1.glb',
                name: 'Nayong Look1 (Primary)',
                format: 'GLB 2.0',
                joints: 92,
                materials: 79,
                textures: 32,
                features: ['PBR材质', '面部表情', '骨骼动画', 'Morph Targets'],
                priority: 1,
                size: '~35MB'
            },
            secondary_a: {
                path: 'look1_nayong_glb_test.glb',
                name: 'Nayong GLB Variant A',
                format: 'GLB 2.0',
                features: ['优化版本', '快速加载', '兼容性强'],
                priority: 2,
                size: '~25MB'
            },
            secondary_b: {
                path: 'look1_nayong_glb_test (1).glb',
                name: 'Nayong GLB Variant B',
                format: 'GLB 2.0',
                features: ['测试版本', '实验特性', '开发用途'],
                priority: 3,
                size: '~25MB'
            }
        };
        
        this.initLoader();
    }
    
    /**
     * 初始化GLB加载器
     */
    initLoader() {
        if (!window.THREE || !window.GLTFLoader) {
            console.error('❌ Three.js或GLTFLoader未加载');
            return;
        }
        
        this.loader = new THREE.GLTFLoader();
        
        // 配置加载器
        this.loader.setDRACOLoader(null); // 暂不使用Draco压缩
        
        console.log('🎯 Professional GLB Loader initialized');
    }
    
    /**
     * 加载GLB模型 - 专业级实现
     */
    async loadGLBModel(modelKey = 'primary', options = {}) {
        const startTime = performance.now();
        
        try {
            // 获取模型配置
            const modelConfig = this.models[modelKey] || this.models.primary;
            
            // 更新状态
            this.updateLoadingStatus(`正在加载 ${modelConfig.name}...`);
            
            // 检查缓存
            if (this.modelCache.has(modelKey) && !options.forceReload) {
                console.log(`📦 从缓存加载模型: ${modelConfig.name}`);
                return this.useModel(this.modelCache.get(modelKey), modelConfig);
            }
            
            // 记录加载状态
            this.loadingStates.set(modelKey, {
                status: 'loading',
                startTime,
                config: modelConfig
            });
            
            // 加载GLB文件
            const gltf = await this.loadGLTFFile(modelConfig.path);
            
            // 验证GLB格式
            this.validateGLBModel(gltf, modelConfig);
            
            // 处理模型数据
            const processedModel = this.processGLBModel(gltf, modelConfig);
            
            // 缓存模型
            this.modelCache.set(modelKey, processedModel);
            
            // 使用模型
            const result = this.useModel(processedModel, modelConfig);
            
            // 记录成功加载
            const loadTime = performance.now() - startTime;
            console.log(`✅ GLB模型加载完成: ${modelConfig.name} (${loadTime.toFixed(2)}ms)`);
            
            this.updateLoadingStatus(`${modelConfig.name} 加载完成`);
            
            return result;
            
        } catch (error) {
            console.error(`❌ GLB模型加载失败:`, error);
            this.handleLoadingError(error, modelKey);
            throw error;
        }
    }
    
    /**
     * 加载GLTF文件
     */
    loadGLTFFile(path) {
        return new Promise((resolve, reject) => {
            this.loader.load(
                path,
                (gltf) => resolve(gltf),
                (progress) => {
                    const percent = (progress.loaded / progress.total * 100).toFixed(1);
                    this.updateLoadingStatus(`正在加载... ${percent}%`);
                },
                (error) => reject(error)
            );
        });
    }
    
    /**
     * 验证GLB模型
     */
    validateGLBModel(gltf, config) {
        const asset = gltf.asset;
        
        // 检查GLB版本
        if (!asset || asset.version !== '2.0') {
            throw new Error(`不支持的GLB版本: ${asset?.version || 'unknown'}`);
        }
        
        // 检查必要组件
        if (!gltf.scene) {
            throw new Error('GLB文件缺少场景数据');
        }
        
        console.log(`🔍 GLB模型验证通过:`, {
            version: asset.version,
            generator: asset.generator,
            scenes: gltf.scenes?.length || 0,
            nodes: gltf.nodes?.length || 0,
            meshes: gltf.meshes?.length || 0,
            materials: gltf.materials?.length || 0,
            textures: gltf.textures?.length || 0,
            animations: gltf.animations?.length || 0
        });
    }
    
    /**
     * 处理GLB模型数据
     */
    processGLBModel(gltf, config) {
        const scene = gltf.scene;
        const animations = gltf.animations || [];
        const mixer = animations.length > 0 ? new THREE.AnimationMixer(scene) : null;
        
        // 设置模型属性
        scene.name = config.name;
        scene.userData.config = config;
        scene.userData.format = 'GLB';
        scene.userData.loadTime = Date.now();
        
        // 优化模型
        this.optimizeGLBModel(scene);
        
        // 处理动画
        const actions = [];
        if (mixer) {
            animations.forEach((clip, index) => {
                const action = mixer.clipAction(clip);
                actions.push(action);
                console.log(`🎬 动画片段 ${index}: ${clip.name || 'Unnamed'} (${clip.duration.toFixed(2)}s)`);
            });
        }
        
        return {
            scene,
            gltf,
            mixer,
            actions,
            config,
            metadata: this.extractModelMetadata(gltf)
        };
    }
    
    /**
     * 优化GLB模型
     */
    optimizeGLBModel(scene) {
        scene.traverse((child) => {
            if (child.isMesh) {
                // 启用阴影
                child.castShadow = true;
                child.receiveShadow = true;
                
                // 优化材质
                if (child.material) {
                    if (Array.isArray(child.material)) {
                        child.material.forEach(mat => this.optimizeMaterial(mat));
                    } else {
                        this.optimizeMaterial(child.material);
                    }
                }
            }
        });
    }
    
    /**
     * 优化材质
     */
    optimizeMaterial(material) {
        if (material.isMeshStandardMaterial || material.isMeshPhysicalMaterial) {
            // 启用环境贴图
            if (window.digitalHuman?.environmentMap) {
                material.envMap = window.digitalHuman.environmentMap;
            }
            
            // 设置材质属性
            material.needsUpdate = true;
        }
    }
    
    /**
     * 提取模型元数据
     */
    extractModelMetadata(gltf) {
        return {
            format: 'GLB 2.0',
            generator: gltf.asset?.generator || 'Unknown',
            version: gltf.asset?.version || '2.0',
            copyright: gltf.asset?.copyright || '',
            nodeCount: gltf.nodes?.length || 0,
            meshCount: gltf.meshes?.length || 0,
            materialCount: gltf.materials?.length || 0,
            textureCount: gltf.textures?.length || 0,
            animationCount: gltf.animations?.length || 0,
            primitiveCount: this.countPrimitives(gltf),
            vertexCount: this.countVertices(gltf),
            hasBlendShapes: this.hasBlendShapes(gltf),
            hasSkeleton: this.hasSkeleton(gltf)
        };
    }
    
    /**
     * 统计图元数量
     */
    countPrimitives(gltf) {
        let count = 0;
        if (gltf.meshes) {
            gltf.meshes.forEach(mesh => {
                count += mesh.primitives?.length || 0;
            });
        }
        return count;
    }
    
    /**
     * 统计顶点数量
     */
    countVertices(gltf) {
        let count = 0;
        if (gltf.meshes) {
            gltf.meshes.forEach(mesh => {
                mesh.primitives?.forEach(primitive => {
                    const positionAccessor = gltf.accessors?.[primitive.attributes?.POSITION];
                    if (positionAccessor) {
                        count += positionAccessor.count;
                    }
                });
            });
        }
        return count;
    }
    
    /**
     * 检查是否有BlendShapes
     */
    hasBlendShapes(gltf) {
        if (!gltf.meshes) return false;
        return gltf.meshes.some(mesh => 
            mesh.primitives?.some(primitive => 
                primitive.targets && primitive.targets.length > 0
            )
        );
    }
    
    /**
     * 检查是否有骨骼
     */
    hasSkeleton(gltf) {
        return gltf.skins && gltf.skins.length > 0;
    }
    
    /**
     * 使用加载的模型
     */
    useModel(processedModel, config) {
        this.currentModel = processedModel;
        
        // 通知数字人系统
        if (window.digitalHuman) {
            window.digitalHuman.setModel(processedModel);
        }
        
        // 触发事件
        window.dispatchEvent(new CustomEvent('glb-model-loaded', {
            detail: {
                model: processedModel,
                config,
                metadata: processedModel.metadata
            }
        }));
        
        return processedModel;
    }
    
    /**
     * 处理加载错误
     */
    handleLoadingError(error, modelKey) {
        this.updateLoadingStatus('模型加载失败');
        
        // 尝试加载备用模型
        if (modelKey === 'primary') {
            console.log('🔄 尝试加载备用模型...');
            setTimeout(() => {
                this.loadGLBModel('secondary_a').catch(() => {
                    this.loadGLBModel('secondary_b').catch(() => {
                        console.error('❌ 所有GLB模型加载失败，切换到2D模式');
                        this.fallbackTo2D();
                    });
                });
            }, 1000);
        }
    }
    
    /**
     * 回退到2D模式
     */
    fallbackTo2D() {
        if (window.digitalHuman?.switchTo2DMode) {
            window.digitalHuman.switchTo2DMode();
        }
    }
    
    /**
     * 更新加载状态
     */
    updateLoadingStatus(message) {
        const statusElement = document.getElementById('status');
        if (statusElement) {
            statusElement.textContent = message;
        }
        console.log(`📊 ${message}`);
    }
    
    /**
     * 获取模型信息
     */
    getModelInfo(modelKey = 'primary') {
        if (this.modelCache.has(modelKey)) {
            return this.modelCache.get(modelKey).metadata;
        }
        return this.models[modelKey] || null;
    }
    
    /**
     * 清理资源
     */
    dispose() {
        this.modelCache.forEach(model => {
            if (model.scene) {
                model.scene.traverse((child) => {
                    if (child.geometry) child.geometry.dispose();
                    if (child.material) {
                        if (Array.isArray(child.material)) {
                            child.material.forEach(mat => mat.dispose());
                        } else {
                            child.material.dispose();
                        }
                    }
                });
            }
            if (model.mixer) {
                model.mixer.stopAllAction();
            }
        });
        this.modelCache.clear();
    }
}

// 全局实例
window.professionalGLBLoader = new ProfessionalGLBLoader();

// 扩展数字人系统
if (window.digitalHuman) {
    window.digitalHuman.loadGLBModel = (path) => {
        // 根据路径确定模型类型
        let modelKey = 'primary';
        if (path.includes('look1_nayong_glb_test.glb')) modelKey = 'secondary_a';
        if (path.includes('look1_nayong_glb_test (1).glb')) modelKey = 'secondary_b';
        
        return window.professionalGLBLoader.loadGLBModel(modelKey);
    };
}

console.log('🏢 Professional GLB Loader ready');