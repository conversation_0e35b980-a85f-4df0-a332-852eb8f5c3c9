/**
 * 高级功能集成
 * 包括代码高亮、数学公式渲染、文件预览、图表生成等
 */

class AdvancedFeatures {
    constructor(chatInterface) {
        this.chatInterface = chatInterface;
        this.mathRenderer = null;
        this.codeHighlighter = null;
        this.chartGenerator = null;
        this.filePreviewers = new Map();
        
        // 功能配置
        this.config = {
            enableCodeHighlight: true,
            enableMathRendering: true,
            enableFilePreview: true,
            enableChartGeneration: true,
            enableTableFormatting: true,
            enableMarkdownExtended: true,
            codeTheme: 'github',
            mathEngine: 'katex'
        };
        
        this.init();
    }
    
    /**
     * 初始化高级功能
     */
    async init() {
        await this.loadExternalLibraries();
        this.setupCodeHighlighting();
        this.setupMathRendering();
        this.setupFilePreview();
        this.setupChartGeneration();
        this.setupTableFormatting();
        this.enhanceMessageRendering();
        
        console.log('✅ 高级功能集成完成');
    }
    
    /**
     * 加载外部库
     */
    async loadExternalLibraries() {
        const libraries = [
            {
                name: 'highlight.js',
                css: 'https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github.min.css',
                js: 'https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js'
            },
            {
                name: 'katex',
                css: 'https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css',
                js: 'https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js'
            },
            {
                name: 'chart.js',
                js: 'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js'
            },
            {
                name: 'mermaid',
                js: 'https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js'
            }
        ];
        
        for (const lib of libraries) {
            await this.loadLibrary(lib);
        }
    }
    
    /**
     * 加载单个库
     */
    async loadLibrary(lib) {
        try {
            // 加载CSS
            if (lib.css) {
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = lib.css;
                document.head.appendChild(link);
            }
            
            // 加载JS
            if (lib.js) {
                await new Promise((resolve, reject) => {
                    const script = document.createElement('script');
                    script.src = lib.js;
                    script.onload = resolve;
                    script.onerror = reject;
                    document.head.appendChild(script);
                });
            }
            
            console.log(`✅ ${lib.name} 加载成功`);
        } catch (error) {
            console.warn(`❌ ${lib.name} 加载失败:`, error);
        }
    }
    
    /**
     * 设置代码高亮
     */
    setupCodeHighlighting() {
        if (!this.config.enableCodeHighlight) return;
        
        this.codeHighlighter = {
            highlight: (code, language) => {
                if (window.hljs) {
                    if (language && hljs.getLanguage(language)) {
                        return hljs.highlight(code, { language }).value;
                    } else {
                        return hljs.highlightAuto(code).value;
                    }
                }
                return this.escapeHtml(code);
            },
            
            detectLanguage: (code) => {
                if (window.hljs) {
                    const result = hljs.highlightAuto(code);
                    return result.language || 'text';
                }
                return 'text';
            }
        };
    }
    
    /**
     * 设置数学公式渲染
     */
    setupMathRendering() {
        if (!this.config.enableMathRendering) return;
        
        this.mathRenderer = {
            renderInline: (math) => {
                if (window.katex) {
                    try {
                        return katex.renderToString(math, {
                            displayMode: false,
                            throwOnError: false
                        });
                    } catch (error) {
                        console.warn('数学公式渲染失败:', error);
                        return `<code>${math}</code>`;
                    }
                }
                return `<code>${math}</code>`;
            },
            
            renderBlock: (math) => {
                if (window.katex) {
                    try {
                        return katex.renderToString(math, {
                            displayMode: true,
                            throwOnError: false
                        });
                    } catch (error) {
                        console.warn('数学公式渲染失败:', error);
                        return `<pre><code>${math}</code></pre>`;
                    }
                }
                return `<pre><code>${math}</code></pre>`;
            }
        };
    }
    
    /**
     * 设置文件预览
     */
    setupFilePreview() {
        if (!this.config.enableFilePreview) return;
        
        // PDF预览器
        this.filePreviewers.set('pdf', {
            canPreview: (file) => file.type === 'application/pdf',
            preview: (file) => this.createPDFPreview(file)
        });
        
        // 图片预览器
        this.filePreviewers.set('image', {
            canPreview: (file) => file.type.startsWith('image/'),
            preview: (file) => this.createImagePreview(file)
        });
        
        // 文本预览器
        this.filePreviewers.set('text', {
            canPreview: (file) => file.type.startsWith('text/'),
            preview: (file) => this.createTextPreview(file)
        });
        
        // JSON预览器
        this.filePreviewers.set('json', {
            canPreview: (file) => file.type === 'application/json' || file.name.endsWith('.json'),
            preview: (file) => this.createJSONPreview(file)
        });
    }
    
    /**
     * 设置图表生成
     */
    setupChartGeneration() {
        if (!this.config.enableChartGeneration) return;
        
        this.chartGenerator = {
            createChart: (data, type = 'line') => {
                const canvas = document.createElement('canvas');
                canvas.width = 400;
                canvas.height = 300;
                
                if (window.Chart) {
                    new Chart(canvas, {
                        type: type,
                        data: data,
                        options: {
                            responsive: true,
                            plugins: {
                                legend: {
                                    position: 'top',
                                },
                                title: {
                                    display: true,
                                    text: data.title || '图表'
                                }
                            }
                        }
                    });
                }
                
                return canvas;
            },
            
            parseDataFromText: (text) => {
                // 简单的数据解析
                const lines = text.split('\n').filter(line => line.trim());
                const labels = [];
                const values = [];
                
                lines.forEach(line => {
                    const parts = line.split(/[,\t]/).map(p => p.trim());
                    if (parts.length >= 2) {
                        labels.push(parts[0]);
                        values.push(parseFloat(parts[1]) || 0);
                    }
                });
                
                return {
                    labels: labels,
                    datasets: [{
                        label: '数据',
                        data: values,
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }]
                };
            }
        };
    }
    
    /**
     * 设置表格格式化
     */
    setupTableFormatting() {
        if (!this.config.enableTableFormatting) return;
        
        this.tableFormatter = {
            parseTable: (text) => {
                const lines = text.split('\n').filter(line => line.trim());
                const rows = [];
                
                lines.forEach(line => {
                    if (line.includes('|')) {
                        const cells = line.split('|').map(cell => cell.trim()).filter(cell => cell);
                        if (cells.length > 0) {
                            rows.push(cells);
                        }
                    }
                });
                
                return rows;
            },
            
            renderTable: (rows) => {
                if (rows.length === 0) return '';
                
                let html = '<table class="formatted-table">';
                
                // 表头
                if (rows.length > 0) {
                    html += '<thead><tr>';
                    rows[0].forEach(cell => {
                        html += `<th>${this.escapeHtml(cell)}</th>`;
                    });
                    html += '</tr></thead>';
                }
                
                // 表体
                if (rows.length > 1) {
                    html += '<tbody>';
                    for (let i = 1; i < rows.length; i++) {
                        html += '<tr>';
                        rows[i].forEach(cell => {
                            html += `<td>${this.escapeHtml(cell)}</td>`;
                        });
                        html += '</tr>';
                    }
                    html += '</tbody>';
                }
                
                html += '</table>';
                return html;
            }
        };
    }
    
    /**
     * 增强消息渲染
     */
    enhanceMessageRendering() {
        // 重写消息格式化方法
        const originalFormatMessageContent = this.chatInterface.formatMessageContent;
        
        this.chatInterface.formatMessageContent = (content) => {
            // 首先应用原始格式化
            let formatted = originalFormatMessageContent.call(this.chatInterface, content);
            
            // 应用高级功能
            formatted = this.processCodeBlocks(formatted);
            formatted = this.processMathFormulas(formatted);
            formatted = this.processCharts(formatted);
            formatted = this.processTables(formatted);
            formatted = this.processMermaidDiagrams(formatted);
            
            return formatted;
        };
    }
    
    /**
     * 处理代码块
     */
    processCodeBlocks(content) {
        if (!this.config.enableCodeHighlight || !this.codeHighlighter) return content;
        
        // 处理代码块 ```language\ncode\n```
        content = content.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, language, code) => {
            const highlightedCode = this.codeHighlighter.highlight(code.trim(), language);
            const detectedLanguage = language || this.codeHighlighter.detectLanguage(code.trim());
            
            return `
                <div class="code-block">
                    <div class="code-header">
                        <span class="code-language">${detectedLanguage}</span>
                        <button class="copy-code-btn" onclick="this.copyCode('${this.escapeForAttribute(code.trim())}')">复制</button>
                    </div>
                    <pre><code class="hljs">${highlightedCode}</code></pre>
                </div>
            `;
        });
        
        // 处理行内代码
        content = content.replace(/`([^`]+)`/g, (match, code) => {
            return `<code class="inline-code">${this.escapeHtml(code)}</code>`;
        });
        
        return content;
    }
    
    /**
     * 处理数学公式
     */
    processMathFormulas(content) {
        if (!this.config.enableMathRendering || !this.mathRenderer) return content;
        
        // 处理块级公式 $$formula$$
        content = content.replace(/\$\$([\s\S]*?)\$\$/g, (match, formula) => {
            return `<div class="math-block">${this.mathRenderer.renderBlock(formula.trim())}</div>`;
        });
        
        // 处理行内公式 $formula$
        content = content.replace(/\$([^$]+)\$/g, (match, formula) => {
            return `<span class="math-inline">${this.mathRenderer.renderInline(formula.trim())}</span>`;
        });
        
        return content;
    }
    
    /**
     * 处理图表
     */
    processCharts(content) {
        if (!this.config.enableChartGeneration || !this.chartGenerator) return content;
        
        // 检测图表数据格式
        const chartPattern = /```chart:(\w+)\n([\s\S]*?)```/g;
        
        content = content.replace(chartPattern, (match, type, data) => {
            try {
                const chartData = this.chartGenerator.parseDataFromText(data.trim());
                const canvas = this.chartGenerator.createChart(chartData, type);
                
                const chartContainer = document.createElement('div');
                chartContainer.className = 'chart-container';
                chartContainer.appendChild(canvas);
                
                return chartContainer.outerHTML;
            } catch (error) {
                console.warn('图表生成失败:', error);
                return `<div class="chart-error">图表生成失败: ${error.message}</div>`;
            }
        });
        
        return content;
    }
    
    /**
     * 处理表格
     */
    processTables(content) {
        if (!this.config.enableTableFormatting || !this.tableFormatter) return content;
        
        // 检测Markdown表格
        const tablePattern = /(\|[^\n]+\|\n)+/g;
        
        content = content.replace(tablePattern, (match) => {
            try {
                const rows = this.tableFormatter.parseTable(match);
                return this.tableFormatter.renderTable(rows);
            } catch (error) {
                console.warn('表格解析失败:', error);
                return match;
            }
        });
        
        return content;
    }
    
    /**
     * 处理Mermaid图表
     */
    processMermaidDiagrams(content) {
        // 处理Mermaid图表 ```mermaid\ndiagram\n```
        content = content.replace(/```mermaid\n([\s\S]*?)```/g, (match, diagram) => {
            const id = 'mermaid-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
            
            setTimeout(() => {
                if (window.mermaid) {
                    mermaid.render(id, diagram.trim()).then(({ svg }) => {
                        const element = document.getElementById(id);
                        if (element) {
                            element.innerHTML = svg;
                        }
                    });
                }
            }, 100);
            
            return `<div id="${id}" class="mermaid-diagram">正在渲染图表...</div>`;
        });
        
        return content;
    }
    
    /**
     * 创建PDF预览
     */
    createPDFPreview(file) {
        const url = URL.createObjectURL(file);
        return `
            <div class="file-preview pdf-preview">
                <div class="preview-header">
                    <span class="file-icon">📄</span>
                    <span class="file-name">${file.name}</span>
                    <span class="file-size">${this.formatFileSize(file.size)}</span>
                </div>
                <div class="preview-content">
                    <embed src="${url}" type="application/pdf" width="100%" height="400px">
                    <div class="preview-actions">
                        <a href="${url}" target="_blank" class="preview-btn">在新窗口打开</a>
                        <button class="preview-btn" onclick="this.downloadFile('${url}', '${file.name}')">下载</button>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * 创建图片预览
     */
    createImagePreview(file) {
        const url = URL.createObjectURL(file);
        return `
            <div class="file-preview image-preview">
                <div class="preview-header">
                    <span class="file-icon">🖼️</span>
                    <span class="file-name">${file.name}</span>
                    <span class="file-size">${this.formatFileSize(file.size)}</span>
                </div>
                <div class="preview-content">
                    <img src="${url}" alt="${file.name}" style="max-width: 100%; border-radius: 8px;">
                    <div class="preview-actions">
                        <button class="preview-btn" onclick="this.openImageFullscreen('${url}')">全屏查看</button>
                        <button class="preview-btn" onclick="this.downloadFile('${url}', '${file.name}')">下载</button>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * 创建文本预览
     */
    async createTextPreview(file) {
        const text = await file.text();
        const preview = text.length > 500 ? text.substring(0, 500) + '...' : text;
        
        return `
            <div class="file-preview text-preview">
                <div class="preview-header">
                    <span class="file-icon">📝</span>
                    <span class="file-name">${file.name}</span>
                    <span class="file-size">${this.formatFileSize(file.size)}</span>
                </div>
                <div class="preview-content">
                    <pre class="text-content">${this.escapeHtml(preview)}</pre>
                    <div class="preview-actions">
                        <button class="preview-btn" onclick="this.showFullText('${this.escapeForAttribute(text)}')">查看全文</button>
                        <button class="preview-btn" onclick="this.downloadFile('${URL.createObjectURL(file)}', '${file.name}')">下载</button>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * 创建JSON预览
     */
    async createJSONPreview(file) {
        try {
            const text = await file.text();
            const json = JSON.parse(text);
            const formatted = JSON.stringify(json, null, 2);
            const preview = formatted.length > 1000 ? formatted.substring(0, 1000) + '...' : formatted;
            
            return `
                <div class="file-preview json-preview">
                    <div class="preview-header">
                        <span class="file-icon">📋</span>
                        <span class="file-name">${file.name}</span>
                        <span class="file-size">${this.formatFileSize(file.size)}</span>
                    </div>
                    <div class="preview-content">
                        <pre class="json-content"><code class="language-json">${this.escapeHtml(preview)}</code></pre>
                        <div class="preview-actions">
                            <button class="preview-btn" onclick="this.showFullJSON('${this.escapeForAttribute(formatted)}')">查看完整JSON</button>
                            <button class="preview-btn" onclick="this.downloadFile('${URL.createObjectURL(file)}', '${file.name}')">下载</button>
                        </div>
                    </div>
                </div>
            `;
        } catch (error) {
            return `
                <div class="file-preview json-preview error">
                    <div class="preview-header">
                        <span class="file-icon">❌</span>
                        <span class="file-name">${file.name}</span>
                        <span class="file-size">${this.formatFileSize(file.size)}</span>
                    </div>
                    <div class="preview-content">
                        <div class="error-message">JSON格式错误: ${error.message}</div>
                    </div>
                </div>
            `;
        }
    }
    
    /**
     * 工具函数
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    escapeForAttribute(text) {
        return text.replace(/'/g, '&#39;').replace(/"/g, '&quot;');
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    /**
     * 获取文件预览器
     */
    getFilePreview(file) {
        for (const [name, previewer] of this.filePreviewers) {
            if (previewer.canPreview(file)) {
                return previewer.preview(file);
            }
        }
        return null;
    }
}

// 全局工具函数
window.copyCode = function(code) {
    navigator.clipboard.writeText(code).then(() => {
        // 显示复制成功提示
        const toast = document.createElement('div');
        toast.textContent = '代码已复制到剪贴板';
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--success-color);
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            z-index: 1000;
        `;
        document.body.appendChild(toast);
        setTimeout(() => toast.remove(), 2000);
    });
};

window.downloadFile = function(url, filename) {
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
};

    /**
     * 初始化绘图板
     */
    initDrawingBoard(modal) {
        const canvas = modal.querySelector('.drawing-canvas');
        const ctx = canvas.getContext('2d');
        let isDrawing = false;
        let currentTool = 'pen';
        let currentColor = '#000000';
        let currentSize = 3;

        // 工具事件
        modal.querySelectorAll('.tool-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                modal.querySelectorAll('.tool-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                currentTool = btn.dataset.tool;

                if (currentTool === 'clear') {
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                }
            });
        });

        // 颜色和大小
        const colorPicker = modal.querySelector('.color-picker');
        const sizeSlider = modal.querySelector('.size-slider');

        colorPicker.addEventListener('change', (e) => {
            currentColor = e.target.value;
        });

        sizeSlider.addEventListener('input', (e) => {
            currentSize = e.target.value;
        });

        // 绘图事件
        canvas.addEventListener('mousedown', startDrawing);
        canvas.addEventListener('mousemove', draw);
        canvas.addEventListener('mouseup', stopDrawing);
        canvas.addEventListener('mouseout', stopDrawing);

        function startDrawing(e) {
            isDrawing = true;
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            ctx.beginPath();
            ctx.moveTo(x, y);
        }

        function draw(e) {
            if (!isDrawing) return;

            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            ctx.lineWidth = currentSize;
            ctx.lineCap = 'round';

            if (currentTool === 'pen') {
                ctx.globalCompositeOperation = 'source-over';
                ctx.strokeStyle = currentColor;
            } else if (currentTool === 'eraser') {
                ctx.globalCompositeOperation = 'destination-out';
            }

            ctx.lineTo(x, y);
            ctx.stroke();
            ctx.beginPath();
            ctx.moveTo(x, y);
        }

        function stopDrawing() {
            isDrawing = false;
            ctx.beginPath();
        }

        // 发送绘图
        modal.querySelector('.send-drawing-btn').addEventListener('click', () => {
            canvas.toBlob((blob) => {
                const imageUrl = URL.createObjectURL(blob);

                this.chatInterface.addMessage({
                    type: 'user',
                    content: `<img src="${imageUrl}" style="max-width: 100%; border-radius: 8px;" alt="手绘图">`,
                    timestamp: new Date(),
                    isDrawing: true
                });

                modal.remove();
            });
        });

        // 关闭按钮
        modal.querySelector('.close-btn').addEventListener('click', () => {
            modal.remove();
        });

        // 点击外部关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        // 添加样式
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        `;

        const container = modal.querySelector('.drawing-container');
        container.style.cssText = `
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: var(--shadow-xl);
            max-width: 90vw;
            max-height: 90vh;
        `;

        const header = modal.querySelector('.drawing-header');
        header.style.cssText = `
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid var(--border-color);
        `;

        canvas.style.cssText = `
            border: 1px solid var(--border-color);
            border-radius: 8px;
            cursor: crosshair;
            display: block;
            margin: 16px 0;
        `;

        const tools = modal.querySelector('.drawing-tools');
        tools.style.cssText = `
            display: flex;
            gap: 12px;
            align-items: center;
            padding-top: 12px;
            border-top: 1px solid var(--border-color);
        `;
    }

    /**
     * 处理音频文件
     */
    async processAudioFile(file) {
        this.chatInterface.showTypingIndicator();

        try {
            // 模拟音频转录
            setTimeout(() => {
                this.chatInterface.hideTypingIndicator();
                this.chatInterface.addMessage({
                    type: 'ai',
                    content: `🎵 音频文件已接收：${file.name}\n正在处理音频内容...`,
                    timestamp: new Date()
                });
            }, 2000);
        } catch (error) {
            this.chatInterface.hideTypingIndicator();
            console.error('音频处理失败:', error);
        }
    }

    /**
     * 处理文档文件
     */
    async processDocumentFile(file) {
        this.chatInterface.showTypingIndicator();

        try {
            // 模拟文档分析
            setTimeout(() => {
                this.chatInterface.hideTypingIndicator();
                this.chatInterface.addMessage({
                    type: 'ai',
                    content: `📄 文档已接收：${file.name}\n我可以帮您分析文档内容，请告诉我您想了解什么？`,
                    timestamp: new Date()
                });
            }, 1500);
        } catch (error) {
            this.chatInterface.hideTypingIndicator();
            console.error('文档处理失败:', error);
        }
    }
}

// 全局工具函数
window.copyCode = function(code) {
    navigator.clipboard.writeText(code).then(() => {
        // 显示复制成功提示
        const toast = document.createElement('div');
        toast.textContent = '代码已复制到剪贴板';
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--success-color);
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            z-index: 1000;
        `;
        document.body.appendChild(toast);
        setTimeout(() => toast.remove(), 2000);
    });
};

window.downloadFile = function(url, filename) {
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
};

window.openImageFullscreen = function(url) {
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        cursor: pointer;
    `;

    const img = document.createElement('img');
    img.src = url;
    img.style.cssText = `
        max-width: 90%;
        max-height: 90%;
        object-fit: contain;
    `;

    modal.appendChild(img);
    document.body.appendChild(modal);

    modal.addEventListener('click', () => modal.remove());
};

window.showFullText = function(text) {
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        padding: 20px;
    `;

    const container = document.createElement('div');
    container.style.cssText = `
        background: white;
        border-radius: 12px;
        padding: 20px;
        max-width: 80%;
        max-height: 80%;
        overflow: auto;
    `;

    const pre = document.createElement('pre');
    pre.textContent = text;
    pre.style.cssText = `
        white-space: pre-wrap;
        font-family: var(--font-mono);
        font-size: 14px;
        line-height: 1.5;
    `;

    const closeBtn = document.createElement('button');
    closeBtn.textContent = '关闭';
    closeBtn.style.cssText = `
        position: absolute;
        top: 10px;
        right: 10px;
        padding: 8px 16px;
        background: var(--primary-color);
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    `;

    container.appendChild(pre);
    container.appendChild(closeBtn);
    modal.appendChild(container);
    document.body.appendChild(modal);

    closeBtn.addEventListener('click', () => modal.remove());
    modal.addEventListener('click', (e) => {
        if (e.target === modal) modal.remove();
    });
};

window.showFullJSON = function(json) {
    window.showFullText(json);
};

// 导出类
window.AdvancedFeatures = AdvancedFeatures;
