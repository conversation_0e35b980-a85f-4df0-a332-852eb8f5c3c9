@echo off
cls
echo ========================================
echo 企业数字人客服系统 - 快速启动
echo ========================================
echo.

echo [1] 检查端口占用...
netstat -an | findstr ":5000" >nul
if %errorlevel% equ 0 (
    echo ⚠️ 端口5000已占用，尝试停止...
    taskkill /f /im python.exe /fi "windowtitle eq API代理服务器" >nul 2>&1
)

netstat -an | findstr ":8000" >nul  
if %errorlevel% equ 0 (
    echo ⚠️ 端口8000已占用，尝试停止...
    taskkill /f /im python.exe /fi "windowtitle eq 文件服务器" >nul 2>&1
)

echo.
echo [2] 设置API Key...
if not exist .env (
    set /p api_key=请输入火山引擎API Key: 
    echo ARK_API_KEY=!api_key! > .env
) else (
    echo ✅ 使用已存在的.env配置
)

echo.
echo [3] 启动服务...
echo 正在启动API代理服务器...
start "API代理服务器" /min python doubao_server.py
timeout /t 2 >nul

echo 正在启动文件服务器...
start "文件服务器" /min python -m http.server 8000
timeout /t 2 >nul

echo.
echo [4] 检查服务状态...
timeout /t 3 >nul

echo 测试API代理服务器...
curl -s http://localhost:5000/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ API代理服务器 - 运行正常
) else (
    echo ❌ API代理服务器 - 启动失败
)

echo 测试文件服务器...
curl -s http://localhost:8000/ >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 文件服务器 - 运行正常
) else (
    echo ❌ 文件服务器 - 启动失败
)

echo.
echo ========================================
echo ✅ 启动完成！
echo.
echo 🌐 访问地址: http://localhost:8000/index-three-column.html
echo 📊 API状态: http://localhost:5000/health
echo.
echo 如有问题，请检查:
echo 1. Python是否已安装
echo 2. 依赖包是否已安装 (运行install.bat)
echo 3. 防火墙是否阻止了5000/8000端口
echo ========================================
echo.
echo 3秒后自动打开浏览器...
timeout /t 3 >nul

start http://localhost:8000/index-three-column.html
echo.
echo 按任意键退出...
pause >nul