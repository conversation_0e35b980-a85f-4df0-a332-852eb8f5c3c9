/**
 * 增强版2D动画系统
 * 解决2D图片没有BlendShape或动画的问题
 * Linus式解决方案：用CSS变换模拟3D效果
 */

class Enhanced2DAnimations {
    constructor() {
        this.animations = {
            // 表情动画 - 模拟BlendShape效果
            expressions: {
                neutral: {
                    transform: 'scale(1) rotate(0deg)',
                    filter: 'brightness(1) contrast(1) saturate(1)',
                    description: '中性表情'
                },
                
                happy: {
                    // 微笑：轻微放大+增亮+饱和度提升
                    keyframes: [
                        { transform: 'scale(1)', filter: 'brightness(1) saturate(1)' },
                        { transform: 'scale(1.03)', filter: 'brightness(1.15) saturate(1.2)' },
                        { transform: 'scale(1.01)', filter: 'brightness(1.08) saturate(1.1)' }
                    ],
                    duration: 800,
                    description: '😊 开心表情 - 放大增亮'
                },
                
                thinking: {
                    // 思考：轻微倾斜+透明度变化
                    keyframes: [
                        { transform: 'rotate(0deg)', opacity: '1' },
                        { transform: 'rotate(-1deg)', opacity: '0.9' },
                        { transform: 'rotate(1deg)', opacity: '0.95' },
                        { transform: 'rotate(-0.5deg)', opacity: '0.9' },
                        { transform: 'rotate(0deg)', opacity: '1' }
                    ],
                    duration: 2500,
                    description: '🤔 思考表情 - 摇摆变化'
                },
                
                concern: {
                    // 关切：降低亮度+增加对比度
                    keyframes: [
                        { filter: 'brightness(1) contrast(1)' },
                        { filter: 'brightness(0.85) contrast(1.2)' },
                        { filter: 'brightness(0.9) contrast(1.1)' }
                    ],
                    duration: 1200,
                    description: '😟 关切表情 - 调暗强化'
                },
                
                surprised: {
                    // 惊讶：快速放大+震动
                    keyframes: [
                        { transform: 'scale(1)', filter: 'brightness(1)' },
                        { transform: 'scale(1.05)', filter: 'brightness(1.2)' },
                        { transform: 'scale(0.98)', filter: 'brightness(1.1)' },
                        { transform: 'scale(1.02)', filter: 'brightness(1.05)' },
                        { transform: 'scale(1)', filter: 'brightness(1)' }
                    ],
                    duration: 600,
                    description: '😲 惊讶表情 - 震动放大'
                }
            },
            
            // 手势动画 - 模拟身体动作
            gestures: {
                greeting: {
                    // 打招呼：左右摆动
                    keyframes: [
                        { transform: 'translateX(0px) rotate(0deg)' },
                        { transform: 'translateX(-3px) rotate(2deg)' },
                        { transform: 'translateX(3px) rotate(-2deg)' },
                        { transform: 'translateX(-2px) rotate(1deg)' },
                        { transform: 'translateX(2px) rotate(-1deg)' },
                        { transform: 'translateX(0px) rotate(0deg)' }
                    ],
                    duration: 2000,
                    description: '👋 打招呼 - 摆手动作'
                },
                
                pointing: {
                    // 指向：向前倾斜+移动
                    keyframes: [
                        { transform: 'translateX(0px) translateY(0px) rotate(0deg)' },
                        { transform: 'translateX(5px) translateY(-2px) rotate(3deg)' },
                        { transform: 'translateX(3px) translateY(-1px) rotate(2deg)' },
                        { transform: 'translateX(0px) translateY(0px) rotate(0deg)' }
                    ],
                    duration: 1500,
                    description: '👉 指向 - 前倾指示'
                },
                
                explaining: {
                    // 解释：上下浮动+轻微旋转
                    keyframes: [
                        { transform: 'translateY(0px) rotate(0deg)' },
                        { transform: 'translateY(-2px) rotate(0.5deg)' },
                        { transform: 'translateY(0px) rotate(0deg)' },
                        { transform: 'translateY(-1px) rotate(-0.5deg)' },
                        { transform: 'translateY(0px) rotate(0deg)' }
                    ],
                    duration: 3000,
                    description: '🗣️ 解释 - 浮动讲解'
                },
                
                nodding: {
                    // 点头：上下移动
                    keyframes: [
                        { transform: 'translateY(0px)' },
                        { transform: 'translateY(3px)' },
                        { transform: 'translateY(0px)' },
                        { transform: 'translateY(2px)' },
                        { transform: 'translateY(0px)' }
                    ],
                    duration: 1800,
                    description: '👍 点头 - 确认动作'
                },
                
                shaking: {
                    // 摇头：左右移动
                    keyframes: [
                        { transform: 'translateX(0px)' },
                        { transform: 'translateX(-3px)' },
                        { transform: 'translateX(3px)' },
                        { transform: 'translateX(-2px)' },
                        { transform: 'translateX(0px)' }
                    ],
                    duration: 1500,
                    description: '❌ 摇头 - 否定动作'
                }
            },
            
            // 特殊效果动画
            effects: {
                attention: {
                    // 吸引注意：闪烁+缩放
                    keyframes: [
                        { transform: 'scale(1)', filter: 'brightness(1)' },
                        { transform: 'scale(1.05)', filter: 'brightness(1.3)' },
                        { transform: 'scale(1)', filter: 'brightness(1)' },
                        { transform: 'scale(1.03)', filter: 'brightness(1.2)' },
                        { transform: 'scale(1)', filter: 'brightness(1)' }
                    ],
                    duration: 1000,
                    description: '✨ 吸引注意 - 闪烁效果'
                },
                
                loading: {
                    // 加载中：旋转+透明度
                    keyframes: [
                        { transform: 'rotate(0deg)', opacity: '1' },
                        { transform: 'rotate(90deg)', opacity: '0.7' },
                        { transform: 'rotate(180deg)', opacity: '1' },
                        { transform: 'rotate(270deg)', opacity: '0.7' },
                        { transform: 'rotate(360deg)', opacity: '1' }
                    ],
                    duration: 2000,
                    description: '⏳ 加载中 - 旋转等待'
                },
                
                success: {
                    // 成功：绿色闪烁+放大
                    keyframes: [
                        { transform: 'scale(1)', filter: 'brightness(1) hue-rotate(0deg)' },
                        { transform: 'scale(1.1)', filter: 'brightness(1.5) hue-rotate(120deg)' },
                        { transform: 'scale(1)', filter: 'brightness(1) hue-rotate(0deg)' }
                    ],
                    duration: 800,
                    description: '✅ 成功 - 绿色确认'
                },
                
                error: {
                    // 错误：红色闪烁+震动
                    keyframes: [
                        { transform: 'translateX(0px)', filter: 'brightness(1) hue-rotate(0deg)' },
                        { transform: 'translateX(-2px)', filter: 'brightness(1.3) hue-rotate(0deg)' },
                        { transform: 'translateX(2px)', filter: 'brightness(1.3) hue-rotate(0deg)' },
                        { transform: 'translateX(-1px)', filter: 'brightness(1.1) hue-rotate(0deg)' },
                        { transform: 'translateX(0px)', filter: 'brightness(1) hue-rotate(0deg)' }
                    ],
                    duration: 600,
                    description: '❌ 错误 - 红色警告'
                }
            }
        };
        
        this.currentAnimations = new Set(); // 支持同时播放多个动画
        this.isEnabled = true;
    }
    
    /**
     * 播放表情动画
     */
    playExpression(imageElement, expression, callback) {
        if (!this.isEnabled || !imageElement) return;
        
        const animation = this.animations.expressions[expression];
        if (!animation) {
            console.warn('未知2D表情:', expression);
            return;
        }
        
        console.log('🎭 播放2D表情:', animation.description);
        return this.playAnimation(imageElement, animation, callback);
    }
    
    /**
     * 播放手势动画
     */
    playGesture(imageElement, gesture, callback) {
        if (!this.isEnabled || !imageElement) return;
        
        const animation = this.animations.gestures[gesture];
        if (!animation) {
            console.warn('未知2D手势:', gesture);
            return;
        }
        
        console.log('👋 播放2D手势:', animation.description);
        return this.playAnimation(imageElement, animation, callback);
    }
    
    /**
     * 播放特效动画
     */
    playEffect(imageElement, effect, callback) {
        if (!this.isEnabled || !imageElement) return;
        
        const animation = this.animations.effects[effect];
        if (!animation) {
            console.warn('未知2D特效:', effect);
            return;
        }
        
        console.log('✨ 播放2D特效:', animation.description);
        return this.playAnimation(imageElement, animation, callback);
    }
    
    /**
     * 执行动画
     */
    playAnimation(imageElement, animation, callback) {
        // 如果有keyframes，使用Web Animations API
        if (animation.keyframes) {
            const webAnimation = imageElement.animate(
                animation.keyframes,
                {
                    duration: animation.duration || 1000,
                    easing: 'ease-in-out',
                    fill: 'none' // 动画结束后恢复原状
                }
            );
            
            this.currentAnimations.add(webAnimation);
            
            webAnimation.addEventListener('finish', () => {
                this.currentAnimations.delete(webAnimation);
                this.resetToNeutral(imageElement);
                if (callback) callback();
            });
            
            return webAnimation;
        }
        // 如果是静态样式，直接应用
        else {
            this.applyStaticStyle(imageElement, animation);
            if (callback) {
                setTimeout(callback, animation.duration || 1000);
            }
        }
    }
    
    /**
     * 应用静态样式
     */
    applyStaticStyle(imageElement, animation) {
        if (animation.transform) {
            imageElement.style.transform = animation.transform;
        }
        if (animation.filter) {
            imageElement.style.filter = animation.filter;
        }
    }
    
    /**
     * 恢复中性状态
     */
    resetToNeutral(imageElement) {
        if (!imageElement) return;
        
        // 渐变回到原始状态
        const resetAnimation = imageElement.animate([
            {}, // 当前状态
            { 
                transform: 'scale(1) rotate(0deg) translateX(0px) translateY(0px)',
                filter: 'brightness(1) contrast(1) saturate(1) hue-rotate(0deg)',
                opacity: '1'
            }
        ], {
            duration: 300,
            easing: 'ease-out',
            fill: 'forwards'
        });
        
        resetAnimation.addEventListener('finish', () => {
            // 清除所有样式，恢复CSS原始状态
            imageElement.style.transform = '';
            imageElement.style.filter = '';
            imageElement.style.opacity = '';
        });
    }
    
    /**
     * 停止所有动画
     */
    stopAllAnimations() {
        this.currentAnimations.forEach(animation => {
            animation.cancel();
        });
        this.currentAnimations.clear();
    }
    
    /**
     * 连续播放动画序列
     */
    async playSequence(imageElement, animations) {
        for (const { type, name, delay = 500 } of animations) {
            await new Promise(resolve => {
                switch (type) {
                    case 'expression':
                        this.playExpression(imageElement, name, resolve);
                        break;
                    case 'gesture':
                        this.playGesture(imageElement, name, resolve);
                        break;
                    case 'effect':
                        this.playEffect(imageElement, name, resolve);
                        break;
                    default:
                        resolve();
                }
            });
            
            // 序列间延迟
            if (delay > 0) {
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }
    
    /**
     * 获取所有可用动画
     */
    getAllAnimations() {
        return {
            expressions: Object.keys(this.animations.expressions),
            gestures: Object.keys(this.animations.gestures),
            effects: Object.keys(this.animations.effects)
        };
    }
    
    /**
     * 启用/禁用动画系统
     */
    setEnabled(enabled) {
        this.isEnabled = enabled;
        if (!enabled) {
            this.stopAllAnimations();
        }
        console.log('2D动画系统', enabled ? '已启用' : '已禁用');
    }
}

/**
 * 集成增强版2D动画到现有系统
 */
function integrateEnhanced2DAnimations() {
    console.log('🚀 集成增强版2D动画系统...');
    
    // 创建全局增强动画实例
    window.enhanced2DAnimations = new Enhanced2DAnimations();
    
    // 扩展现有数字人系统
    if (window.digitalHuman) {
        digitalHuman.enhanced2D = window.enhanced2DAnimations;
        
        // 增强setExpression方法
        const originalSetExpression = digitalHuman.setExpression;
        digitalHuman.setExpression = function(expression, intensity = 1.0) {
            // 调用原来的3D表情
            if (originalSetExpression) {
                originalSetExpression.call(this, expression, intensity);
            }
            
            // 如果是2D模式，使用增强版2D动画
            const imageElement = document.getElementById('digital-human-2d-image');
            if (imageElement && imageElement.offsetParent !== null) { // 检查元素是否可见
                window.enhanced2DAnimations.playExpression(imageElement, expression);
            }
        };
        
        // 增强testAnimation方法
        const originalTestAnimation = digitalHuman.testAnimation;
        digitalHuman.testAnimation = function(animation) {
            // 调用原来的3D动画
            if (originalTestAnimation) {
                originalTestAnimation.call(this, animation);
            }
            
            // 如果是2D模式，使用增强版2D动画
            const imageElement = document.getElementById('digital-human-2d-image');
            if (imageElement && imageElement.offsetParent !== null) {
                window.enhanced2DAnimations.playGesture(imageElement, animation);
            }
        };
        
        // 添加新的2D专用方法
        digitalHuman.play2DEffect = function(effect) {
            const imageElement = document.getElementById('digital-human-2d-image');
            if (imageElement) {
                window.enhanced2DAnimations.playEffect(imageElement, effect);
            }
        };
        
        digitalHuman.play2DSequence = function(sequences) {
            const imageElement = document.getElementById('digital-human-2d-image');
            if (imageElement) {
                window.enhanced2DAnimations.playSequence(imageElement, sequences);
            }
        };
        
        console.log('✅ 增强版2D动画已集成到数字人系统');
        console.log('📋 可用动画:', window.enhanced2DAnimations.getAllAnimations());
    }
    
    return window.enhanced2DAnimations;
}

// 在DOM加载完成后自动初始化
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        integrateEnhanced2DAnimations();
        
        // 测试动画功能（可选）
        setTimeout(() => {
            const imageElement = document.getElementById('digital-human-2d-image');
            if (imageElement && window.enhanced2DAnimations) {
                console.log('🎬 开始2D动画演示...');
                window.enhanced2DAnimations.playEffect(imageElement, 'attention');
            }
        }, 3000);
    }, 2000);
});

console.log('🎭 增强版2D动画系统加载完成');