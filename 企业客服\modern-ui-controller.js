/**
 * 现代化UI交互控制器
 * Linus设计哲学：简单的事件处理，清晰的状态管理
 */

class ModernUIController {
    constructor() {
        this.isModelPanelOpen = false;
        this.currentTheme = 'dark';
        this.init();
    }
    
    init() {
        console.log('🎨 初始化现代化UI控制器');
        this.bindEvents();
        this.setupModelPanel();
        this.setupToolbar();
    }
    
    /**
     * 绑定所有UI事件
     */
    bindEvents() {
        // 模型选择面板切换
        const modelSelector = document.getElementById('model-selector');
        if (modelSelector) {
            modelSelector.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleModelPanel();
            });
        }
        
        // 诊断按钮
        const diagnosticsBtn = document.getElementById('diagnostics-btn');
        if (diagnosticsBtn) {
            diagnosticsBtn.addEventListener('click', () => {
                this.runDiagnostics();
            });
        }
        
        // 设置按钮
        const settingsBtn = document.getElementById('settings-btn');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => {
                this.openSettings();
            });
        }
        
        // 点击外部关闭面板
        document.addEventListener('click', (e) => {
            const panel = document.getElementById('model-selection-panel');
            const selector = document.getElementById('model-selector');
            
            if (panel && !panel.contains(e.target) && e.target !== selector) {
                this.closeModelPanel();
            }
        });
        
        // ESC键关闭面板
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModelPanel();
            }
        });
        
        // 消息输入框回车发送
        const messageInput = document.getElementById('message-input');
        if (messageInput) {
            messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    if (window.digitalHuman && digitalHuman.sendMessage) {
                        digitalHuman.sendMessage();
                    }
                }
            });
        }
    }
    
    /**
     * 切换模型选择面板
     */
    toggleModelPanel() {
        if (this.isModelPanelOpen) {
            this.closeModelPanel();
        } else {
            this.openModelPanel();
        }
    }
    
    /**
     * 打开模型选择面板
     */
    openModelPanel() {
        const panel = document.getElementById('model-selection-panel');
        const selector = document.getElementById('model-selector');
        
        if (panel && selector) {
            panel.style.display = 'flex';
            selector.classList.add('active');
            this.isModelPanelOpen = true;
            
            // 更新模型选项
            this.updateModelOptions();
            
            // 添加打开动画
            panel.style.opacity = '0';
            panel.style.transform = 'translateY(-10px)';
            
            requestAnimationFrame(() => {
                panel.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                panel.style.opacity = '1';
                panel.style.transform = 'translateY(0)';
            });
        }
    }
    
    /**
     * 关闭模型选择面板
     */
    closeModelPanel() {
        const panel = document.getElementById('model-selection-panel');
        const selector = document.getElementById('model-selector');
        
        if (panel && selector) {
            panel.style.opacity = '0';
            panel.style.transform = 'translateY(-10px)';
            
            setTimeout(() => {
                panel.style.display = 'none';
                selector.classList.remove('active');
                this.isModelPanelOpen = false;
            }, 300);
        }
    }
    
    /**
     * 设置模型选择面板
     */
    setupModelPanel() {
        // 等待模型管理器初始化
        setTimeout(() => {
            this.updateModelOptions();
        }, 2000);
    }
    
    /**
     * 更新模型选项
     */
    updateModelOptions() {
        const container = document.getElementById('model-options-container');
        if (!container || !window.modelManager) return;
        
        const models = window.modelManager.availableModels;
        const currentModel = window.modelManager.currentModel;
        
        container.innerHTML = '';
        
        Object.entries(models).forEach(([id, model]) => {
            const option = document.createElement('div');
            option.className = 'model-option';
            if (currentModel === id) {
                option.classList.add('active');
            }
            
            const isLoading = window.modelManager.loadingModel === id;
            const progress = window.modelManager.loadingProgress.get(id) || 0;
            
            let statusIcon = '';
            if (currentModel === id) statusIcon = '✅';
            else if (isLoading) statusIcon = '⏳';
            else if (model.available === false) statusIcon = '❌';
            else if (model.type === '2d') statusIcon = '⚡';
            else statusIcon = '📦';
            
            // 使用安全的 DOM 操作替代 innerHTML
            const nameEl = document.createElement('div');
            nameEl.className = 'model-name';
            nameEl.textContent = model.name;

            const descEl = document.createElement('div');
            descEl.className = 'model-description';
            descEl.textContent = model.description;

            const statusEl = document.createElement('div');
            statusEl.className = 'model-status';
            const loadTimeSpan = document.createElement('span');
            loadTimeSpan.textContent = `加载时间: ${model.loadTime}`;
            const iconSpan = document.createElement('span');
            iconSpan.textContent = statusIcon;
            statusEl.appendChild(loadTimeSpan);
            statusEl.appendChild(iconSpan);

            const featuresEl = document.createElement('div');
            featuresEl.className = 'model-features';
            featuresEl.textContent = model.features.join(' · ');

            option.appendChild(nameEl);
            option.appendChild(descEl);
            option.appendChild(statusEl);
            option.appendChild(featuresEl);

            if (isLoading) {
                const progressBar = document.createElement('div');
                progressBar.className = 'progress-bar';
                const progressFill = document.createElement('div');
                progressFill.className = 'progress-fill';
                progressFill.style.width = `${progress}%`;
                progressBar.appendChild(progressFill);
                option.appendChild(progressBar);
            }
            
            option.addEventListener('click', () => {
                if (!isLoading && window.modelManager.selectModel) {
                    window.modelManager.selectModel(id);
                    this.closeModelPanel();
                }
            });
            
            container.appendChild(option);
        });
    }
    
    /**
     * 设置工具栏
     */
    setupToolbar() {
        // 工具栏按钮悬浮效果已在CSS中定义
        console.log('🔧 工具栏设置完成');
    }
    
    /**
     * 运行系统诊断
     */
    runDiagnostics() {
        const btn = document.getElementById('diagnostics-btn');
        if (btn) {
            btn.style.transform = 'scale(0.95)';
            setTimeout(() => {
                btn.style.transform = '';
            }, 150);
        }
        
        if (window.modelDiagnostics) {
            window.modelDiagnostics.runFullDiagnostics();
            this.showNotification('🔍 系统诊断已启动', 'info');
        } else {
            this.showNotification('❌ 诊断系统未加载', 'error');
        }
    }
    
    /**
     * 打开设置
     */
    openSettings() {
        const btn = document.getElementById('settings-btn');
        if (btn) {
            btn.style.transform = 'scale(0.95)';
            setTimeout(() => {
                btn.style.transform = '';
            }, 150);
        }
        
        this.showNotification('⚙️ 设置功能开发中', 'info');
    }
    
    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? 'var(--error)' : type === 'success' ? 'var(--success)' : 'var(--info)'};
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            box-shadow: var(--shadow-medium);
            z-index: 10000;
            font-size: 14px;
            animation: slideInRight 0.3s ease;
        `;
        
        notification.textContent = message;
        document.body.appendChild(notification);
        
        // 自动移除
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
    
    /**
     * 更新状态指示器
     */
    updateStatus(message, type = 'info') {
        const status = document.getElementById('status');
        if (status) {
            status.textContent = message;
            
            // 更新状态指示器颜色
            const indicator = status.parentElement;
            if (indicator) {
                const dot = indicator.querySelector('::before');
                // CSS变量已定义颜色
            }
        }
    }
    
    /**
     * 添加消息到聊天
     */
    addMessage(content, isUser = false) {
        const messages = document.getElementById('messages');
        if (!messages) return;
        
        const message = document.createElement('div');
        message.className = `message ${isUser ? 'user' : 'ai'}`;
        message.textContent = content;
        
        messages.appendChild(message);
        
        // 滚动到底部
        messages.scrollTop = messages.scrollHeight;
    }
    
    /**
     * 清空聊天记录
     */
    clearMessages() {
        const messages = document.getElementById('messages');
        if (messages) {
            messages.innerHTML = '';
        }
    }
    
    /**
     * 设置加载状态
     */
    setLoading(isLoading) {
        const sendBtn = document.getElementById('send-btn');
        const messageInput = document.getElementById('message-input');
        
        if (sendBtn && messageInput) {
            sendBtn.disabled = isLoading;
            messageInput.disabled = isLoading;
            
            if (isLoading) {
                sendBtn.innerHTML = '⏳';
            } else {
                sendBtn.innerHTML = '➤';
            }
        }
    }
    
    /**
     * 获取输入框内容
     */
    getInputValue() {
        const input = document.getElementById('message-input');
        return input ? input.value.trim() : '';
    }
    
    /**
     * 清空输入框
     */
    clearInput() {
        const input = document.getElementById('message-input');
        if (input) {
            input.value = '';
        }
    }
}

// 动画样式
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(100px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    @keyframes slideOutRight {
        from {
            opacity: 1;
            transform: translateX(0);
        }
        to {
            opacity: 0;
            transform: translateX(100px);
        }
    }
`;
document.head.appendChild(style);

// 创建全局UI控制器实例
window.modernUI = new ModernUIController();

// 扩展数字人系统
document.addEventListener('DOMContentLoaded', function() {
    // 等待其他系统初始化
    setTimeout(() => {
        if (window.digitalHuman) {
            // 扩展数字人的UI方法
            digitalHuman.modernUI = window.modernUI;
            
            // 重写sendMessage方法
            const originalSendMessage = digitalHuman.sendMessage;
            digitalHuman.sendMessage = function() {
                const message = window.modernUI.getInputValue();
                if (!message) return;
                
                // 显示用户消息
                window.modernUI.addMessage(message, true);
                window.modernUI.clearInput();
                window.modernUI.setLoading(true);
                
                // 调用原方法或模拟AI回复
                setTimeout(() => {
                    window.modernUI.addMessage('您好！我是企业数字人客服，很高兴为您服务。', false);
                    window.modernUI.setLoading(false);
                }, 1000);
            };
            
            // 重写updateStatus方法
            const originalUpdateStatus = digitalHuman.updateStatus;
            digitalHuman.updateStatus = function(message) {
                if (originalUpdateStatus) {
                    originalUpdateStatus.call(this, message);
                }
                window.modernUI.updateStatus(message);
            };
            
            console.log('✅ 现代化UI已集成到数字人系统');
        }
    }, 1000);
});

console.log('🎨 现代化UI控制器加载完成');