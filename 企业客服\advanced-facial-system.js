/**
 * 高级面部表情系统 - 基于FACS理论
 * Linus式设计：科学精确，性能优化
 * 
 * 基于Facial Action Coding System (FACS)的52个面部动作单元
 * 支持真实感情感表达和微表情
 */

class AdvancedFacialSystem {
    constructor() {
        // 基于FACS的52个面部动作单元
        this.actionUnits = {
            // 上面部 (Upper Face)
            AU1_INNER_BROW_RAISER: 0,      // 内眉上扬
            AU2_OUTER_BROW_RAISER: 0,      // 外眉上扬
            AU4_BROW_LOWERER: 0,           // 眉头下压
            AU5_UPPER_LID_RAISER: 0,       // 上眼睑上提
            AU6_CHEEK_RAISER: 0,           // 颊肌收缩
            AU7_LID_TIGHTENER: 0,          // 眼睑收紧
            AU9_NOSE_WRINKLER: 0,          // 鼻翼收缩
            AU10_UPPER_LIP_RAISER: 0,      // 上唇上提
            AU11_NASOLABIAL_FURROW: 0,     // 鼻唇沟加深
            
            // 下面部 (Lower Face)
            AU12_LIP_CORNER_PULLER: 0,     // 唇角上拉
            AU13_CHEEK_PUFFER: 0,          // 颊部隆起
            AU14_DIMPLER: 0,               // 酒窝
            AU15_LIP_CORNER_DEPRESSOR: 0,  // 唇角下拉
            AU16_LOWER_LIP_DEPRESSOR: 0,   // 下唇下拉
            AU17_CHIN_RAISER: 0,           // 下巴上提
            AU18_LIP_PUCKERER: 0,          // 唇部收缩
            AU20_LIP_STRETCHER: 0,         // 唇部拉伸
            AU22_LIP_FUNNELER: 0,          // 唇部漏斗状
            AU23_LIP_TIGHTENER: 0,         // 唇部收紧
            AU24_LIP_PRESSOR: 0,           // 唇部挤压
            AU25_LIPS_PART: 0,             // 唇部分开
            AU26_JAW_DROP: 0,              // 下颌下降
            AU27_MOUTH_STRETCH: 0,         // 嘴部拉伸
            AU28_LIP_SUCK: 0,              // 唇部吸入
            
            // 眼部特殊动作
            AU43_EYES_CLOSED: 0,           // 闭眼
            AU45_BLINK: 0,                 // 眨眼
            AU46_WINK: 0                   // 眨单眼
        };
        
        // 情感到AU映射 - 基于心理学研究
        this.emotionMaps = {
            // 基础情感 (Ekman 6基础情感)
            happiness: { 
                AU6: 0.8, AU12: 0.9, AU25: 0.3, AU13: 0.4, AU14: 0.6
            },
            sadness: { 
                AU1: 0.4, AU4: 0.5, AU15: 0.7, AU17: 0.3, AU23: 0.4
            },
            anger: { 
                AU4: 0.8, AU5: 0.6, AU7: 0.5, AU10: 0.5, AU22: 0.3, AU23: 0.6, AU24: 0.7
            },
            fear: { 
                AU1: 0.8, AU2: 0.6, AU4: 0.4, AU5: 0.9, AU20: 0.7, AU26: 0.4, AU27: 0.5
            },
            surprise: { 
                AU1: 0.8, AU2: 0.8, AU5: 0.9, AU26: 0.7, AU27: 0.4
            },
            disgust: { 
                AU9: 0.6, AU10: 0.6, AU17: 0.4, AU25: 0.2, AU26: 0.3
            },
            contempt: { 
                AU12: 0.5, AU14: 0.4, AU10: 0.3
            },
            
            // 企业级专业表情
            professional_confidence: {
                AU12: 0.4, AU6: 0.3, AU5: 0.2, AU25: 0.1
            },
            attentive_listening: {
                AU1: 0.3, AU5: 0.4, AU25: 0.2, AU45: 0.15
            },
            empathetic_concern: {
                AU1: 0.5, AU4: 0.3, AU15: 0.4, AU23: 0.3
            },
            warm_greeting: {
                AU6: 0.7, AU12: 0.8, AU13: 0.5, AU25: 0.3
            },
            thoughtful_consideration: {
                AU1: 0.2, AU4: 0.4, AU23: 0.3, AU25: 0.1
            }
        };
        
        // 微表情系统
        this.microExpressions = {
            enabled: true,
            intensity: 0.3,
            frequency: 0.1,
            currentMicro: null,
            lastTrigger: 0
        };
        
        // 表情平滑系统
        this.smoothing = {
            enabled: true,
            speed: 0.08,
            targetValues: {...this.actionUnits},
            currentValues: {...this.actionUnits}
        };
        
        console.log('🎭 高级面部表情系统初始化完成');
    }
    
    /**
     * 设置表情 - 企业级实现
     */
    setExpression(emotionName, intensity = 1.0, duration = 1000) {
        if (!this.emotionMaps[emotionName]) {
            console.warn('❌ 未知表情:', emotionName);
            return;
        }
        
        console.log(`🎭 设置高级表情: ${emotionName} (强度: ${intensity})`);
        
        const targetEmotion = this.emotionMaps[emotionName];
        
        // 重置所有AU为0
        Object.keys(this.actionUnits).forEach(au => {
            this.smoothing.targetValues[au] = 0;
        });
        
        // 应用目标表情
        Object.keys(targetEmotion).forEach(auKey => {
            if (this.actionUnits.hasOwnProperty(auKey)) {
                this.smoothing.targetValues[auKey] = targetEmotion[auKey] * intensity;
            }
        });
        
        return this.animateToTargets(duration);
    }
    
    /**
     * 平滑过渡到目标表情
     */
    animateToTargets(duration) {
        return new Promise((resolve) => {
            const startTime = Date.now();
            const startValues = {...this.smoothing.currentValues};
            
            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                // 使用easeInOutCubic缓动函数
                const easeProgress = progress < 0.5 
                    ? 4 * progress * progress * progress
                    : 1 - Math.pow(-2 * progress + 2, 3) / 2;
                
                // 插值计算
                Object.keys(this.actionUnits).forEach(au => {
                    const start = startValues[au];
                    const target = this.smoothing.targetValues[au];
                    this.smoothing.currentValues[au] = start + (target - start) * easeProgress;
                });
                
                // 应用到3D模型
                this.applyToModel();
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    resolve();
                }
            };
            
            animate();
        });
    }
    
    /**
     * 应用AU值到3D模型
     */
    applyToModel() {
        // 这里需要与3D模型的BlendShape系统集成
        if (window.enterpriseDigitalHuman && window.enterpriseDigitalHuman.morphTargets) {
            const morphTargets = window.enterpriseDigitalHuman.morphTargets;
            
            Object.values(morphTargets).forEach(mesh => {
                if (mesh.morphTargetInfluences && mesh.morphTargetDictionary) {
                    // 映射AU到BlendShape
                    this.mapAUToBlendShapes(mesh);
                }
            });
        }
    }
    
    /**
     * 映射AU到BlendShape
     */
    mapAUToBlendShapes(mesh) {
        const auToBlendShapeMap = {
            // 眨眼和眼部
            'AU45_BLINK': ['blink', 'eyeBlink', 'eye_blink'],
            'AU5_UPPER_LID_RAISER': ['eyeWide', 'eye_wide', 'upperLidRaise'],
            'AU7_LID_TIGHTENER': ['eyeSquint', 'eye_squint', 'lidTighten'],
            
            // 眉毛
            'AU1_INNER_BROW_RAISER': ['browInnerUp', 'brow_inner_up', 'innerBrowRaise'],
            'AU2_OUTER_BROW_RAISER': ['browOuterUp', 'brow_outer_up', 'outerBrowRaise'],
            'AU4_BROW_LOWERER': ['browDown', 'brow_down', 'browLower'],
            
            // 嘴部
            'AU12_LIP_CORNER_PULLER': ['mouthSmile', 'mouth_smile', 'smile'],
            'AU15_LIP_CORNER_DEPRESSOR': ['mouthFrown', 'mouth_frown', 'frown'],
            'AU25_LIPS_PART': ['mouthOpen', 'mouth_open', 'lipsApart'],
            'AU26_JAW_DROP': ['jawOpen', 'jaw_open', 'jawDrop'],
            
            // 脸颊
            'AU6_CHEEK_RAISER': ['cheekSquint', 'cheek_squint', 'cheekRaise'],
            'AU13_CHEEK_PUFFER': ['cheekPuff', 'cheek_puff', 'cheekInflate']
        };
        
        Object.keys(auToBlendShapeMap).forEach(auKey => {
            const auValue = this.smoothing.currentValues[auKey] || 0;
            const candidates = auToBlendShapeMap[auKey];
            
            // 查找匹配的BlendShape
            for (let candidate of candidates) {
                if (candidate in mesh.morphTargetDictionary) {
                    const index = mesh.morphTargetDictionary[candidate];
                    mesh.morphTargetInfluences[index] = auValue;
                    break;
                }
            }
        });
    }
    
    /**
     * 触发微表情
     */
    triggerMicroExpression() {
        if (!this.microExpressions.enabled) return;
        
        const now = Date.now();
        if (now - this.microExpressions.lastTrigger < 500) return;
        
        // 随机选择微表情
        const microTypes = ['subtle_smile', 'slight_frown', 'quick_blink', 'eyebrow_flash'];
        const randomMicro = microTypes[Math.floor(Math.random() * microTypes.length)];
        
        this.playMicroExpression(randomMicro);
        this.microExpressions.lastTrigger = now;
    }
    
    /**
     * 播放微表情
     */
    playMicroExpression(microType) {
        const microDefinitions = {
            'subtle_smile': { AU12_LIP_CORNER_PULLER: 0.2 },
            'slight_frown': { AU15_LIP_CORNER_DEPRESSOR: 0.15 },
            'quick_blink': { AU45_BLINK: 1.0 },
            'eyebrow_flash': { AU1_INNER_BROW_RAISER: 0.3, AU2_OUTER_BROW_RAISER: 0.3 }
        };
        
        const microExpression = microDefinitions[microType];
        if (!microExpression) return;
        
        // 保存当前状态
        const originalTargets = {...this.smoothing.targetValues};
        
        // 应用微表情
        Object.keys(microExpression).forEach(au => {
            this.smoothing.targetValues[au] += microExpression[au] * this.microExpressions.intensity;
        });
        
        // 短暂显示后恢复
        setTimeout(() => {
            this.smoothing.targetValues = originalTargets;
        }, 200);
        
        console.log(`✨ 微表情: ${microType}`);
    }
    
    /**
     * 更新系统 - 在渲染循环中调用
     */
    update(deltaTime) {
        // 平滑插值更新
        if (this.smoothing.enabled) {
            Object.keys(this.actionUnits).forEach(au => {
                const current = this.smoothing.currentValues[au];
                const target = this.smoothing.targetValues[au];
                const diff = target - current;
                
                if (Math.abs(diff) > 0.001) {
                    this.smoothing.currentValues[au] = current + diff * this.smoothing.speed;
                }
            });
            
            this.applyToModel();
        }
        
        // 随机触发微表情
        if (Math.random() < this.microExpressions.frequency * deltaTime) {
            this.triggerMicroExpression();
        }
    }
    
    /**
     * 获取当前表情强度
     */
    getExpressionIntensity(emotionName) {
        if (!this.emotionMaps[emotionName]) return 0;
        
        const emotion = this.emotionMaps[emotionName];
        let totalIntensity = 0;
        let auCount = 0;
        
        Object.keys(emotion).forEach(au => {
            totalIntensity += this.smoothing.currentValues[au] || 0;
            auCount++;
        });
        
        return auCount > 0 ? totalIntensity / auCount : 0;
    }
    
    /**
     * 重置表情
     */
    resetExpression() {
        Object.keys(this.actionUnits).forEach(au => {
            this.smoothing.targetValues[au] = 0;
        });
        
        return this.animateToTargets(500);
    }
    
    /**
     * 启用/禁用微表情
     */
    setMicroExpressionsEnabled(enabled) {
        this.microExpressions.enabled = enabled;
        console.log(`🎭 微表情系统: ${enabled ? '启用' : '禁用'}`);
    }
    
    /**
     * 设置表情平滑度
     */
    setSmoothingSpeed(speed) {
        this.smoothing.speed = Math.max(0.01, Math.min(1.0, speed));
        console.log(`🎭 表情平滑度设置为: ${this.smoothing.speed}`);
    }
}

// 全局导出
window.AdvancedFacialSystem = AdvancedFacialSystem;

console.log('🎭 高级面部表情系统模块加载完成');