/**
 * 电影级光照系统
 * Linus式设计：专业灯光，真实渲染，性能平衡
 * 
 * 核心特性：
 * - 三点照明法（主光、补光、轮廓光）
 * - 区域光源支持
 * - 动态阴影优化
 * - SSAO环境光遮蔽
 * - 屏幕空间反射
 * - HDR环境光照
 */

class CinematicLighting {
    constructor(scene, renderer) {
        this.scene = scene;
        this.renderer = renderer;
        
        // 灯光配置
        this.lightConfig = {
            // 主光配置
            keyLight: {
                color: 0xffffff,
                intensity: 2.5,
                position: { x: 2, y: 4, z: 3 },
                target: { x: 0, y: 1.5, z: 0 },
                castShadow: true,
                shadowMapSize: 2048
            },
            
            // 补光配置
            fillLight: {
                color: 0xfff8e7,
                intensity: 1.2,
                position: { x: -2, y: 2, z: 2 },
                target: { x: 0, y: 1.5, z: 0 },
                castShadow: false
            },
            
            // 轮廓光配置
            rimLight: {
                color: 0x8ec5ff,
                intensity: 0.8,
                position: { x: 0, y: 3, z: -2 },
                target: { x: 0, y: 1.5, z: 0 },
                castShadow: false
            },
            
            // 环境光配置
            ambientLight: {
                color: 0x404040,
                intensity: 0.4
            }
        };
        
        // 灯光实例
        this.lights = {
            keyLight: null,
            fillLight: null,
            rimLight: null,
            ambientLight: null,
            areaLights: []
        };
        
        // 高级特效
        this.effects = {
            ssao: null,
            ssr: null,
            volumetricLighting: false,
            godRays: false
        };
        
        console.log('🎬 电影级光照系统初始化');
    }
    
    /**
     * 设置真实感光照
     */
    setupRealisticLighting() {
        console.log('🎥 配置电影级三点照明法');
        
        // 清除现有光照
        this.clearExistingLights();
        
        // 主光 - Key Light
        this.setupKeyLight();
        
        // 补光 - Fill Light  
        this.setupFillLight();
        
        // 轮廓光 - Rim Light
        this.setupRimLight();
        
        // 环境光增强
        this.setupEnhancedAmbientLight();
        
        // 区域光源
        this.setupAreaLights();
        
        console.log('✅ 电影级光照配置完成');
    }
    
    /**
     * 清除现有光照
     */
    clearExistingLights() {
        const lightsToRemove = [];
        this.scene.traverse((child) => {
            if (child.isLight) {
                lightsToRemove.push(child);
            }
        });
        
        lightsToRemove.forEach(light => {
            this.scene.remove(light);
        });
        
        console.log(`🧹 清除了 ${lightsToRemove.length} 个旧光源`);
    }
    
    /**
     * 设置主光源
     */
    setupKeyLight() {
        const config = this.lightConfig.keyLight;
        
        // 使用方向光作为主光
        this.lights.keyLight = new THREE.DirectionalLight(config.color, config.intensity);
        this.lights.keyLight.position.set(config.position.x, config.position.y, config.position.z);
        
        // 设置目标
        this.lights.keyLight.target.position.set(config.target.x, config.target.y, config.target.z);
        
        // 配置阴影
        if (config.castShadow) {
            this.lights.keyLight.castShadow = true;
            this.lights.keyLight.shadow.mapSize.setScalar(config.shadowMapSize);
            
            // 阴影相机配置
            const shadowCam = this.lights.keyLight.shadow.camera;
            shadowCam.near = 0.1;
            shadowCam.far = 20;
            shadowCam.left = -4;
            shadowCam.right = 4;
            shadowCam.top = 4;
            shadowCam.bottom = -4;
            
            // 阴影质量优化
            this.lights.keyLight.shadow.bias = -0.0001;
            this.lights.keyLight.shadow.normalBias = 0.02;
            this.lights.keyLight.shadow.radius = 4;
        }
        
        this.scene.add(this.lights.keyLight);
        this.scene.add(this.lights.keyLight.target);
        
        console.log('💡 主光源配置完成');
    }
    
    /**
     * 设置补光
     */
    setupFillLight() {
        const config = this.lightConfig.fillLight;
        
        this.lights.fillLight = new THREE.DirectionalLight(config.color, config.intensity);
        this.lights.fillLight.position.set(config.position.x, config.position.y, config.position.z);
        this.lights.fillLight.target.position.set(config.target.x, config.target.y, config.target.z);
        
        this.scene.add(this.lights.fillLight);
        this.scene.add(this.lights.fillLight.target);
        
        console.log('💡 补光配置完成');
    }
    
    /**
     * 设置轮廓光
     */
    setupRimLight() {
        const config = this.lightConfig.rimLight;
        
        this.lights.rimLight = new THREE.DirectionalLight(config.color, config.intensity);
        this.lights.rimLight.position.set(config.position.x, config.position.y, config.position.z);
        this.lights.rimLight.target.position.set(config.target.x, config.target.y, config.target.z);
        
        this.scene.add(this.lights.rimLight);
        this.scene.add(this.lights.rimLight.target);
        
        console.log('💡 轮廓光配置完成');
    }
    
    /**
     * 设置增强环境光
     */
    setupEnhancedAmbientLight() {
        const config = this.lightConfig.ambientLight;
        
        this.lights.ambientLight = new THREE.AmbientLight(config.color, config.intensity);
        this.scene.add(this.lights.ambientLight);
        
        console.log('💡 环境光配置完成');
    }
    
    /**
     * 创建区域光源
     */
    createAreaLight(color, intensity, width, height) {
        // 使用RectAreaLight模拟区域光
        if (THREE.RectAreaLight) {
            const areaLight = new THREE.RectAreaLight(color, intensity, width, height);
            return areaLight;
        } else {
            // 降级到点光源
            console.warn('⚠️ RectAreaLight不可用，使用点光源替代');
            return new THREE.PointLight(color, intensity, 10);
        }
    }
    
    /**
     * 创建聚光灯
     */
    createSpotLight(color, intensity) {
        const spotLight = new THREE.SpotLight(color, intensity);
        spotLight.angle = Math.PI / 6;      // 30度角
        spotLight.penumbra = 0.3;           // 柔和边缘
        spotLight.decay = 2;                // 物理正确衰减
        spotLight.distance = 20;            // 光照距离
        
        return spotLight;
    }
    
    /**
     * 设置区域光源
     */
    setupAreaLights() {
        // 面部区域光 - 柔和照明
        const faceAreaLight = this.createAreaLight(0xffeedd, 0.8, 1, 1);
        faceAreaLight.position.set(0, 2, 1.5);
        faceAreaLight.lookAt(0, 1.5, 0);
        this.lights.areaLights.push(faceAreaLight);
        this.scene.add(faceAreaLight);
        
        // 背景区域光 - 环境照明
        const backgroundAreaLight = this.createAreaLight(0x87ceeb, 0.4, 2, 2);
        backgroundAreaLight.position.set(0, 1, -3);
        backgroundAreaLight.lookAt(0, 1, 0);
        this.lights.areaLights.push(backgroundAreaLight);
        this.scene.add(backgroundAreaLight);
        
        console.log('💡 区域光源配置完成');
    }
    
    /**
     * 设置基于图像的光照
     */
    setupImageBasedLighting() {
        console.log('🌅 配置IBL环境光照');
        
        // 创建程序化HDR环境
        const hdrEnvironment = this.createHDREnvironment();
        
        // 生成环境贴图
        const pmremGenerator = new THREE.PMREMGenerator(this.renderer);
        pmremGenerator.compileEquirectangularShader();
        
        this.envMap = pmremGenerator.fromScene(hdrEnvironment);
        
        // 应用到场景
        this.scene.environment = this.envMap.texture;
        this.scene.background = this.envMap.texture;
        
        // 清理
        pmremGenerator.dispose();
        hdrEnvironment.dispose();
        
        console.log('✅ IBL环境光照配置完成');
    }
    
    /**
     * 创建HDR环境
     */
    createHDREnvironment() {
        const geometry = new THREE.SphereGeometry(50, 32, 16);
        
        // 创建渐变材质模拟专业摄影棚
        const vertexShader = `
            varying vec3 vWorldPosition;
            void main() {
                vec4 worldPosition = modelMatrix * vec4(position, 1.0);
                vWorldPosition = worldPosition.xyz;
                gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
        `;
        
        const fragmentShader = `
            varying vec3 vWorldPosition;
            void main() {
                vec3 direction = normalize(vWorldPosition);
                float elevation = direction.y;
                float azimuth = atan(direction.z, direction.x);
                
                // 专业摄影棚环境
                vec3 studioTop = vec3(0.95, 0.95, 0.98);      // 冷白光顶部
                vec3 studioMid = vec3(0.85, 0.88, 0.92);      // 中性中间
                vec3 studioBot = vec3(0.75, 0.78, 0.82);      // 稍暖底部
                
                // 垂直渐变
                vec3 studioColor = mix(studioBot, studioMid, smoothstep(-0.3, 0.3, elevation));
                studioColor = mix(studioColor, studioTop, smoothstep(0.3, 0.8, elevation));
                
                // 添加主要光源方向
                vec3 keyDirection = normalize(vec3(0.5, 0.8, 0.3));
                float keyDot = max(dot(direction, keyDirection), 0.0);
                vec3 keyColor = vec3(1.2, 1.1, 1.0) * pow(keyDot, 128.0);
                
                // 添加补光方向
                vec3 fillDirection = normalize(vec3(-0.3, 0.5, 0.2));
                float fillDot = max(dot(direction, fillDirection), 0.0);
                vec3 fillColor = vec3(0.8, 0.85, 0.9) * pow(fillDot, 64.0);
                
                // 合成最终颜色
                vec3 finalColor = studioColor + keyColor + fillColor;
                
                gl_FragColor = vec4(finalColor, 1.0);
            }
        `;
        
        const material = new THREE.ShaderMaterial({
            vertexShader,
            fragmentShader,
            side: THREE.BackSide
        });
        
        const environment = new THREE.Scene();
        environment.add(new THREE.Mesh(geometry, material));
        
        return environment;
    }
    
    /**
     * 设置SSAO环境光遮蔽
     */
    setupSSAO() {
        console.log('🔍 配置SSAO环境光遮蔽');
        
        // 这个方法需要与后处理管线集成
        // 在EffectComposer中添加SSAO Pass
        this.effects.ssao = {
            enabled: true,
            radius: 0.1,
            intensity: 0.5,
            bias: 0.01,
            samples: 32
        };
        
        console.log('✅ SSAO配置完成');
    }
    
    /**
     * 设置屏幕空间反射
     */
    setupSSR() {
        console.log('✨ 配置屏幕空间反射');
        
        this.effects.ssr = {
            enabled: true,
            intensity: 0.3,
            roughnessFade: 0.8,
            thickness: 0.1,
            maxDistance: 5.0
        };
        
        console.log('✅ SSR配置完成');
    }
    
    /**
     * 动态调整光照
     */
    adjustLightingForMood(mood) {
        const moodPresets = {
            professional: {
                keyIntensity: 2.5,
                fillIntensity: 1.2,
                rimIntensity: 0.8,
                ambientIntensity: 0.4,
                temperature: 5500  // 白平衡
            },
            warm: {
                keyIntensity: 2.0,
                fillIntensity: 1.0,
                rimIntensity: 0.6,
                ambientIntensity: 0.5,
                temperature: 3200  // 暖色调
            },
            dramatic: {
                keyIntensity: 3.0,
                fillIntensity: 0.8,
                rimIntensity: 1.2,
                ambientIntensity: 0.2,
                temperature: 6500  // 冷色调
            },
            soft: {
                keyIntensity: 1.8,
                fillIntensity: 1.5,
                rimIntensity: 0.5,
                ambientIntensity: 0.6,
                temperature: 4500  // 中性偏暖
            }
        };
        
        const preset = moodPresets[mood];
        if (!preset) {
            console.warn('❌ 未知光照情绪:', mood);
            return;
        }
        
        console.log(`🎭 切换到${mood}光照模式`);
        
        // 应用光照强度
        if (this.lights.keyLight) this.lights.keyLight.intensity = preset.keyIntensity;
        if (this.lights.fillLight) this.lights.fillLight.intensity = preset.fillIntensity;
        if (this.lights.rimLight) this.lights.rimLight.intensity = preset.rimIntensity;
        if (this.lights.ambientLight) this.lights.ambientLight.intensity = preset.ambientIntensity;
        
        // 应用色温
        this.applyColorTemperature(preset.temperature);
    }
    
    /**
     * 应用色温
     */
    applyColorTemperature(temperature) {
        // 色温到RGB的转换
        const tempToRGB = (temp) => {
            temp = temp / 100;
            let r, g, b;
            
            if (temp <= 66) {
                r = 255;
                g = temp;
                g = 99.4708025861 * Math.log(g) - 161.1195681661;
                
                if (temp >= 19) {
                    b = temp - 10;
                    b = 138.5177312231 * Math.log(b) - 305.0447927307;
                } else {
                    b = 0;
                }
            } else {
                r = temp - 60;
                r = 329.698727446 * Math.pow(r, -0.1332047592);
                
                g = temp - 60;
                g = 288.1221695283 * Math.pow(g, -0.0755148492);
                
                b = 255;
            }
            
            return {
                r: Math.max(0, Math.min(255, r)) / 255,
                g: Math.max(0, Math.min(255, g)) / 255,
                b: Math.max(0, Math.min(255, b)) / 255
            };
        };
        
        const rgb = tempToRGB(temperature);
        const color = new THREE.Color(rgb.r, rgb.g, rgb.b);
        
        // 应用到主光源
        if (this.lights.keyLight) {
            this.lights.keyLight.color.copy(color);
        }
        
        console.log(`🌡️ 色温设置为: ${temperature}K`);
    }
    
    /**
     * 更新光照系统
     */
    update(deltaTime) {
        // 可以添加动态光照效果
        // 例如：轻微的光照摆动、色彩变化等
        
        if (this.lights.keyLight && this.effects.dynamicLighting) {
            const time = Date.now() * 0.001;
            const flicker = Math.sin(time * 2) * 0.05 + 1;
            this.lights.keyLight.intensity = this.lightConfig.keyLight.intensity * flicker;
        }
    }
    
    /**
     * 获取光照信息
     */
    getLightingInfo() {
        return {
            keyLight: this.lights.keyLight ? {
                intensity: this.lights.keyLight.intensity,
                color: this.lights.keyLight.color.getHex(),
                position: this.lights.keyLight.position.toArray()
            } : null,
            fillLight: this.lights.fillLight ? {
                intensity: this.lights.fillLight.intensity,
                color: this.lights.fillLight.color.getHex()
            } : null,
            rimLight: this.lights.rimLight ? {
                intensity: this.lights.rimLight.intensity,
                color: this.lights.rimLight.color.getHex()
            } : null,
            ambientLight: this.lights.ambientLight ? {
                intensity: this.lights.ambientLight.intensity,
                color: this.lights.ambientLight.color.getHex()
            } : null
        };
    }
    
    /**
     * 资源清理
     */
    dispose() {
        // 清理光源
        Object.values(this.lights).forEach(light => {
            if (light && light.dispose) {
                light.dispose();
            }
        });
        
        // 清理环境贴图
        if (this.envMap) {
            this.envMap.dispose();
        }
        
        console.log('🧹 电影级光照系统资源已清理');
    }
}

// 全局导出
window.CinematicLighting = CinematicLighting;

console.log('🎬 电影级光照系统模块加载完成');