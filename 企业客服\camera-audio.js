/**
 * 摄像头和音频处理模块
 * Linus式设计：最简单的实现，最可靠的功能
 * 
 * 功能：
 * 1. WebRTC摄像头访问
 * 2. 音频输入处理
 * 3. 基础交互检测
 * 4. TTS音频播放
 */

class CameraAudioHandler {
    constructor() {
        this.videoStream = null;
        this.audioStream = null;
        this.videoElement = null;
        this.audioContext = null;
        this.analyser = null;
        this.isVideoEnabled = false;
        this.isAudioEnabled = false;
        
        // 音频分析相关
        this.audioData = null;
        this.lastVolume = 0;
        this.volumeThreshold = 50; // 语音检测阈值
        
        // TTS相关
        this.ttsQueue = [];
        this.isSpeaking = false;
    }
    
    /**
     * 初始化摄像头和音频
     */
    async init() {
        try {
            await this.requestCameraPermission();
            await this.requestAudioPermission();
            this.initAudioContext();
            this.createVideoElement();
            this.startVoiceDetection();
            
            console.log('摄像头和音频初始化完成');
            return true;
        } catch (error) {
            console.error('摄像头或音频初始化失败:', error);
            return false;
        }
    }
    
    /**
     * 请求摄像头权限
     */
    async requestCameraPermission() {
        try {
            const constraints = {
                video: {
                    width: { ideal: 640 },
                    height: { ideal: 480 },
                    facingMode: 'user' // 前置摄像头
                }
            };
            
            this.videoStream = await navigator.mediaDevices.getUserMedia(constraints);
            this.isVideoEnabled = true;
            
            console.log('摄像头访问成功');
        } catch (error) {
            console.error('摄像头访问失败:', error);
            throw error;
        }
    }
    
    /**
     * 请求音频权限
     */
    async requestAudioPermission() {
        try {
            const constraints = {
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                }
            };
            
            this.audioStream = await navigator.mediaDevices.getUserMedia(constraints);
            this.isAudioEnabled = true;
            
            console.log('音频访问成功');
        } catch (error) {
            console.error('音频访问失败:', error);
            throw error;
        }
    }
    
    /**
     * 初始化音频上下文
     */
    initAudioContext() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            this.analyser = this.audioContext.createAnalyser();
            this.analyser.fftSize = 256;
            this.analyser.smoothingTimeConstant = 0.8;
            
            if (this.audioStream) {
                const source = this.audioContext.createMediaStreamSource(this.audioStream);
                source.connect(this.analyser);

                // 使用 AnalyserNode 周期性读取频谱并发送到 Worker（比 ScriptProcessor 更容易兼容并避免 deprecated API）
                try {
                    this._frequencyData = new Float32Array(this.analyser.frequencyBinCount);
                    this._freqPolling = true;
                    const pollFreq = () => {
                        if (!this._freqPolling) return;
                        try {
                            // getFloatFrequencyData 返回 dB 值，使用绝对值作为幅度指示
                            this.analyser.getFloatFrequencyData(this._frequencyData);
                            if (this._audioWorker && this._audioWorker.postMessage) {
                                // 发送副本（ArrayBuffer）
                                const copy = new Float32Array(this._frequencyData.length);
                                copy.set(this._frequencyData);
                                this._audioWorker.postMessage({ freq: copy }, [copy.buffer]);
                            }
                        } catch (e) {
                            // 忽略单次错误
                        }
                        // 使用 requestAnimationFrame 控制采样频率（近 60Hz）
                        requestAnimationFrame(pollFreq);
                    };
                    requestAnimationFrame(pollFreq);
                } catch (e) {
                    console.warn('无法开始频谱轮询:', e);
                }
            }
            
            this.audioData = new Uint8Array(this.analyser.frequencyBinCount);
            
            // 启动 audio worker（PoC）
            try {
                if (window.Worker) {
                    this._audioWorker = new Worker('camera-audio-worker.js');
                    this._audioWorker.onmessage = (e) => {
                        const msg = e.data;
                        if (msg.error) {
                            console.error('audio worker error:', msg.error);
                            return;
                        }

                        if (msg.visemes) {
                            // 将 viseme 应用到数字人（若有）
                            if (window.digitalHuman && typeof digitalHuman.applyVisemes === 'function') {
                                digitalHuman.applyVisemes(msg.visemes, msg.timestamp);
                            }
                        }
                    };
                }
            } catch (e) {
                console.warn('无法初始化 audio worker:', e);
            }
        } catch (error) {
            console.error('音频上下文初始化失败:', error);
        }
    }
    
    /**
     * 创建视频元素（隐藏，用于处理）
     */
    createVideoElement() {
        if (!this.videoStream) return;
        
        this.videoElement = document.createElement('video');
        this.videoElement.style.display = 'none'; // 隐藏，只用于数据处理
        this.videoElement.autoplay = true;
        this.videoElement.muted = true;
        this.videoElement.srcObject = this.videoStream;
        
        document.body.appendChild(this.videoElement);
    }
    
    /**
     * 开始语音检测 - 增强版错误处理
     */
    startVoiceDetection() {
        if (!this.analyser) {
            console.warn('🔇 分析器未初始化，跳过语音检测');
            return;
        }
        
        // 检查是否应该启用语音检测
        if (window.offlineOptimizer && !window.offlineOptimizer.isFeatureEnabled('voice_detection')) {
            console.log('🔇 语音检测已被离线优化器禁用');
            return;
        }
        
        let voiceDetectionEnabled = true;
        
        const detectVoice = () => {
            // 动态检查是否应该继续语音检测
            if (window.offlineOptimizer && !window.offlineOptimizer.isFeatureEnabled('voice_detection')) {
                voiceDetectionEnabled = false;
                console.log('🔇 语音检测中途禁用');
                return;
            }
            
            if (!voiceDetectionEnabled || !this.analyser || !this.audioData) {
                return; // 停止检测
            }
            
            try {
                this.analyser.getByteFrequencyData(this.audioData);
                
                // 计算音量
                const volume = this.audioData.reduce((sum, value) => sum + value, 0) / this.audioData.length;
                
                // 提高语音检测阈值，减少误触发
                const adjustedThreshold = this.volumeThreshold + 20;
                
                // 检测语音活动（大幅减少日志输出）
                if (volume > adjustedThreshold && this.lastVolume <= adjustedThreshold) {
                    if (Math.random() < 0.01) { // 只有1%概率输出日志
                        this.onVoiceStarted();
                    } else {
                        // 静默触发表情变化，不输出日志
                        if (window.digitalHuman && digitalHuman.avatar) {
                            digitalHuman.setExpression('thinking');
                        }
                    }
                } else if (volume <= adjustedThreshold && this.lastVolume > adjustedThreshold) {
                    if (Math.random() < 0.01) { // 只有1%概率输出日志
                        this.onVoiceStopped();
                    } else {
                        // 静默恢复默认表情，不输出日志
                        if (window.digitalHuman && digitalHuman.avatar) {
                            digitalHuman.setExpression('neutral');
                        }
                    }
                }
                
                this.lastVolume = volume;
                
                // 继续检测
                requestAnimationFrame(detectVoice);
            } catch (error) {
                console.error('语音检测错误:', error);
                voiceDetectionEnabled = false;
            }
        };
        
        detectVoice();
    }
    
    /**
     * 语音开始回调 - 极少日志输出
     */
    onVoiceStarted() {
        // 只有5%的概率输出日志
        if (Math.random() < 0.05) {
            console.log('检测到语音开始');
        }
        
        // 触发3D人物反应
        if (window.digitalHuman && digitalHuman.avatar) {
            digitalHuman.setExpression('thinking');
        }
        
        // 可以在这里添加语音识别逻辑
        // TODO: 集成语音识别API
    }
    
    /**
     * 语音结束回调 - 极少日志输出
     */
    onVoiceStopped() {
        // 只有5%的概率输出日志
        if (Math.random() < 0.05) {
            console.log('语音结束');
        }
        
        // 恢复默认表情
        if (window.digitalHuman && digitalHuman.avatar) {
            digitalHuman.setExpression('neutral');
        }
    }
    
    /**
     * TTS文本转语音
     * 使用浏览器原生API，简单可靠
     */
    speak(text, options = {}) {
        return new Promise((resolve, reject) => {
            if (!('speechSynthesis' in window)) {
                console.error('浏览器不支持TTS');
                reject(new Error('TTS not supported'));
                return;
            }
            
            // 如果正在说话，加入队列
            if (this.isSpeaking) {
                this.ttsQueue.push({ text, options, resolve, reject });
                return;
            }
            
            this.isSpeaking = true;
            
            const utterance = new SpeechSynthesisUtterance(text);
            
            // 设置参数
            utterance.lang = options.lang || 'zh-CN';
            utterance.rate = options.rate || 1.0;
            utterance.pitch = options.pitch || 1.0;
            utterance.volume = options.volume || 1.0;
            
            // 事件处理
            utterance.onstart = () => {
                console.log('TTS开始播放:', text);
                this.onTTSStart();
            };
            
            utterance.onend = () => {
                console.log('TTS播放完成');
                this.isSpeaking = false;
                this.onTTSEnd();
                resolve();
                
                // 处理队列中的下一个
                this.processNextTTS();
            };
            
            utterance.onerror = (error) => {
                console.error('TTS播放错误:', error);
                this.isSpeaking = false;
                this.onTTSEnd();
                reject(error);
            };
            
            // 播放
            speechSynthesis.speak(utterance);
        });
    }
    
    /**
     * TTS开始播放回调
     */
    onTTSStart() {
        // 触发说话动画和表情
        if (window.digitalHuman && digitalHuman.avatar) {
            digitalHuman.setExpression('happy');
            // 这里可以添加嘴部动画逻辑
        }
    }
    
    /**
     * TTS结束播放回调
     */
    onTTSEnd() {
        // 恢复默认状态
        if (window.digitalHuman && digitalHuman.avatar) {
            digitalHuman.setExpression('neutral');
        }
    }
    
    /**
     * 处理TTS队列
     */
    processNextTTS() {
        if (this.ttsQueue.length > 0) {
            const { text, options, resolve, reject } = this.ttsQueue.shift();
            this.speak(text, options).then(resolve).catch(reject);
        }
    }
    
    /**
     * 停止TTS
     */
    stopSpeaking() {
        if ('speechSynthesis' in window) {
            speechSynthesis.cancel();
            this.isSpeaking = false;
            this.ttsQueue = [];
        }
    }
    
    /**
     * 简单的音量检测用于口型同步
     */
    getCurrentVolume() {
        if (!this.analyser || !this.audioData) return 0;
        
        this.analyser.getByteFrequencyData(this.audioData);
        const volume = this.audioData.reduce((sum, value) => sum + value, 0) / this.audioData.length;
        return volume / 255; // 归一化到0-1
    }
    
    /**
     * 基础的口型同步
     * 基于音量的简单实现
     */
    updateLipSync() {
        const volume = this.getCurrentVolume();
        
        if (window.digitalHuman && digitalHuman.avatar && digitalHuman.morphTargets) {
            Object.values(digitalHuman.morphTargets).forEach(mesh => {
                if (mesh.morphTargetInfluences && mesh.morphTargetDictionary) {
                    // 简单的嘴部开合控制
                    const jawIndex = mesh.morphTargetDictionary['jawOpen'] || mesh.morphTargetDictionary['mouth_open'];
                    if (jawIndex !== undefined) {
                        mesh.morphTargetInfluences[jawIndex] = volume * 0.5;
                    }
                }
            });
        }
    }
    
    /**
     * 开启摄像头显示（调试用）
     */
    showCameraFeed() {
        if (this.videoElement) {
            this.videoElement.style.display = 'block';
            this.videoElement.style.position = 'fixed';
            this.videoElement.style.top = '10px';
            this.videoElement.style.right = '10px';
            this.videoElement.style.width = '200px';
            this.videoElement.style.height = '150px';
            this.videoElement.style.border = '2px solid #007bff';
            this.videoElement.style.borderRadius = '4px';
            this.videoElement.style.zIndex = '9999';
        }
    }
    
    /**
     * 关闭摄像头显示
     */
    hideCameraFeed() {
        if (this.videoElement) {
            this.videoElement.style.display = 'none';
        }
    }
    
    /**
     * 清理资源
     */
    cleanup() {
        // 停止视频流
        if (this.videoStream) {
            this.videoStream.getTracks().forEach(track => track.stop());
        }
        
        // 停止音频流
        if (this.audioStream) {
            this.audioStream.getTracks().forEach(track => track.stop());
        }
        
        // 关闭音频上下文
        if (this.audioContext) {
            this.audioContext.close();
        }
        
        // 停止TTS
        this.stopSpeaking();
        
        // 移除视频元素
        if (this.videoElement && this.videoElement.parentNode) {
            this.videoElement.parentNode.removeChild(this.videoElement);
        }
    }
}

// 扩展DigitalHuman类添加摄像头和音频功能
DigitalHuman.prototype.initCameraAudio = async function() {
    this.cameraAudio = new CameraAudioHandler();
    
    try {
        const success = await this.cameraAudio.init();
        if (success) {
            this.updateStatus('摄像头和音频就绪');
            
            // 启动口型同步更新循环
            this.startLipSyncLoop();
            
            return true;
        } else {
            this.updateStatus('摄像头或音频初始化失败');
            return false;
        }
    } catch (error) {
        console.error('摄像头音频初始化失败:', error);
        this.updateStatus('设备访问失败');
        return false;
    }
};

// 启动口型同步循环
DigitalHuman.prototype.startLipSyncLoop = function() {
    if (!this.cameraAudio) return;
    
    const updateLipSync = () => {
        if (this.cameraAudio.isSpeaking) {
            this.cameraAudio.updateLipSync();
        }
        requestAnimationFrame(updateLipSync);
    };
    
    updateLipSync();
};

// TTS播放方法
DigitalHuman.prototype.speak = function(text, options) {
    if (this.cameraAudio) {
        return this.cameraAudio.speak(text, options);
    } else {
        console.warn('音频系统未初始化');
        return Promise.resolve();
    }
};