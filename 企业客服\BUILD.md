# C后端编译和部署说明

## 依赖安装

### Ubuntu/Debian
```bash
sudo apt-get update
sudo apt-get install build-essential
sudo apt-get install libcurl4-openssl-dev
sudo apt-get install libjson-c-dev
sudo apt-get install libwebsockets-dev
```

### CentOS/RHEL
```bash
sudo yum install gcc gcc-c++
sudo yum install libcurl-devel
sudo yum install json-c-devel
sudo yum install libwebsockets-devel
```

### Windows (使用MSYS2)
```bash
pacman -S mingw-w64-x86_64-gcc
pacman -S mingw-w64-x86_64-curl
pacman -S mingw-w64-x86_64-json-c
pacman -S mingw-w64-x86_64-libwebsockets
```

## 编译

```bash
# 标准编译
gcc -o doubao_server doubao_server.c -lcurl -ljson-c -lwebsockets

# 调试版本
gcc -g -DDEBUG -o doubao_server doubao_server.c -lcurl -ljson-c -lwebsockets

# 优化版本
gcc -O2 -o doubao_server doubao_server.c -lcurl -ljson-c -lwebsockets
```

## 配置

在运行之前，请修改以下配置：

1. **豆包API密钥**：
   ```c
   #define DOUBAO_API_KEY "your_actual_api_key_here"
   ```

2. **豆包模型ID**：
   ```c
   #define DOUBAO_MODEL "your_actual_model_endpoint"
   ```

3. **WebSocket端口**（可选）：
   ```c
   #define WEBSOCKET_PORT 8080
   ```

## 运行

```bash
# 前台运行
./doubao_server

# 后台运行
./doubao_server &

# 使用nohup（推荐生产环境）
nohup ./doubao_server > server.log 2>&1 &
```

## 测试连接

使用JavaScript测试WebSocket连接：

```javascript
const ws = new WebSocket('ws://localhost:8080');

ws.onopen = function() {
    console.log('连接成功');
    
    // 发送测试消息
    ws.send(JSON.stringify({
        type: 'ai_chat_request',
        data: {
            user_id: 'test_user',
            message: '你好，请介绍一下你们的服务',
            session_id: 'test_session'
        }
    }));
};

ws.onmessage = function(event) {
    console.log('收到回复:', JSON.parse(event.data));
};
```

## 集成到OA系统

### 方式1：独立服务
- 单独运行这个WebSocket服务器
- OA系统通过WebSocket客户端连接

### 方式2：嵌入现有C服务
- 将WebSocket处理代码集成到现有OA后端
- 复用现有的认证和会话管理

### 示例集成代码
```c
// 在现有OA系统中添加
void handle_ai_chat_request(const char* user_message, const char* user_id) {
    char *api_response = call_doubao_api(user_message, user_id);
    if (api_response) {
        char *ws_response = parse_doubao_response(api_response);
        if (ws_response) {
            // 发送到前端
            send_to_client(ws_response);
            free(ws_response);
        }
        free(api_response);
    }
}
```

## 安全注意事项

1. **API密钥安全**：
   - 不要在源代码中硬编码API密钥
   - 使用环境变量或配置文件

2. **输入验证**：
   - 验证所有WebSocket输入
   - 限制消息长度和频率

3. **HTTPS/WSS**：
   - 生产环境使用SSL/TLS
   - 配置证书和密钥文件

## 监控和日志

建议添加以下监控：
- 连接数统计
- API调用次数
- 响应时间监控
- 错误率统计

```c
// 简单的统计示例
struct server_stats {
    int total_connections;
    int active_connections;
    int total_api_calls;
    int failed_api_calls;
    double avg_response_time;
};
```

## 故障排查

### 常见问题

1. **编译错误**：
   - 检查依赖库是否正确安装
   - 确认gcc版本支持

2. **连接失败**：
   - 检查防火墙设置
   - 确认端口未被占用

3. **API调用失败**：
   - 验证豆包API密钥和模型ID
   - 检查网络连接

4. **内存泄露**：
   - 确保所有malloc的内存都被释放
   - 使用valgrind检测内存问题

### 调试命令
```bash
# 检查端口占用
netstat -tulpn | grep 8080

# 内存检测
valgrind --leak-check=full ./doubao_server

# 系统资源监控
top -p $(pgrep doubao_server)
```